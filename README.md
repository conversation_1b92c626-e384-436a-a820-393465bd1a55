# 如何实现部署
## 一、数据库申请以及初始化
### 1、如何获取数据库建表语句
获取数据库表结构：京东云低代码平台：**设置->关于->数据库建表语句**，如下图所示
![数据库结构获取](https://s3.cn-north-1.jdcloud-oss.com/oss-joybuilder/attachment/20240110/776493544440152066.png)
京东云低代码平台默认有一些内置模型，以下表介绍：  
以下表均为结尾（京东云低代码平台自动带表前缀）  
common_auto_increment：存放自增列  
common_draft：存放临时数据，用于草稿箱的功能  
common_view_plan：用户自定义的列表查询方案信息  
common_view_plan_info：用户自定义的列表查询方案字段信息  
common_field_track：表字段跟踪功能  
conditions_plan：用户自定义标签  


### 2、数据库数据源申请
因独立部署后，依赖数据库，需要先申请数据库，并且进行数据初始化

**京东集团DBS-JED数据库申请链接**：

1）数据库申请链接

https://taishan.jd.com/dbs/dbManage/jed/add

2）初始化数据库表结构

https://taishan.jd.com/dbs/dbManage/dbInfo/online/94/2/33315/ares_monitor/dbOnline

其他数据库请按照运维提供模板进行申请

### 3、数据库数据源配置修改
修改对应环境的数据源配置，如下：
以上请替换成自己申请的数据源信息
![数据源修改](https://s3.cn-north-1.jdcloud-oss.com/oss-joybuilder/attachment/20240110/776494389474967554.png)

### 4、数据库加解密
默认生成的加解密规则是按照京东科技的AKS的解密，如果有明文密码，可修改DruidConfig类，去掉解密过程，如下：
![DruidConfig修改](https://s3.cn-north-1.jdcloud-oss.com/oss-joybuilder/attachment/20240110/776494578772336641.png)

如果是DBS-JED集团的数据库，解密，如下流程：
https://cf.jd.com/pages/viewpage.action?pageId=246568933#javaSpring%E5%BA%94%E7%94%A8%E9%85%8D%E7%BD%AE%E6%96%87%E4%BB%B6%E5%8A%A0%E5%AF%86%E6%93%8D%E4%BD%9C%E8%AF%B4%E6%98%8E-DOIT


**京东科技数据库申请链接**：

1）数据库申请链接

http://online.cbpmgt.com/online/model/wizardGeneralNew/1569258

2）开通数据库与部署示例IP的访问权限（可选择增改查/增删改查）

http://online.cbpmgt.com/online/model/wizardGeneralNew/1569259

3）初始化数据库表结构

https://online.cbpmgt.com/online/model/wizardGeneralNew/1569278


## 二、行云部署
### 1、应用基本信息
应用类型：application_worker  
开发语言：java1.8  
源码地址：关联出码的代码库地址  
### 2、编译构建
构建方式：代码构建  
源码地址：关联出码的代码库地址  
Git信息：关联出码的代码库分支  
编译参数：选择上线的编译参数，低代码默认会生成dev、test、prod三套环境，其余请自定义选择 。 如果自定义环境后，需要在pom添加环境节点： project->profiles-><profile>自定义环境名称</profile>  , 同时修改application.yml文件的配置

config:
    import: classpath:appconfig/<EMAIL>@.yml
工程编码：UTF-8   
编译语言：java-8    
打包类型：maven-3.6.1  
编译命令：mvn clean -U package -Dmaven.test.skip=true  
**编译路径**：xxx (xxx为git仓库应用名)  
**制品路径(抽包地址)**：xxx/xxx-ae/target（如果应用名与artifactId一致，不一致xxx-ae改为实际的artifactId-ae） 

**如下图CRM应用示例**：
![CRM示例](https://s3.cn-north-1.jdcloud-oss.com/oss-joybuilder/attachment/20240110/776494745110704129.png)

## 三、前端如何访问
京东云低代码平台默认生成的应用是前后端一体的应用，后端通过 IndexController来负责前端资源路由，所以一旦后端服务启动之后，会自动发布前端资源，
后端端口默认是8091，可通过在np.jd.com申请域名vip挂载到该示例中的8091端口。

## 四、日志目录
jdos中日志路径：/export/Logs/app/xxx-ae-1.0.0-SNAPSHOT/xxx-ae.log

# 应用框架介绍：

## 一、模块介绍（module）分别：

### 1、ae结尾（应用引擎模块）：

主要编写java代码，yml配置

1)、启动类为：Application.java

2)、前端页面调用的类：IndexController.java 注：尽量避免改动

3)、数据库连接池：DruidConfig.java 注：尽量避免改动

4)、前端请求拦截器：WebMvcConfig.java

### 2、restapi结尾（服务模块）：

在低代码平台配置的模型服务，主要用途是：

1)、无需ide，在平台页面上即可快速实现http接口，并访问数据库

### 3、trigger结尾（触发器模块）：

文档：https://joyspace.jd.com/page/xsFxTpmPrCyMhQyoxaKR

在低代码平台配置的模型触发器 ，主要用途是：

1)、可对模型在新增前，新增后，修改前，修改后，删除前，删除后等步骤，加入代码逻辑

## 二、应用初始引用的包

### 1、app-starter

应用启动引擎，主要用途是：

1)、数据库的数据增删改查。

2)、提供前端驱出的页面所需的http接口。

3)、提供登录拦截，权限拦截

### 2、component-common

应用的公共组件，主要用途是：

1)、提供数据库访问接口，CommonModelService，InnerJdbcTemplate

CommonModelService使用文档：https://joyspace.jd.com/pages/zjTgBTIxUd3ZSxfASpVb

QueryCondition及QuerySort使用文档：https://joyspace.jd.com/pages/2rTkK60XnjntcKDW0QVt

查询参数构建使用文档：https://joyspace.jd.com/pages/0ZfS0CAH7d2jvKKkipPx

DataHelper使用文档：https://joyspace.jd.com/pages/Cu0jo1oj6TjHQiDyWOsO

2)、提供starter包所需的各种接口和实体类。

3)、提供远程服务调用接口

4)、提供文件上传下载接口

### 3、joybuilder-adapter-core-export

应用的适配器，主要用途是：

1)、数据库密码aks加解密（默认是京东科技内部的aks加解密）。

2)、提供应用访问集团sso，集团人资。

3)、提供应用访问工作流的能力。

4)、提供文件上传下载适配能力。

4)、和应用解耦，当应用涉及到部署到公网或者其他公司时，可更换jar，并重写里面的实现。

### 4、joybuilder-permission
应用权限拦截器，主要用途是：

1)、权限拦截，包括页面，菜单，按钮，模型，字段等的权限拦截。

2)、提供权限接口API

### 5、joybuilder-metadata

应用元数据，主要用途是：

1)、可读取应用的resources/metadata中的数据，包括菜单，按钮，页面，领域，模型，字段，模型视图，查询计划，应用基本配置等

2)、供starter包引用。

## 二、应用调用京东中间件

### 1、JSF
创建一个类，例如：

@Configuration
@EnableJsf
public class XXXConfig {

    @JsfConsumer(alias = "123", id = "xxxApi")
    private XXXApi xxxApi;
}

注：项目已引入jsf包

### 2、JMQ

1）可使用项目自带的jmq进行发消息
例如：

@Autowired
private MqService mqService;

public void send(){
String uuid = UUID.randomUUID().toString().replaceAll("-", "");
    mqService.send("topic,"json字符串-正文",uuid,"操作人");
}

2）可自定义
例如：
@Configuration
@ImportResource(locations = {"spring-jmq.xml"})
public class XXXConfigrution {

}

然后按jmq文档，编辑spring-jmq.xml
具体可参考joybuilder-adapter-core jar包里的的配置文件。


