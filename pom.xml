<?xml version="1.0" encoding="UTF-8" standalone="no"?><project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.jd.jdt.joycreator</groupId>
    <artifactId>joycreator</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>joycreator</name>
    <description>[JoyBuilder] LowCode Framework of Java.</description>
    <modules>
        <module>joycreator-ae</module>
        <module>joycreator-trigger</module>
        <module>joycreator-restapi</module>
    </modules>
    <properties>
        <scripts_projectName>${project.artifactId}</scripts_projectName>
        <scripts_packageName>${project.artifactId}-${project.version}</scripts_packageName>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.build.targetEncoding>UTF-8</project.build.targetEncoding>
        <java.version>1.8</java.version>
        <spring.boot.version>2.6.2</spring.boot.version>
        <lombok.version>1.18.20</lombok.version>
        <collections4.version>4.2</collections4.version>
        <lang.version>3.8.1</lang.version>
        <mybatis-plus-boot-starter.version>3.4.1</mybatis-plus-boot-starter.version>
        <mybatis-plus-support.version>2.2.0</mybatis-plus-support.version>
        <mybatis-plus-generator.version>3.4.1</mybatis-plus-generator.version>
        <druid.version>1.1.22</druid.version>
        <mysql.version>8.0.27</mysql.version>
        <log4j.version>2.17.1-jdsec.rc1</log4j.version>
        <jpa.version>2.2</jpa.version>
        <app-starter.version>3.5.1-SNAPSHOT</app-starter.version>
        <joybuilder-adapter-core-export.version>3.4.2-SNAPSHOT</joybuilder-adapter-core-export.version>
        <app4s.permission.version>3.1.5-interface-pms</app4s.permission.version>
        <velocity.version>2.3</velocity.version>
        <aws-s3.version>1.11.319</aws-s3.version>
        <risk.version>1.0.25</risk.version>
        <customerapi-api.version>1.3.5-20240321.091823-1</customerapi-api.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${spring.boot.version}</version>
                <scope>test</scope>
            </dependency>
            <!-- 应用启动器，主要用途是：1，数据库的数据增删改查。2，提供前端驱出的页面所需的http接口。3，提供登录拦截，权限拦截 -->
            <dependency>
                <groupId>com.jd.jdt.app4s.component</groupId>
                <artifactId>app-starter</artifactId>
                <version>${app-starter.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.jd.jdt.joybuilder</groupId>
                        <artifactId>joybuilder-adapter-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 应用适配器，主要用途是：1，数据库密码加解密。2，提供应用访问集团sso，集团人资。3，提供应用访问工作流的能力 -->
            <dependency>
                <groupId>com.jd.jdt.joybuilder</groupId>
                <artifactId>joybuilder-adapter-core-export</artifactId>
                <version>${joybuilder-adapter-core-export.version}</version>
            </dependency>
            <!-- 应用权限拦截器，主要用途是：1，权限拦截，包括页面，菜单，按钮，模型，字段等的权限拦截。2，提供权限接口API -->
            <dependency>
                <groupId>com.jd.jdt.joybuilder</groupId>
                <artifactId>joybuilder-permission</artifactId>
                <version>${app4s.permission.version}</version>
            </dependency>
            <!-- 在低代码平台配置的模型服务，主要用途是：1，无需ide，在平台页面上即可快速实现http接口，并访问数据库-->
            <dependency>
                <groupId>com.jd.jdt.joycreator</groupId>
                <artifactId>joycreator-restapi</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- 在低代码平台配置的模型触发器 ，主要用途是：1，可对模型在新增前，新增后，修改前，修改后，删除前，删除后等步骤，加入代码逻辑-->
            <dependency>
                <groupId>com.jd.jdt.joycreator</groupId>
                <artifactId>joycreator-trigger</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- Lombok是一个Java库，它通过注解来简化Java类的开发。它提供了一组注解，可以自动生成常见的Java类代码，如getter和setter方法、构造函数、equals和hashCode方法等。这样可以减少开发人员编写样板代码的工作量，提高开发效率。-->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <!-- 提供了一组扩展和增强的集合类和工具类，用于简化集合操作和处理 -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${collections4.version}</version>
            </dependency>
            <!-- 字符串操作、数组处理、日期时间操作、随机数生成或异常处理等任务 -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${lang.version}</version>
            </dependency>
            <!-- 确保项目中的依赖项版本与Spring Boot框架的版本保持一致，简化依赖项的配置，提高项目的可维护性和可靠性 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- 连接mysql -->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>
            <!-- 数据库连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <!-- 日志 -->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <!-- 日志 -->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-web</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <!--            <dependency>-->
            <!--                <groupId>org.apache.logging.log4j</groupId>-->
            <!--                <artifactId>log4j-slf4j-impl</artifactId>-->
            <!--                <version>${log4j.version}</version>-->
            <!--            </dependency>-->
            <!-- 日志 -->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-jul</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <!-- mybatis -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis-plus-generator.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-support</artifactId>
                <version>${mybatis-plus-support.version}</version>
            </dependency>
            <!-- JPA，用于生成的实体类，标记其对应的表名，字段名。可被BaseService中进行反射拿到值，再调commonModelService进行CRUD -->
            <dependency>
                <groupId>javax.persistence</groupId>
                <artifactId>javax.persistence-api</artifactId>
                <version>${jpa.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>
            <!--S3 oss存储-->
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-s3</artifactId>
                <version>${aws-s3.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>joda-time</artifactId>
                        <groupId>joda-time</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-openfeign</artifactId>
                <version>3.1.4</version>
            </dependency>
            <dependency>
                <groupId>io.github.openfeign</groupId>
                <artifactId>feign-okhttp</artifactId>
                <version>11.6</version>
            </dependency>
            <dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>pdfbox</artifactId>
                <version>2.0.24</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>4.1.2</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>4.1.2</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-scratchpad</artifactId>
                <version>4.1.2</version>
            </dependency>
            <dependency>
                <groupId>p6spy</groupId>
                <artifactId>p6spy</artifactId>
                <version>3.9.1</version>
            </dependency>
            <!-- 格式化sql，美化sql -->
            <dependency>
                <groupId>com.github.vertical-blank</groupId>
                <artifactId>sql-formatter</artifactId>
                <version>2.0.4</version>
            </dependency>
            <dependency>
                <groupId>com.jdt.toolgood</groupId>
                <artifactId>toolgood-words</artifactId>
                <version>3.1.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.jd.jdt.tob</groupId>
                <artifactId>tob-word</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.jd.jdt.tob</groupId>
                <artifactId>tob-pdf</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.jd.jdt.tob</groupId>
                <artifactId>tob-diff</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>technology.tabula</groupId>
                <artifactId>tabula</artifactId>
                <version>1.0.5</version>
            </dependency>
            <dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>pdfbox</artifactId>
                <version>2.0.24</version>
            </dependency>
            <dependency>
                <groupId>io.github.java-diff-utils</groupId>
                <artifactId>java-diff-utils</artifactId>
                <version>4.11</version>
            </dependency>
            <dependency>
                <groupId>com.hynnet</groupId>
                <artifactId>jacob</artifactId>
                <version>1.18</version>
            </dependency>
            <dependency>
                <groupId>com.jd.jdt.app4s</groupId>
                <artifactId>app4s-permission-embed</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>5.8.30</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>4.5.13</version>
            </dependency>
            <dependency>
                <groupId>com.jd.jdr.aiporta</groupId>
                <artifactId>ai-business-export</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <artifactId>star-message-api</artifactId>
                <groupId>com.jd.jdt.star</groupId>
                <version>1.0.5-SNAPSHOT</version>
            </dependency>
            <!--数据校验-->
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>2.0.1.Final</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>6.0.17.Final</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>com.jd.jdt.risk</groupId>
                <artifactId>risk-export-api</artifactId>
                <version>${risk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.jr.qyjr</groupId>
                <artifactId>customerapi-api</artifactId>
                <version>${customerapi-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.fms.clearing.checkrisk</groupId>
                <artifactId>checkrisk-api</artifactId>
                <version>3.3.9</version>
            </dependency>
            <dependency>
                <groupId>com.jd.autobots</groupId>
                <artifactId>autobots-client</artifactId>
                <version>1.1.5-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.jd.jr</groupId>
                <artifactId>jr-csp-api</artifactId>
                <version>2.5.4-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.jddglobal.ecrm</groupId>
                <artifactId>ecrm-api</artifactId>
                <version>0.1.61-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.jd.icc</groupId>
                <artifactId>icc-data-query-center-api</artifactId>
                <version>2.0.3-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.jd.jdt.tob</groupId>
                <artifactId>tob-sp-doc</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>io.projectreactor</groupId>
                <artifactId>reactor-core</artifactId>
                <version>3.4.12</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tika</groupId>
                <artifactId>tika-core</artifactId>
                <version>2.7.0</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.11.0</version>
            </dependency>
            <dependency>
                <groupId>com.jd</groupId>
                <artifactId>jsf</artifactId>
                <version>1.7.8-HOTFIX-T3</version>
            </dependency>
            <dependency>
                <groupId>com.vladsch.flexmark</groupId>
                <artifactId>flexmark-all</artifactId>
                <version>0.62.2</version>
            </dependency>
            <dependency>
                <groupId>com.vladsch.flexmark</groupId>
                <artifactId>flexmark-html2md-converter</artifactId>
                <version>0.62.2</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>