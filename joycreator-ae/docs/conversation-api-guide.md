# JoyCreator 智能写作助手 API 文档

根据最新的代码改造，我们提供一份完整的 API 文档，包含入参说明、示例以及一个完整的合同写作场景流程。

## 1. 接口概述

### 基本信息
- **接口路径**: `/api/v1/conversation/message`
- **请求方式**: POST
- **响应格式**: text/event-stream (SSE 流式响应)
- **编码**: UTF-8

### 功能说明
该接口支持用户与 JoyCreator 智能写作助手进行多轮对话，系统会自动:
1. 检测用户意图
2. 在需要时提出澄清问题
3. 调用相关工具和资源
4. 生成满足用户需求的写作内容

## 2. 请求参数说明

### 2.1 请求体结构

```json
{
  "role": "user",
  "content": "用户输入的消息内容",
  "timestamp": 1634567890123,
  "intent": {
    // 仅在澄清问题回复时可能需要传入
  },
  "context": {
    // 首次请求可不传，由系统自动创建
    // 多轮对话中需要传入上一轮响应中的 context
  }
}
```

### 2.2 参数详细说明

| 参数名 | 类型 | 是否必须 | 说明 |
| ----- | ---- | ------- | ---- |
| role | String | 是 | 消息发送者角色，固定为 "user" |
| content | String | 是 | 用户输入的消息内容 |
| timestamp | Long | 否 | 消息发送时间戳，毫秒级，不传由服务端生成 |
| intent | Object | 否 | 意图信息，首次请求可不传 |
| context | Object | 否 | 会话上下文，首次请求可不传，后续请求需要传入上一次响应中的 context |

#### 2.2.1 intent 参数结构

```json
{
  "intentType": "写作意图类型",
  "confidence": 0.8,
  "parameters": {
    "key1": "value1",
    "key2": "value2"
  },
  "needsClarification": true,
  "clarificationPoints": ["需要澄清的点1", "需要澄清的点2"],
  "requiredTools": ["需要的工具1", "需要的工具2"]
}
```

#### 2.2.2 context 参数结构

```json
{
  "conversationId": "会话ID",
  "replyToMessageId": "回复消息ID",
  "conversationStage": "会话阶段",
  "toolResults": {
    "key1": "value1",
    "key2": "value2"
  },
  "selectedResources": {
    "key1": "value1",
    "key2": "value2"
  },
  "metadata": {
    "key1": "value1",
    "key2": "value2"
  }
}
```

## 3. 响应说明

### 3.1 响应格式
接口以 SSE (Server-Sent Events) 方式返回流式数据，每个事件为一个 JSON 字符串，表示响应内容的增量更新。

### 3.2 响应示例

```
data: {"newContent":"我是"}
data: {"newContent":" JoyCreator"}
data: {"newContent":" 智能写作助手"}
data: {"newContent":"，很高兴为您服务"}
data: [DONE]
```

### 3.3 结构化响应（当返回澄清问题时）

当系统需要向用户提出澄清问题时，会返回一个完整的 JSON 对象，而不是流式内容：

```json
{
  "role": "assistant",
  "content": "澄清问题内容...",
  "timestamp": 1634567890123,
  "intent": {
    "intentType": "写作意图类型",
    "needsClarification": true,
    "clarificationPoints": ["需要澄清的点1", "需要澄清的点2"]
  },
  "context": {
    "conversationId": "会话ID",
    "conversationStage": "CLARIFICATION"
  }
}
```

## 4. 完整的合同写作场景示例

### 场景描述
用户需要创建一份销售合同，但起初提供的信息不完整，系统需要进行澄清后生成最终的合同文本。

### 4.1 第一轮对话：用户提出初始需求

#### 请求
```json
{
  "role": "user",
  "content": "帮我写一份销售合同"
}
```

#### 响应（完整的 JSON 对象，包含澄清问题）
```json
{
  "role": "assistant",
  "content": "我需要了解更多信息来为您创建一份专业的销售合同：\n\n1. 您是卖方还是买方？这会影响合同中的权利义务描述方式。\n\n2. 销售的具体产品或服务是什么？请提供详细描述，包括数量、规格等信息。\n\n3. 合同金额是多少？您期望的付款方式是什么（例如：一次性付款、分期付款、预付款等）？\n\n4. 您对交付时间和方式有什么要求？\n\n5. 有特殊的违约责任或保密条款需要包含吗？\n\n提供这些信息将帮助我生成一份更符合您实际需求的合同文本。",
  "timestamp": 1634567890123,
  "intent": {
    "intentType": "创建销售合同",
    "confidence": 0.9,
    "parameters": {
      "文档类型": "合同",
      "合同类型": "销售合同"
    },
    "needsClarification": true,
    "clarificationPoints": [
      "合同双方身份",
      "销售产品/服务详情",
      "合同金额与付款方式",
      "交付时间和方式",
      "特殊条款"
    ],
    "requiredTools": ["CONTRACT_TEMPLATE_LOOKUP"]
  },
  "context": {
    "conversationId": "conv-2023-10-15-001",
    "conversationStage": "CLARIFICATION"
  }
}
```

### 4.2 第二轮对话：用户提供澄清信息

#### 请求
```json
{
  "role": "user",
  "content": "我是卖方，销售的是办公家具，包括20套办公桌椅，合同总金额是10万元，买方需要支付30%的预付款，剩余款项在交付后10个工作日内付清。交付时间为合同签订后的15个工作日内，由我方负责运输到买方指定地点。需要包含产品质量保证条款和售后服务条款。",
  "context": {
    "conversationId": "conv-2023-10-15-001",
    "conversationStage": "CLARIFICATION"
  }
}
```

#### 响应（流式返回最终的合同文本）
```
data: {"newContent":"# 办公家具销售合同\n\n"}
data: {"newContent":"## 合同编号：XS-2023-"}
data: {"newContent":"1015-001\n\n"}
data: {"newContent":"## 一、合同双方\n\n"}
data: {"newContent":"甲方（卖方）：________________________\n\n"}
data: {"newContent":"地址：________________________\n\n"}
data: {"newContent":"联系人：________________________ 电话：________________________\n\n"}
data: {"newContent":"乙方（买方）：________________________\n\n"}
... [流式返回合同内容] ...
data: {"newContent":"合同未尽事宜，双方协商解决。\n\n"}
data: {"newContent":"## 九、合同生效\n\n"}
data: {"newContent":"本合同一式两份，甲乙双方各执一份，具有同等法律效力。自双方签字盖章之日起生效。\n\n"}
data: {"newContent":"\n\n甲方（盖章）：                      乙方（盖章）：\n\n"}
data: {"newContent":"代表人（签字）：                    代表人（签字）：\n\n"}
data: {"newContent":"日期：                            日期："}
data: [DONE]
```

## 5. API 调用注意事项

### 5.1 多轮对话处理
- 保留并在后续请求中传入系统返回的 `context` 对象，以维持会话连贯性
- 当收到澄清问题时，用户回答应包含原始 `context` 信息

### 5.2 流式响应处理
- 客户端需要支持处理 SSE 格式的流式响应
- 通过累积 `newContent` 字段内容来构建完整响应
- `data: [DONE]` 表示流式响应结束

### 5.3 错误处理
- HTTP 状态码非 200 时表示请求失败
- 请求失败时会返回错误信息 JSON，包含 `code` 和 `message` 字段

## 6. 常见问题

### 6.1 如何处理复杂的写作需求？
对于复杂的写作需求，建议在初始请求中提供尽可能详细的信息，减少澄清问题的轮次。

### 6.2 如何使用模板？
系统会根据用户意图自动查找并应用合适的模板。如需使用特定模板，可在请求中的 `context.selectedResources` 中指定模板信息。

### 6.3 支持哪些写作类型？
目前系统支持各类商务文档、合同、营销文案、产品说明等写作需求，详细支持列表可咨询技术支持。

## 7. 集成建议

### 7.1 前端实现建议
1. 使用支持 SSE 的库处理流式响应，如 JavaScript 的 EventSource
2. 实现打字机效果展示流式返回的内容
3. 针对澄清问题设计用户友好的表单或引导式界面

### 7.2 示例代码（JavaScript）

```javascript
// 发送初始请求
async function sendMessage(content, context = null) {
  const body = {
    role: "user",
    content: content
  };
  
  if (context) {
    body.context = context;
  }
  
  const response = await fetch('/api/v1/conversation/message', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(body)
  });
  
  // 检查是否是流式响应
  const contentType = response.headers.get('content-type');
  if (contentType.includes('text/event-stream')) {
    // 处理流式响应
    handleStreamResponse(response);
  } else {
    // 处理JSON响应（澄清问题）
    const jsonResponse = await response.json();
    handleClarificationResponse(jsonResponse);
  }
}

// 处理流式响应
function handleStreamResponse(response) {
  const reader = response.body.getReader();
  const decoder = new TextDecoder();
  let buffer = '';
  let result = '';
  
  reader.read().then(function processText({ done, value }) {
    if (done) {
      console.log('Stream complete');
      return;
    }
    
    buffer += decoder.decode(value, { stream: true });
    const lines = buffer.split('\n');
    buffer = lines.pop();
    
    for (const line of lines) {
      if (line.startsWith('data:')) {
        const data = line.slice(5).trim();
        if (data === '[DONE]') {
          // 流结束
          displayFinalResult(result);
        } else {
          try {
            const parsed = JSON.parse(data);
            if (parsed.newContent) {
              result += parsed.newContent;
              updateProgressiveDisplay(result);
            }
          } catch (e) {
            console.error('Error parsing SSE data', e);
          }
        }
      }
    }
    
    return reader.read().then(processText);
  });
}

// 处理澄清问题响应
function handleClarificationResponse(response) {
  // 显示澄清问题
  displayClarificationQuestions(response.content);
  
  // 保存会话上下文，用于用户回答后的后续请求
  saveContext(response.context);
}
```

---

以上文档提供了 JoyCreator 智能写作助手 API 的详细说明，包括请求参数、响应格式以及一个完整的合同写作场景示例。前端开发人员可以根据此文档进行系统集成，实现与用户的自然交互和专业内容生成功能。 