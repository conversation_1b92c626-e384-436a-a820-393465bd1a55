# 共有8个级别，按照从低到高为：ALL < TRACE < DEBUG < INFO < WARN < ERROR < FATAL < OFF。
# status：用于设置log4j2自身内部的信息输出，可以不设置
# monitorInterval：log4j2监测配置文件的时间间隔，如果文件被修改，则重新加载
Configuration:
  status: 'INFO'
  monitorInterval: 30

  Properties: # 定义全局变量
    Property: # 缺省配置（用于开发环境）。其他环境需要在VM参数中指定，如下：
    #测试：-Dlog.level.console=warn -Dlog.level.xjj=trace
    #生产：-Dlog.level.console=warn -Dlog.level.xjj=info
    - name: log.level.console
      value: info
    - name: project.name
      value: '@project.artifactId@'
    - name: log.path
      value: '/export/Logs/app/@scripts_packageName@'
    - name: log.pattern
      value: "%clr{%d{yyyy-MM-dd HH:mm:ss.SSS}}:%clr{[TId :%T]}{blue} %clr{%-5p}{cyan} %clr{${sys:PID}}{magenta} %clr{---} %clr{%c.%M:%L}{cyan} %clr{:}{faint} %m%n%xwEx"
  Appenders:
    Console:  #输出到控制台
      name: CONSOLE
      target: SYSTEM_OUT
      PatternLayout:
        pattern: ${log.pattern}
    RollingFile:  #输出到滚动文件
      #   运行日志
      - name: ROLLING_FILE
        fileName: ${log.path}/${project.name}.log
        filePattern: "${log.path}/${project.name}_detail.log%d{yyyy-MM-dd}"
        PatternLayout:
          pattern: ${log.pattern}
        Policies:
          TimeBasedTriggeringPolicy:  # 按天分类
            modulate: true
            interval: 1
        DefaultRolloverStrategy:     # 文件最多100个
          max: 100

      #   错误日志
      - name: EXCEPTION_ROLLING_FILE
        ignoreExceptions: false
        fileName: ${log.path}/${project.name}_error.log
        filePattern: "${log.path}/${project.name}_error.log%d{yyyy-MM-dd}"
        ThresholdFilter:
          level: error
          onMatch: ACCEPT
          onMismatch: DENY
        PatternLayout:
          pattern: ${log.pattern}
        Policies:
          TimeBasedTriggeringPolicy:  # 按天分类
            modulate: true
            interval: 1
        DefaultRolloverStrategy:     # 文件最多100个
          max: 100
  Loggers:
    Root:
      level: info
      AppenderRef:
        - ref: CONSOLE
        - ref: ROLLING_FILE
        - ref: EXCEPTION_ROLLING_FILE
    Logger:
      - name: org.springframework.web
        level: info
        additivity: false
        AppenderRef:
          - ref: CONSOLE
          - ref: ROLLING_FILE
        Logger:
          - name: com.baomidou.mybatisplus
            level: debug