<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.jdt.joycreator.ae.dao.mapper.ChatHistoryDetailTemplateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jd.jdt.joycreator.ae.entity.ChatHistoryDetailTemplate">
        <result column="id" property="id" />
        <result column="name" property="name" />
        <result column="owner_id" property="ownerId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="created_user" property="createdUser" />
        <result column="modified_user" property="modifiedUser" />
        <result column="yn" property="yn" />
        <result column="chat_history_detail_id" property="chatHistoryDetailId" />
        <result column="template_id" property="templateId" />
        <result column="selected" property="selected" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        name,
        owner_id,
        create_time,
        update_time,
        created_user,
        modified_user,
        yn,
        chat_history_detail_id, template_id, selected
    </sql>

</mapper>
