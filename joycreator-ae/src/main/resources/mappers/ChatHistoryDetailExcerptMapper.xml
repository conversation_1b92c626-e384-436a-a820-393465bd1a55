<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.jdt.joycreator.ae.dao.mapper.ChatHistoryDetailExcerptMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jd.jdt.joycreator.ae.entity.ChatHistoryDetailExcerpt">
        <result column="id" property="id" />
        <result column="name" property="name" />
        <result column="owner_id" property="ownerId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="created_user" property="createdUser" />
        <result column="modified_user" property="modifiedUser" />
        <result column="yn" property="yn" />
        <result column="chat_history_detail_id" property="chatHistoryDetailId" />
        <result column="file_id" property="fileId" />
        <result column="msg_type" property="msgType" />
        <result column="document_content" property="documentContent" />
        <result column="text_content" property="textContent" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        name,
        owner_id,
        create_time,
        update_time,
        created_user,
        modified_user,
        yn,
        chat_history_detail_id, file_id, msg_type, document_content, text_content
    </sql>

</mapper>
