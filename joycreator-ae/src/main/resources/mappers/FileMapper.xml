<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.jdt.joycreator.ae.dao.mapper.FileMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jd.jdt.joycreator.ae.entity.File">
        <result column="id" property="id" />
        <result column="name" property="name" />
        <result column="owner_id" property="ownerId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="created_user" property="createdUser" />
        <result column="modified_user" property="modifiedUser" />
        <result column="yn" property="yn" />
        <result column="file_suffix" property="fileSuffix" />
        <result column="file_type" property="fileType" />
        <result column="file_size" property="fileSize" />
        <result column="wan_url" property="wanUrl" />
        <result column="lan_url" property="lanUrl" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        name,
        owner_id,
        create_time,
        update_time,
        created_user,
        modified_user,
        yn,
        file_suffix, file_type, file_size, wan_url, lan_url
    </sql>

</mapper>
