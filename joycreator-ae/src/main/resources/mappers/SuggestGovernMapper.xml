<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.jdt.joycreator.ae.dao.mapper.SuggestGovernMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jd.jdt.joycreator.ae.entity.SuggestGovern">
        <result column="id" property="id" />
        <result column="name" property="name" />
        <result column="owner_id" property="ownerId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="created_user" property="createdUser" />
        <result column="modified_user" property="modifiedUser" />
        <result column="yn" property="yn" />
        <result column="category" property="category" />
        <result column="icon" property="icon" />
        <result column="is_enabled" property="isEnabled" />
        <result column="priority" property="priority" />
        <result column="preset_prompt" property="presetPrompt" />
        <result column="background" property="background" />
        <result column="recommendation_category_enum" property="recommendationCategoryEnum" />
        <result column="recommendation_description" property="recommendationDescription" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        name,
        owner_id,
        create_time,
        update_time,
        created_user,
        modified_user,
        yn,
        category, icon, is_enabled, priority, preset_prompt, background, recommendation_category_enum, recommendation_description
    </sql>

</mapper>
