<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.jdt.joycreator.ae.dao.mapper.PromptWordsConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jd.jdt.joycreator.ae.entity.PromptWordsConfig">
        <result column="id" property="id" />
        <result column="name" property="name" />
        <result column="owner_id" property="ownerId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="created_user" property="createdUser" />
        <result column="modified_user" property="modifiedUser" />
        <result column="yn" property="yn" />
        <result column="prompt_word_code" property="promptWordCode" />
        <result column="model" property="model" />
        <result column="prompt_source" property="promptSource" />
        <result column="prompt_word" property="promptWord" />
        <result column="description" property="description" />
        <result column="temperature" property="temperature" />
        <result column="top_k" property="topK" />
        <result column="max_tokens" property="maxTokens" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        name,
        owner_id,
        create_time,
        update_time,
        created_user,
        modified_user,
        yn,
        prompt_word_code, model, prompt_source, prompt_word, description, temperature, top_k, max_tokens
    </sql>

</mapper>
