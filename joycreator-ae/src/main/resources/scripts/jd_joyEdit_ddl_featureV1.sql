CREATE TABLE `t_2943_ipms_user_role` (
 `id` bigint(20) NOT NULL AUTO_INCREMENT  COMMENT '主键id-主键id',
 `name` varchar(100)  COMMENT '名称-名称',
 `owner_id` varchar(64)  DEFAULT NULL  COMMENT '归属人-归属人（erp）',
 `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '创建时间-创建时间',
 `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP  COMMENT '更新时间-更新时间',
 `created_user` varchar(100)  DEFAULT NULL  COMMENT '创建人-创建人',
 `modified_user` varchar(100)  DEFAULT NULL  COMMENT '修改人-修改人',
 `yn` smallint(6) DEFAULT '1'  COMMENT '是否有效-是否有效(1:有效,0:无效)',
 `role` varchar(128)  COMMENT '角色',
 `user` varchar(64)  COMMENT '用户',
 PRIMARY KEY (`id`),
 KEY `idx_user_role_user` (`user`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='用户与角色';
CREATE TABLE `t_2943_ipms_user` (
 `id` bigint(20) NOT NULL AUTO_INCREMENT  COMMENT '主键id-主键id',
 `name` varchar(100)  COMMENT '名称-名称',
 `owner_id` varchar(64)  DEFAULT NULL  COMMENT '归属人-归属人（erp）',
 `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '创建时间-创建时间',
 `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP  COMMENT '更新时间-更新时间',
 `created_user` varchar(100)  DEFAULT NULL  COMMENT '创建人-创建人',
 `modified_user` varchar(100)  DEFAULT NULL  COMMENT '修改人-修改人',
 `yn` smallint(6) DEFAULT '1'  COMMENT '是否有效-是否有效(1:有效,0:无效)',
 `vn` int(11) DEFAULT '0'  COMMENT '版本号-版本号',
 `login_name` varchar(64)  COMMENT '用户账号-用户账号',
 `phone` varchar(64)  COMMENT '电话-电话',
 `fax` varchar(64)  COMMENT '传真-传真',
 `mobile` varchar(64)  COMMENT '手机号-手机号',
 `email` varchar(64)  COMMENT '电子邮件-电子邮件',
 `title` varchar(999)  COMMENT '岗位名称-岗位名称',
 `titlecode` varchar(64)  COMMENT '岗位编码-岗位编码',
 `pwdd` varchar(64)  COMMENT '密码-密码',
 `office_organization` varchar(32)  COMMENT '行政组织 -所属组织机构 ',
 `isusing` tinyint(1)  COMMENT '是否启用-是否启用',
 PRIMARY KEY (`id`),
 KEY `idx_user_login_name` (`login_name`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='用户-用户';
CREATE TABLE `t_2943_ipms_role` (
 `id` bigint(20) NOT NULL AUTO_INCREMENT  COMMENT '主键id-主键id',
 `name` varchar(100)  COMMENT '名称-名称',
 `owner_id` varchar(64)  DEFAULT NULL  COMMENT '归属人-归属人（erp）',
 `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '创建时间-创建时间',
 `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP  COMMENT '更新时间-更新时间',
 `created_user` varchar(100)  DEFAULT NULL  COMMENT '创建人-创建人',
 `modified_user` varchar(100)  DEFAULT NULL  COMMENT '修改人-修改人',
 `yn` smallint(6) DEFAULT '1'  COMMENT '是否有效-是否有效(1:有效,0:无效)',
 `role_code` varchar(255)  COMMENT '角色编码',
 `role_name` varchar(100)  COMMENT '角色名称',
 `role_type` varchar(64)  COMMENT '角色类型',
 `description` varchar(64)  COMMENT '描述',
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='角色';
CREATE TABLE `t_2943_ipms_business_organization` (
 `id` bigint(20) NOT NULL AUTO_INCREMENT  COMMENT '主键id-主键id',
 `name` varchar(100)  COMMENT '名称-名称',
 `owner_id` varchar(64)  DEFAULT NULL  COMMENT '归属人-归属人（erp）',
 `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '创建时间-创建时间',
 `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP  COMMENT '更新时间-更新时间',
 `created_user` varchar(100)  DEFAULT NULL  COMMENT '创建人-创建人',
 `modified_user` varchar(100)  DEFAULT NULL  COMMENT '修改人-修改人',
 `yn` smallint(6) DEFAULT '1'  COMMENT '是否有效-是否有效(1:有效,0:无效)',
 `parent_id` varchar(32)  COMMENT '父级-父级ID',
 `order` bigint(20)  COMMENT '排序字段-排序字段',
 `leaf` tinyint(1)  COMMENT '是否叶子节点-是否叶子节点',
 `full_path_name` varchar(256)  COMMENT '组织全路径名称',
 `full_path_code` varchar(256)  COMMENT '组织全路径编号',
 `remark` varchar(256)  COMMENT '备注',
 `code` varchar(256)  COMMENT '组织机构编码',
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='业务组织';
CREATE TABLE `t_2943_ipms_user_business_organization` (
 `id` bigint(20) NOT NULL AUTO_INCREMENT  COMMENT '主键id-主键id',
 `name` varchar(100)  COMMENT '名称-名称',
 `owner_id` varchar(64)  DEFAULT NULL  COMMENT '归属人-归属人（erp）',
 `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '创建时间-创建时间',
 `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP  COMMENT '更新时间-更新时间',
 `created_user` varchar(100)  DEFAULT NULL  COMMENT '创建人-创建人',
 `modified_user` varchar(100)  DEFAULT NULL  COMMENT '修改人-修改人',
 `yn` smallint(6) DEFAULT '1'  COMMENT '是否有效-是否有效(1:有效,0:无效)',
 `business_organization` varchar(256)  COMMENT '业务组织',
 `user` varchar(64)  COMMENT '用户',
 PRIMARY KEY (`id`),
 KEY `idx_user_business_user` (`user`),
 KEY `idx_user_business_org` (`business_organization`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='用户与业务组织';
CREATE TABLE `t_2943_ipms_office_organization` (
 `id` bigint(20) NOT NULL AUTO_INCREMENT  COMMENT '主键id-主键id',
 `name` varchar(100)  COMMENT '名称-名称',
 `owner_id` varchar(64)  DEFAULT NULL  COMMENT '归属人-归属人（erp）',
 `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '创建时间-创建时间',
 `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP  COMMENT '更新时间-更新时间',
 `created_user` varchar(100)  DEFAULT NULL  COMMENT '创建人-创建人',
 `modified_user` varchar(100)  DEFAULT NULL  COMMENT '修改人-修改人',
 `yn` smallint(6) DEFAULT '1'  COMMENT '是否有效-是否有效(1:有效,0:无效)',
 `parent_id` varchar(32)  COMMENT '父级-父级ID',
 `order` bigint(20)  COMMENT '排序字段-排序字段',
 `leaf` tinyint(1)  COMMENT '是否叶子节点-是否叶子节点',
 `full_path_name` varchar(256)  COMMENT '组织全路径名称',
 `full_path_code` varchar(256)  COMMENT '组织全路径编号',
 `remark` varchar(256)  COMMENT '备注',
 `code` varchar(256)  COMMENT '组织机构编码',
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='行政组织';
CREATE TABLE `t_2943_ceshi` (
 `id` bigint(20) NOT NULL AUTO_INCREMENT  COMMENT '主键id-主键id',
 `name` varchar(100)  COMMENT '名称-名称',
 `owner_id` varchar(64)  DEFAULT NULL  COMMENT '归属人-归属人（erp）',
 `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '创建时间-创建时间',
 `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP  COMMENT '更新时间-更新时间',
 `created_user` varchar(100)  DEFAULT NULL  COMMENT '创建人-创建人',
 `modified_user` varchar(100)  DEFAULT NULL  COMMENT '修改人-修改人',
 `yn` smallint(6) DEFAULT '1'  COMMENT '是否有效-是否有效(1:有效,0:无效)',
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='ceshi';
CREATE TABLE `t_2943_common_draft` (
 `id` bigint(20) NOT NULL AUTO_INCREMENT  COMMENT '主键id-主键id',
 `name` varchar(256)  COMMENT '名称-名称',
 `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '创建时间-创建时间',
 `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP  COMMENT '更新时间-更新时间',
 `created_user` varchar(100)  DEFAULT NULL  COMMENT '创建人-创建人',
 `modified_user` varchar(100)  DEFAULT NULL  COMMENT '修改人-修改人',
 `yn` smallint(6) DEFAULT '1'  COMMENT '是否有效-是否有效(1:有效,0:无效)',
 `vn` int(11) DEFAULT '0'  COMMENT '版本号-版本号',
 `data_info` text  COMMENT '暂存的数据',
 `model_name` varchar(256)  COMMENT '模型',
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT=' 暂存箱';
CREATE TABLE `t_2943_common_field_track` (
 `id` bigint(20) NOT NULL AUTO_INCREMENT  COMMENT '主键id-主键id',
 `name` varchar(100)  COMMENT '名称-名称',
 `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '创建时间-创建时间',
 `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP  COMMENT '更新时间-更新时间',
 `created_user` varchar(100)  DEFAULT NULL  COMMENT '创建人-创建人',
 `modified_user` varchar(100)  DEFAULT NULL  COMMENT '修改人-修改人',
 `yn` smallint(6) DEFAULT '1'  COMMENT '是否有效-是否有效(1:有效,0:无效)',
 `vn` int(11) DEFAULT '0'  COMMENT '版本号-版本号',
 `model_name` varchar(64)  COMMENT '模型名称',
 `track_type` int(11)  COMMENT '操作类型',
 `data_id` varchar(64)  COMMENT '数据id',
 `data_info` text  COMMENT '数据信息',
 PRIMARY KEY (`id`),
 KEY `idx_model_name` (`model_name`),
 KEY `idx_data_id` (`data_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='字段跟踪';
CREATE TABLE `t_2943_common_model_config_list_view` (
 `id` bigint(20) NOT NULL AUTO_INCREMENT  COMMENT '主键id-主键id',
 `name` varchar(256)  COMMENT '名称-名称',
 `owner_id` varchar(64)  DEFAULT NULL  COMMENT '归属人-归属人（erp）',
 `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '创建时间-创建时间',
 `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP  COMMENT '更新时间-更新时间',
 `created_user` varchar(100)  DEFAULT NULL  COMMENT '创建人-创建人',
 `modified_user` varchar(100)  DEFAULT NULL  COMMENT '修改人-修改人',
 `yn` smallint(6) DEFAULT '1'  COMMENT '是否有效-是否有效(1:有效,0:无效)',
 `modelName` varchar(256)  COMMENT '模型名称-模型名称',
 `pageKey` varchar(256)  COMMENT '页面标识-页面标识',
 `editorViewName` varchar(256)  COMMENT '视图名称-视图名称',
 `defaultState` tinyint(1)  COMMENT '是否默认-是否默认',
 `showIndex` int(11)  COMMENT '排序-排序',
 `viewData` mediumtext  COMMENT '视图数据-视图数据',
 `openCombinationRule` tinyint(1)  COMMENT '高级查询启用状态-高级查询启用状态',
 `combinationRule` varchar(256)  COMMENT '高级查询：组合规则-高级查询：组合规则',
 `displayDensity` varchar(256)  COMMENT '展示密度',
 `freezeFirstColumn` tinyint(1)  COMMENT '冻结首列',
 `freezeOperateColumn` tinyint(1)  COMMENT '冻结操作列',
 `editorViewCode` varchar(256)  COMMENT '视图标识-视图标识',
 PRIMARY KEY (`id`),
 KEY `idx_editorViewCode` (`editorViewCode`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='应用运行时列表视图';
CREATE TABLE `t_2943_common_auto_increment` (
 `id` bigint(20) NOT NULL AUTO_INCREMENT  COMMENT '主键id-主键id',
 `name` varchar(256)  COMMENT '名称-名称',
 `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '创建时间-创建时间',
 `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP  COMMENT '更新时间-更新时间',
 `created_user` varchar(100)  DEFAULT NULL  COMMENT '创建人-创建人',
 `modified_user` varchar(100)  DEFAULT NULL  COMMENT '修改人-修改人',
 `yn` smallint(6) DEFAULT '1'  COMMENT '是否有效-是否有效(1:有效,0:无效)',
 `vn` int(11) DEFAULT '0'  COMMENT '版本号-版本号',
 `field_key` varchar(256)  COMMENT '需要自增的字段',
 `start_num` bigint(20)  COMMENT '起始值',
 `max_num` bigint(20)  COMMENT '最大值',
 `current_num` bigint(20)  COMMENT '当前值',
 PRIMARY KEY (`id`),
 UNIQUE KEY `uniq_field_key` (`field_key`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='自增字段表';

CREATE TABLE `t_2943_template_library` (
 `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id-主键id',
 `name` varchar(100)  COMMENT '名称-名称',
 `owner_id` varchar(64)  DEFAULT NULL  COMMENT '归属人-归属人（erp）',
 `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '创建时间-创建时间',
 `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP  COMMENT '更新时间-更新时间',
 `created_user` varchar(100)  DEFAULT NULL  COMMENT '创建人-创建人',
 `modified_user` varchar(100)  DEFAULT NULL  COMMENT '修改人-修改人',
 `yn` smallint(6) DEFAULT '1'  COMMENT '是否有效-是否有效(1:有效,0:无效)',
 `tag` varchar(256)  COMMENT '标签',
 `content` LONGTEXT  COMMENT '内容',
 `description` text  COMMENT '描述',
 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='模板库';

CREATE TABLE `t_2943_chat_history_detail` (
 `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id-主键id',
 `name` varchar(100)  COMMENT '名称-名称',
 `owner_id` varchar(64)  DEFAULT NULL  COMMENT '归属人-归属人（erp）',
 `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '创建时间-创建时间',
 `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP  COMMENT '更新时间-更新时间',
 `created_user` varchar(100)  DEFAULT NULL  COMMENT '创建人-创建人',
 `modified_user` varchar(100)  DEFAULT NULL  COMMENT '修改人-修改人',
 `yn` smallint(6) DEFAULT '1'  COMMENT '是否有效-是否有效(1:有效,0:无效)',
 `chat_history_id` bigint(20)  COMMENT '聊天记录ID',
 `chat_role` varchar(64)  COMMENT '角色',
 `chat_content` mediumtext  COMMENT '聊天内容',

 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='聊天记录明细';


CREATE TABLE `t_2943_chat_history` (
 `id` bigint(20) NOT NULL AUTO_INCREMENT  COMMENT '主键id-主键id',
 `name` varchar(100)  COMMENT '名称-名称',
 `owner_id` varchar(64)  DEFAULT NULL  COMMENT '归属人-归属人（erp）',
 `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '创建时间-创建时间',
 `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP  COMMENT '更新时间-更新时间',
 `created_user` varchar(100)  DEFAULT NULL  COMMENT '创建人-创建人',
 `modified_user` varchar(100)  DEFAULT NULL  COMMENT '修改人-修改人',
 `yn` smallint(6) DEFAULT '1'  COMMENT '是否有效-是否有效(1:有效,0:无效)',
 `erp` varchar(256)  COMMENT '发起人erp',
 `session_id` varchar(64)  COMMENT '会话ID',
 `collection` tinyint(1)  COMMENT '收藏';
 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='聊天记录';


CREATE TABLE `t_2943_chat_history_detail_template` (
 `id` bigint(20) NOT NULL AUTO_INCREMENT  COMMENT '主键id-主键id',
 `name` varchar(100)  COMMENT '名称-名称',
 `owner_id` varchar(64)  DEFAULT NULL  COMMENT '归属人-归属人（erp）',
 `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '创建时间-创建时间',
 `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP  COMMENT '更新时间-更新时间',
 `created_user` varchar(100)  DEFAULT NULL  COMMENT '创建人-创建人',
 `modified_user` varchar(100)  DEFAULT NULL  COMMENT '修改人-修改人',
 `yn` smallint(6) DEFAULT '1'  COMMENT '是否有效-是否有效(1:有效,0:无效)',
 `chat_history_detail_id` bigint(20)  COMMENT '聊天记录明细Id',
 `template_id` bigint(20)  COMMENT '模版Id',
 `selected` varchar(64)  COMMENT '是否选中',
 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='聊天记录明细模版';


CREATE TABLE `t_2943_edit_document` (
 `id` bigint(20) NOT NULL AUTO_INCREMENT  COMMENT '主键id-主键id',
 `name` varchar(100)  COMMENT '名称-名称',
 `owner_id` varchar(64)  DEFAULT NULL  COMMENT '归属人-归属人（erp）',
 `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '创建时间-创建时间',
 `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP  COMMENT '更新时间-更新时间',
 `created_user` varchar(100)  DEFAULT NULL  COMMENT '创建人-创建人',
 `modified_user` varchar(100)  DEFAULT NULL  COMMENT '修改人-修改人',
 `yn` smallint(6) DEFAULT '1'  COMMENT '是否有效-是否有效(1:有效,0:无效)',
 `document_content` LONGTEXT  COMMENT '文档内容',
 `chat_history_id` bigint(20)  COMMENT '聊天记录id',
 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='编辑器文档数据';



alter table t_2943_chat_history_detail
 add COLUMN `business_no` varchar(256)  COMMENT '业务唯一编号';


alter table t_2943_chat_history_detail
 add COLUMN `selected` varchar(64)  COMMENT '是否选中';





CREATE TABLE `t_2943_dict` (
 `id` bigint(20) NOT NULL AUTO_INCREMENT  COMMENT '主键id-主键id',
 `name` varchar(100)  COMMENT '名称-名称',
 `owner_id` varchar(64)  DEFAULT NULL  COMMENT '归属人-归属人（erp）',
 `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '创建时间-创建时间',
 `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP  COMMENT '更新时间-更新时间',
 `created_user` varchar(100)  DEFAULT NULL  COMMENT '创建人-创建人',
 `modified_user` varchar(100)  DEFAULT NULL  COMMENT '修改人-修改人',
 `yn` smallint(6) DEFAULT '1'  COMMENT '是否有效-是否有效(1:有效,0:无效)',
 `dict_code` varchar(256)  COMMENT '字典编码',
 `enabled` varchar(64)  COMMENT '是否启用',
 `dict_desc` text  COMMENT '字典描述',
 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='字典-字典配置';

CREATE TABLE `t_2943_dict_dtl` (
 `id` bigint(20) NOT NULL AUTO_INCREMENT  COMMENT '主键id-主键id',
 `name` varchar(100)  COMMENT '名称-名称',
 `owner_id` varchar(64)  DEFAULT NULL  COMMENT '归属人-归属人（erp）',
 `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '创建时间-创建时间',
 `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP  COMMENT '更新时间-更新时间',
 `created_user` varchar(100)  DEFAULT NULL  COMMENT '创建人-创建人',
 `modified_user` varchar(100)  DEFAULT NULL  COMMENT '修改人-修改人',
 `yn` smallint(6) DEFAULT '1'  COMMENT '是否有效-是否有效(1:有效,0:无效)',
 `dict_id` varchar(256)  COMMENT '字典id',
 `value` text  COMMENT '字典值',
 `sort` int(11)  COMMENT '字典排序',
 `desc` text  COMMENT '字典描述',
 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='字典明细';


alter table t_2943_edit_document
 add COLUMN `text_content` LONGTEXT  COMMENT '文档文本内容';

CREATE TABLE `t_2943_file` (
 `id` bigint(20) NOT NULL AUTO_INCREMENT  COMMENT '主键id-主键id',
 `name` varchar(100)  COMMENT '名称-名称',
 `owner_id` varchar(64)  DEFAULT NULL  COMMENT '归属人-归属人（erp）',
 `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '创建时间-创建时间',
 `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP  COMMENT '更新时间-更新时间',
 `created_user` varchar(100)  DEFAULT NULL  COMMENT '创建人-创建人',
 `modified_user` varchar(100)  DEFAULT NULL  COMMENT '修改人-修改人',
 `yn` smallint(6) DEFAULT '1'  COMMENT '是否有效-是否有效(1:有效,0:无效)',
 `file_suffix` varchar(128)  COMMENT '文件后缀',
 `file_type` varchar(128)  COMMENT '文件类型',
 `file_size` decimal(10,2)  COMMENT '文件大小',
 `wan_url` text  COMMENT '外网链接',
 `lan_url` text  COMMENT '内网链接',
 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='文件-业务附件';



CREATE TABLE `t_2943_suggest_govern` (
 `id` bigint(20) NOT NULL AUTO_INCREMENT  COMMENT '主键id-主键id',
 `name` varchar(100)  COMMENT '推荐主题-名称',
 `owner_id` varchar(64)  DEFAULT NULL  COMMENT '归属人-归属人（erp）',
 `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '创建时间-创建时间',
 `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP  COMMENT '更新时间-更新时间',
 `created_user` varchar(100)  DEFAULT NULL  COMMENT '创建人-创建人',
 `modified_user` varchar(100)  DEFAULT NULL  COMMENT '修改人-修改人',
 `yn` smallint(6) DEFAULT '1'  COMMENT '是否有效-是否有效(1:有效,0:无效)',
 `category` varchar(128)  COMMENT '推荐分类',
 `icon` varchar(256)  COMMENT '推荐图标',
 `is_enabled` varchar(64)  COMMENT '是否启用',
 `priority` decimal(10,2)  COMMENT '推荐优先级',
 `preset_prompt` text  COMMENT '预置提示词',
 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='推荐管理-推荐管理配置';



alter table t_2943_suggest_govern
 add COLUMN `background` varchar(128) COMMENT '图标背景色',
 add COLUMN `recommendation_category_enum` varchar(256) COMMENT '推荐分类枚举',
 add COLUMN `recommendation_description` text COMMENT '推荐描述';

alter table t_2943_chat_history_detail
 add COLUMN `file_id` bigint(20) COMMENT '附件';


 CREATE TABLE `t_2943_chat_history_detail_excerpt` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT  COMMENT '主键id-主键id',
  `name` varchar(100)  COMMENT '名称-名称',
  `owner_id` varchar(64)  DEFAULT NULL  COMMENT '归属人-归属人（erp）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '创建时间-创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP  COMMENT '更新时间-更新时间',
  `created_user` varchar(100)  DEFAULT NULL  COMMENT '创建人-创建人',
  `modified_user` varchar(100)  DEFAULT NULL  COMMENT '修改人-修改人',
  `yn` smallint(6) DEFAULT '1'  COMMENT '是否有效-是否有效(1:有效,0:无效)',
  `chat_history_detail_id` bigint(20)  COMMENT '聊天记录明细',
  `file_id` bigint(20)  COMMENT '附件',
  `msg_type` varchar(128)  COMMENT '引用类型',
  `document_content` LONGTEXT  COMMENT '引用文档内容',
  `text_content` LONGTEXT  COMMENT '引用文档纯文本内容',
  PRIMARY KEY (`id`)
 ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='聊天记录明细引用';

CREATE TABLE `t_2943_prompt_words_config` (
 `id` bigint(20) NOT NULL AUTO_INCREMENT  COMMENT '主键id-主键id',
 `name` varchar(100)  COMMENT '名称-名称',
 `owner_id` varchar(64)  DEFAULT NULL  COMMENT '归属人-归属人（erp）',
 `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP  COMMENT '创建时间-创建时间',
 `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP  COMMENT '更新时间-更新时间',
 `created_user` varchar(100)  DEFAULT NULL  COMMENT '创建人-创建人',
 `modified_user` varchar(100)  DEFAULT NULL  COMMENT '修改人-修改人',
 `yn` smallint(6) DEFAULT '1'  COMMENT '是否有效-是否有效(1:有效,0:无效)',
 `prompt_word_code` varchar(128)  COMMENT '提示词编码',
 `model` varchar(256)  COMMENT '模型',
 `temperature` decimal(10,2)  COMMENT '采样温度',
 `top_k` decimal(10,2)  COMMENT '核取样',
 `max_tokens` decimal(10,2)  COMMENT '输出最大token数',
 `prompt_word` text  COMMENT '提示词',
 `description` text  COMMENT '描述/说明',
 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='提示词管理';

alter table t_2943_prompt_words_config
 add COLUMN `temperature` decimal(10,2)  COMMENT '采样温度',
 add COLUMN `top_k` decimal(10,2)  COMMENT '核取样',
 add COLUMN `max_tokens` decimal(10,2)  COMMENT '输出最大token数';


alter table t_2943_prompt_words_config
 add COLUMN `prompt_source` varchar(128)  COMMENT '提示词来源';