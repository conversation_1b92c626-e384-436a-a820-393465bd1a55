"use strict";(self["webpackChunkjoy_creator"]=self["webpackChunkjoy_creator"]||[]).push([[683],{639:function(t,e,a){a.r(e),a.d(e,{default:function(){return m}});var s=function(){var t=this,e=t._self._c;return e("div",[t.vueDsl?e("vue-render",{staticClass:"page-403-box",attrs:{dsl:t.vueDsl}}):e("div",{staticClass:"empty-menu-box"},[e("div",{staticClass:"word-error"},[e("h3",[t._v("您没有该"+t._s(t.text)+"的权限，请联系管理员添加")]),t.isPortal?e("a",{attrs:{href:t.mainPage}},[t._v("回首页")]):e("a",{attrs:{href:t.defaultHomeUrl}},[t._v("回首页")])])])],1)},r=[],n=a(2084),o=a(5872),l=a(4637),u=a(7795),i=a(1811),c={name:"error-403",data(){return{text:"应用",isPortal:0,vueDsl:null,defaultHomeUrl:(0,i.L8)()}},components:{VueRender:o.A},computed:{...(0,l.aH)(["menus_data"]),mainPage(){return window.location.href.slice(0,window.location.href.length-4)}},methods:{getPageInfo(t){const e=this;(0,n.jN)(t).then((t=>{if(t?.data){const{pageDsl:s=null}=t.data;try{e.vueDsl=s?JSON.parse(s):null}catch(a){console.log(a)}}}))}},mounted(){const t=(0,u.hV)(this.menus_data?.data);this.isPortal=t?.isPortal,"page"==this.$route.query.type&&(this.text="页面"),(0,n.zj)().then((t=>{t?.data?.pms403Page&&this.getPageInfo(t.data.pms403Page)}))}},d=c,h=a(6108),f=(0,h.A)(d,s,r,!1,null,"0ec83857",null),m=f.exports}}]);