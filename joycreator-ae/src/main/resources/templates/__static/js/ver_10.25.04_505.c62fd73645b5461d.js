"use strict";(self["webpackChunkjoy_creator"]=self["webpackChunkjoy_creator"]||[]).push([[505],{840:function(e,t,n){n.d(t,{J0:function(){return l},PE:function(){return i},Yq:function(){return a},as:function(){return o},m8:function(){return c},xs:function(){return r}});var s=n(5763);const a=(e,t="YYYY-MM-DD HH:mm:ss")=>{const n=new Date(e),s=n.getFullYear(),a=String(n.getMonth()+1).padStart(2,"0"),i=String(n.getDate()).padStart(2,"0"),o=String(n.getHours()).padStart(2,"0"),r=String(n.getMinutes()).padStart(2,"0"),l=String(n.getSeconds()).padStart(2,"0");return t.replace("YYYY",s).replace("MM",a).replace("DD",i).replace("HH",o).replace("mm",r).replace("ss",l)};function i(e,t){return new Promise(((n,s)=>{fetch(e,{responseType:"blob"}).then((e=>(e.ok||(s(e.status),console.error(`HTTP error! status: ${e.status}`)),e.blob()))).then((e=>{const s=window.URL.createObjectURL(e),a=document.createElement("a");a.href=s,a.download=t,document.body.appendChild(a),a.click(),document.body.removeChild(a),window.URL.revokeObjectURL(s),n()})).catch((e=>{s(e),console.error("下载文件失败：",e)}))}))}const o=(e,t)=>{const{TEXT:n,FILE:a}=s.$z,i={msgType:n,chatContent:e.trim()},o=t[0]&&{msgType:a,id:t[0].id,fileId:t[0].id,fileVO:t[0]?.fileVO||t[0]};return[o,i].filter(Boolean)},r=e=>{if(!Array.isArray(e))return[];const t=[];return e.forEach((e=>{e.chatRole===s.Th.USER&&e.content.forEach((e=>{e.msgType===s.$z.FILE&&t.push(e)}))})),t},l=e=>e.map((e=>{let t="";if(e.chatRole===s.Th.USER)t=e.content||e.messageDetailList;else{let n=e.content.filter((e=>[s.Qy.STREAM].includes(e.chatRole))).map((e=>e.value)).join("。");t=[{msgType:s.$z.ASSISTANT,chatContent:n}]}return{chatRole:e.chatRole,messageDetailList:t}})),c=e=>({...e,erp:e.userName,name:e.realName,orgName:e.fullDetpName})},3002:function(e,t,n){n.d(t,{UV:function(){return d},V1:function(){return A},Xv:function(){return o},bP:function(){return c},nw:function(){return r},ud:function(){return u},v5:function(){return p},zU:function(){return l}});var s=n(908),a=n.n(s),i=n(6858);const o=({params:e,onopen:t,onmessage:n,onerror:s,onclose:a})=>{(0,i.Wp)({url:`${i.VY}/api/v1/llm/chat/doron`,params:e,onopen:t,onmessage:n,onclose:a,onerror:s})},r=({params:e,onopen:t,onmessage:n,onerror:s,onclose:a})=>{(0,i.Wp)({url:`${i.VY}/api/v1/llm/chat/create`,params:e,onopen:t,onmessage:n,onerror:s,onclose:a})},l=({params:e,onopen:t,onmessage:n,onerror:s,onclose:a})=>{(0,i.Wp)({url:`${i.VY}/api/v1/llm/chat/rewrite`,params:e,onopen:t,onmessage:n,onerror:s,onclose:a})},c=()=>a().$http.post(`${i.VY}/api/v1/chat-history/create`),d=e=>a().$http.post(`${i.VY}/api/v1/chat-history/operation`,e),A=e=>a().$http.get(`${i.VY}/api/v1/dict/dtl/${e.dict}`),u=e=>a().$http.get(`${i.VY}/api/v1/suggest-govern/list`,e),p=e=>a().$http.get(`${i.VY}/api/v1/edit-document/convert-html/${e}`)},4607:function(e,t,n){n.d(t,{A:function(){return c}});var s=function(){var e=this,t=e._self._c;return t("div",{staticClass:"tag-block",class:{"single-row":e.singleRow},on:{click:e.handleInteract}},[e.showClose?t("span",{staticClass:"tag-close",on:{click:function(t){return e.$emit("delete",e.index)}}},[t("i",{staticClass:"el-icon-error",staticStyle:{color:"#9e9fa3","font-size":"14px"}})]):e._e(),t("div",{staticClass:"tag-content"},[t("div",{staticClass:"tag-name"},[e.singleRow?t("i",{class:e.fileIconClass}):e._e(),e._v(" "+e._s(e.item.name)+" ")]),e.singleRow?e._e():t("div",{staticClass:"tag-desc"},[t("i",{class:e.fileIconClass}),t("span",{staticClass:"tag-size"},[e._v(e._s(e.formatFileSize(e.item.fileSize)))])])])])},a=[],i={name:"TagItem",props:{item:{type:Object,default:()=>{}},index:{type:Number,default:0},showClose:{type:Boolean,default:!0},singleRow:{type:Boolean,default:!1}},computed:{fileIconClass(){return{joyIcon:!0,"icon-doc":this.isWord(this.item.name),"icon-txt":!this.isWord(this.item.name),"file-icon-color":!0}}},methods:{handleInteract(){console.log("handleInteract",this.item),this.$emit("tagClick",this.item)},isWord(e){const t=["doc","docx"],n=e.split(".");return n.length>1&&t.includes(n[n.length-1].toLowerCase())},formatFileSize(e){if(!e)return"";if(0===e)return"0 B";const t=1024,n=["B","KB","MB","GB","TB","PB","EB","ZB","YB"],s=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,s)).toFixed(2))+" "+n[s]}}},o=i,r=n(6108),l=(0,r.A)(o,s,a,!1,null,"563c9c90",null),c=l.exports},5505:function(e,t,n){n.r(t),n.d(t,{default:function(){return x}});var s=function(){var e=this,t=e._self._c;return t("div",{staticClass:"write-box"},[t("div",{staticClass:"write-content"},[t("p",{staticClass:"write-title"},[e._v("JoyEdit")]),t("p",{staticClass:"write-desc"},[e._v("AI 驱动的内容创作平台，让创意无限可能")]),t("div",{staticClass:"dialogue-input"},[0!==e.tagList.length?t("WriteHeader",{attrs:{tagList:e.tagList},on:{delete:e.handleDeleteTag,tagClick:e.handleViewFile}}):e._e(),t("div",{ref:"inputRef",staticClass:"input-box",attrs:{contenteditable:!0},domProps:{innerHTML:e._s(e.inputValue)},on:{input:e.inputChange,keydown:[function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:(t.preventDefault(),e.handleSearch.apply(null,arguments))},e.handleKeydown],keyup:e.handleKeyup}}),t("div",{staticClass:"input-footer"},[t("div",{staticClass:"footer-category-list"},e._l(e.showSuggestList,(function(n){return t("div",{key:n.id,staticClass:"category-item",on:{click:function(t){return e.handleItem(n)}}},[t("el-image",{style:`background-color: ${n.background}`,attrs:{src:e.imageSrc(n.icon)}}),t("p",{staticClass:"suggest-title"},[e._v(e._s(n.name))])],1)})),0),t("div",{staticClass:"footer-right"},[t("UploadFile",{ref:"uploadFile",attrs:{isDisabled:e.isDisabled},on:{progress:e.handleProgress,success:e.handleSuccess,error:e.handleError}},[t("i",{staticClass:"joyIcon icon-jia",style:{fontSize:"20px",cursor:e.isDisabled?"not-allowed":"pointer",color:e.isDisabled?"#D5D5D8":"#000"}})]),t("i",{staticClass:"row-gap"}),t("i",{staticClass:"joyIcon icon-send1",class:{"is-disabled":!e.isHaveValue||e.loading},on:{click:e.handleSearch}})],1)])],1)])])},a=[],i=n(3002),o=n(8754),r=n(7711),l=n(8939),c=n(7441),d=n(8225),A=n(8880),u=n(840),p=function(){var e=this,t=e._self._c;return t("div",{staticClass:"input-header"},e._l(e.tagList,(function(n,s){return t("TagItem",{key:`tag-${s}`,attrs:{item:{...n.fileVO,item:n}},on:{tagClick:()=>e.$emit("tagClick",n),delete:()=>e.$emit("delete",n)}})})),1)},g=[],h=n(4607),f={name:"WriteHeader",components:{TagItem:h.A},props:{tagList:{type:Array,default:()=>[]}}},m=f,T=n(6108),C=(0,T.A)(m,p,g,!1,null,"4b7dcfd2",null),E=C.exports,w=function(){var e=this,t=e._self._c;return t("UploadFile",{ref:"uploadFile",attrs:{"show-file-list":!1,"with-credentials":!0,multiple:!1,accept:".text,.txt,.doc,.docx",action:e.uploadOption.action,data:e.uploadOption.data,disabled:e.isDisabled,fileList:e.tagList},on:{"update:fileList":function(t){e.tagList=t},"update:file-list":function(t){e.tagList=t},change:e.handleChangeFile,progress:e.handleProgress,success:e.handleSuccess,error:e.handleError}},[e._t("default")],2)},b=[],R=n(6858),S=function(){var e=this,t=e._self._c;return t("el-upload",e._b({ref:"upload",staticClass:"upload-file",attrs:{"on-change":e.handleChange,"on-progress":e.handleProgress,"on-success":e.handleSuccess,"on-error":e.handleError}},"el-upload",e.$attrs,!1),[e._t("default")],2)},y=[],v={name:"UploadFile",inheritAttrs:!1,methods:{handleChange(e,t){this.$emit("change",e,t)},submitUpload(){this.$refs.upload.submit()},handleProgress(e,t,n){this.$emit("progress",e,t,n)},handleSuccess(e,t,n){this.$emit("success",e,t,n)},handleError(e,t,n){this.$emit("error",e,t,n)}}},I=v,N=(0,T.A)(I,S,y,!1,null,"8c3e4a68",null),L=N.exports,B={name:"UploadFileDoc",components:{UploadFile:L},props:{isDisabled:{type:Boolean,default:!1}},data(){return{uploadOption:{action:`${R.VY}/api/v1/file/upload`,data:{fileType:"USER_CHAT"}},tagList:[]}},methods:{handleChangeFile(e,t){},handleProgress(e,t,n){this.$emit("progress",e,t,n)},handleSuccess(e,t,n){console.log("handleSuccess",e),this.$emit("success",e,t,n)},handleError(e,t,n){console.log("handleError",e),this.$emit("error",e,t,n)}}},O=B,U=(0,T.A)(O,w,b,!1,null,null,null),D=U.exports,Y={name:"WritePage",components:{WriteHeader:E,UploadFile:D},data(){return{HomeDocument:o,HomeDeepthinking:r,HomeExample:l,HomeFolder:c,HomePackage:d,HomeRules:A,inputValue:"",loading:!1,tagList:[],isHaveValue:!1,categoryList:[],activeCategory:"",suggestList:[]}},computed:{isDisabled(){return this.loading||this.tagList.length>0},showSuggestList(){return this.suggestList.filter((e=>"Y"===e.isEnabled))},imageSrc(){return e=>{if(e)return 0===e.indexOf("http")?e:this[e.slice(0,1).toUpperCase()+e.slice(1)]}}},created(){this.initCategoryList(),this.getSuggestList()},mounted(){document.querySelector(".input-box").addEventListener("paste",(function(e){e.preventDefault();var t=(e.clipboardData||window.clipboardData).getData("text");document.queryCommandSupported("insertText")?document.execCommand("insertText",!1,t):document.execCommand("paste",!1,t)}))},methods:{handleSuccess(e,t,n){this.loading=!1,200===e.code?(this.tagList=[{...e.data,fileSize:t.size,fileVO:{...e.data,fileSize:t.size}}],this.$message.success("上传成功")):this.$message.error("上传失败")},handleError(e,t,n){this.loading=!1,this.$message.error("上传失败")},handleProgress(e,t,n){this.loading=!0},handleDeleteTag(e){this.tagList.splice(e,1)},handleViewFile(e){console.log("handleViewFile",e)},cleanData(){this.inputValue="",this.loading=!1,this.tagList=[]},initCategoryList(){(0,i.V1)({dict:"SUGGEST_CATEGORY"}).then((e=>{200===e.code&&(this.categoryList=e.data.dictDtlList,this.categoryList.length>0&&(this.activeCategory=this.categoryList[0].value))}))},getSuggestList(){(0,i.ud)().then((e=>{200===e.code&&(this.suggestList=e.data)}))},handleSearch(){const e=this.$refs.inputRef;let t="";e.childNodes.forEach((e=>{if(3===e.nodeType)t+=e.nodeValue;else if(1===e.nodeType&&e.classList.contains("editable")){const n=e.textContent.trim();t+=n||(`【${e.getAttribute("data-placeholder")}】`||"【】")}else 1===e.nodeType&&"BR"===e.tagName&&(t+="\n")})),t=t.trim(),t?(this.loading=!0,(0,i.bP)().then((e=>{if(200===e.code){const n=(0,u.as)(t,this.tagList);sessionStorage.setItem("chatContent",JSON.stringify(n)),this.cleanData(),this.$router.push("/writeDetail?sessionId="+e.data)}})).finally((()=>{this.loading=!1}))):this.loading=!1},handleItem(e){this.inputValue=this.parseTemplate(e.presetPrompt),this.isHaveValue=!0},inputChange(e){if("<br>"===e.target.innerHTML)return this.inputValue="",void(this.isHaveValue=!1);this.$refs.inputRef.innerHTML&&(this.isHaveValue=!0)},handleKeydown(e){if("Backspace"===e.key){const t=window.getSelection();if(!t.rangeCount)return;const n=t.getRangeAt(0),s=n.startContainer;let a=3===s.nodeType?s.parentElement:s;if(a&&a.classList&&a.classList.contains("editable")){const i=(s.nodeType,0===n.startOffset);if(i)return e.preventDefault(),void this.setCaretTo(a);0===a.textContent.length&&e.preventDefault(),1===a.textContent.length?3===s.nodeType&&1===n.startOffset&&(e.preventDefault(),a.textContent="",this.setCaretTo(a)):t.focusOffset-t.anchorOffset===a.textContent.length&&(e.preventDefault(),a.textContent="",this.setCaretTo(a))}}},handleKeyup(e){if("ArrowLeft"!==e.key&&"ArrowRight"!==e.key)return;const t=window.getSelection();if(!t.rangeCount)return;const n=t.getRangeAt(0);let s=n.startContainer,a=n.startOffset;if(1===s.nodeType&&s.classList&&s.classList.contains("editable-split")){let t;if(e.preventDefault&&e.preventDefault(),"ArrowLeft"===e.key){t=s.previousSibling;while(t&&1===t.nodeType&&t.classList.contains("editable-split"))t=t.previousSibling;t&&this.setCaretToEnd(t)}else if("ArrowRight"===e.key){t=s.nextSibling;while(t&&1===t.nodeType&&t.classList.contains("editable-split"))t=t.nextSibling;t&&this.setCaretToStart(t)}}else if(1===s.nodeType&&s.childNodes&&("ArrowLeft"===e.key&&a>0&&s.childNodes[a-1]&&s.childNodes[a-1].classList&&s.childNodes[a-1].classList.contains("editable-split")||"ArrowRight"===e.key&&s.childNodes[a]&&s.childNodes[a].classList&&s.childNodes[a].classList.contains("editable-split"))){let t;if(e.preventDefault&&e.preventDefault(),"ArrowLeft"===e.key){let e=a-1;while(e>=0&&1===s.childNodes[e].nodeType&&s.childNodes[e].classList&&s.childNodes[e].classList.contains("editable-split"))e--;t=e>=0?s.childNodes[e]:null,t&&this.setCaretToEnd(t)}else if("ArrowRight"===e.key){let e=a;while(e<s.childNodes.length&&1===s.childNodes[e].nodeType&&s.childNodes[e].classList&&s.childNodes[e].classList.contains("editable-split"))e++;t=e<s.childNodes.length?s.childNodes[e]:null,t&&this.setCaretToStart(t)}}else;},setCaretTo(e){const t=document.createRange();t.selectNodeContents(e),t.collapse(!0);const n=window.getSelection();n.removeAllRanges(),n.addRange(t)},setCaretToStart(e){const t=document.createRange();3===e.nodeType?t.setStart(e,0):(t.selectNodeContents(e),t.collapse(!0));const n=window.getSelection();n.removeAllRanges(),n.addRange(t)},setCaretToEnd(e){const t=document.createRange();3===e.nodeType?t.setStart(e,e.textContent.length):(t.selectNodeContents(e),t.collapse(!1));const n=window.getSelection();n.removeAllRanges(),n.addRange(t)},parseTemplate(e){return e.replace(/\$\{([^}]+)\}/g,((e,t)=>{const n=t.match(/^(.*?)#(.*?)#$/);if(n){const e=n[1].trim(),t=n[2].trim();return`<span class="editable-split" contenteditable="false"></span><span class="editable" contenteditable="true" data-placeholder="${t}">${e||""}</span><span class="editable-split" contenteditable="false"></span>`}if(t.match(/^#(.*?)#$/)){const e=t.replace(/^#(.*?)#$/,"$1").trim();return`<span class="editable-split" contenteditable="false"></span><span class="editable" contenteditable="true" data-placeholder="${e}"></span><span class="editable-split" contenteditable="false"></span>`}return`<span class="editable-split" contenteditable="false"></span><span class="editable" contenteditable="true">${t.trim()}</span><span class="editable-split" contenteditable="false"></span>`}))}}},k=Y,Q=(0,T.A)(k,s,a,!1,null,"371b785b",null),x=Q.exports},5763:function(e,t,n){n.d(t,{$z:function(){return a},Qy:function(){return i},Th:function(){return s}});const s={USER:"USER",ASSISTANT:"ASSISTANT"},a={TEXT:"TEXT",FILE:"FILE",ASSISTANT:"ASSISTANT"},i={STREAM:"ASSISTANT",TEMPLATE_LIBRARY:"TEMPLATE_LIBRARY",TOOL:"TOOL",TEMPLATE_LIBRARY_QUERY:"TEMPLATE_LIBRARY_QUERY",DOCCONTENT_EXTRACTION_QUERY:"DOCCONTENT_EXTRACTION_QUERY",TEXT_REWRITE:"TEXT_REWRITE",DOCCONTENT_EXTRACTION_ERROR:"DOCCONTENT_EXTRACTION_ERROR",DOCCONTENT_EXTRACTION:"DOCCONTENT_EXTRACTION",DOCCONTENT_REWRITE:"DOCCONTENT_REWRITE",FREELANCE_WRITING:"FREELANCE_WRITING",FREELANCE_WRITING_START:"FREELANCE_WRITING_START",OUTLINE:"OUTLINE",TEXT_REPLACEMENT:"TEXT_REPLACEMENT"}},6858:function(e,t,n){async function s(e,t){const n=e.getReader();let s;while(!(s=await n.read()).done)t(s.value)}function a(e){let t,n,s,a=!1;return function(i){void 0===t?(t=i,n=0,s=-1):t=o(t,i);const r=t.length;let l=0;while(n<r){a&&(10===t[n]&&(l=++n),a=!1);let i=-1;for(;n<r&&-1===i;++n)switch(t[n]){case 58:-1===s&&(s=n-l);break;case 13:a=!0;case 10:i=n;break}if(-1===i)break;e(t.subarray(l,i),s),l=n,s=-1}l===r?t=void 0:0!==l&&(t=t.subarray(l),n-=l)}}function i(e,t,n){let s=r();const a=new TextDecoder;return function(i,o){if(0===i.length)null===n||void 0===n||n(s),s=r();else if(o>0){const n=a.decode(i.subarray(0,o)),r=o+(32===i[o+1]?2:1),l=a.decode(i.subarray(r));switch(n){case"data":s.data=s.data?s.data+"\n"+l:l;break;case"event":s.event=l;break;case"id":e(s.id=l);break;case"retry":const n=parseInt(l,10);isNaN(n)||t(s.retry=n);break}}}}function o(e,t){const n=new Uint8Array(e.length+t.length);return n.set(e),n.set(t,e.length),n}function r(){return{data:"",event:"",id:"",retry:void 0}}n.d(t,{VY:function(){return f},n2:function(){return C},Rh:function(){return T},Wp:function(){return m}});var l=function(e,t){var n={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&t.indexOf(s)<0&&(n[s]=e[s]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var a=0;for(s=Object.getOwnPropertySymbols(e);a<s.length;a++)t.indexOf(s[a])<0&&Object.prototype.propertyIsEnumerable.call(e,s[a])&&(n[s[a]]=e[s[a]])}return n};const c="text/event-stream",d=1e3,A="last-event-id";function u(e,t){var{signal:n,headers:o,onopen:r,onmessage:u,onclose:g,onerror:h,openWhenHidden:f,fetch:m}=t,T=l(t,["signal","headers","onopen","onmessage","onclose","onerror","openWhenHidden","fetch"]);return new Promise(((t,l)=>{const C=Object.assign({},o);let E;function w(){E.abort(),document.hidden||I()}C.accept||(C.accept=c),f||document.addEventListener("visibilitychange",w);let b=d,R=0;function S(){document.removeEventListener("visibilitychange",w),window.clearTimeout(R),E.abort()}null===n||void 0===n||n.addEventListener("abort",(()=>{S(),t()}));const y=null!==m&&void 0!==m?m:window.fetch,v=null!==r&&void 0!==r?r:p;async function I(){var n;E=new AbortController;try{const n=await y(e,Object.assign(Object.assign({},T),{headers:C,signal:E.signal}));await v(n),await s(n.body,a(i((e=>{e?C[A]=e:delete C[A]}),(e=>{b=e}),u))),null===g||void 0===g||g(),S(),t()}catch(o){if(!E.signal.aborted)try{const e=null!==(n=null===h||void 0===h?void 0:h(o))&&void 0!==n?n:b;window.clearTimeout(R),R=window.setTimeout(I,e)}catch(r){S(),l(r)}}}I()}))}function p(e){const t=e.headers.get("content-type");if(!(null===t||void 0===t?void 0:t.startsWith(c)))throw new Error(`Expected content-type to be ${c}, Actual: ${t}`)}var g=n(908),h=n.n(g);let f="";"app.dima.jd.com"!==location.hostname&&"joyb.jd.com"!==location.hostname||(f="http://joyedit-dev.jd.com",localStorage.setItem("baseURL",f));const m=({url:e,params:t,onopen:n,onmessage:s,onerror:a,onclose:i})=>{const o=new AbortController;u(e,{method:"POST",credentials:"include",headers:{"Content-Type":"application/json","Cache-Control":"no-cache",Connection:"keep-alive",accept:"text/event-stream"},signal:o.signal,body:t,openWhenHidden:!0,onopen(){n&&n(o)},onmessage:e=>{s&&s(e)},onerror:e=>(a&&a(e),console.log("🚀 ~ onerror ~ error:",e),!1),onclose:()=>{i&&i()}})},T=e=>h().$http.post(`${f}/api/v1/model/${e.modelName}`,e),C=e=>h().$http.get(`${f}/api/v1/model/${e.modelName}/${e.id}`)},7441:function(e){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAE5SURBVHgB1VWLccIwDJV7HSAjeIMyAiOkE5Ru0E5QOgEwAWECMkI2gA3IBrCBeSLyYcAfQgIH786nyJYt6SmyiV4dyn4YYzKI7Gx9p5TaUVfg8Bxjay7Bc3/UAUocFBBfGCVHLWuczQBDY9QYn8hmTS3xfqb/4pDaKnCsIcbifCWBzJwgQjilljcKJdpnLRRuTDsMfRl4gWiYuhKbfiA/EuZMbU5N1tVVDhxH05SNsJBb/Y3ujGgGiGZETcFKiUwHTNfRfvEVmYtke0H0caqgYqdlbp7KgP/5hUhGFbGtQwtBB5LyyNGrhJN2DhhIc0kNv/9CwzBgWrgN6jvIV4OBJVj0fmvA9w6MvunIb0FhioJ3VJQiOCmc75oixQzh4Y02AS1dH5jsYsZpqj5xuI9ST+at6OepfQrsAaH+huZsf5WgAAAAAElFTkSuQmCC"},7711:function(e){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAG6SURBVHgBtVXRVcMwDHSYICOYCcgIYQNvEG/QMEHLBElHYALYIN0AmCDdgG5gLDi9KI7swkf1np8T5SSfzrFszI2tSh0hhDpODq9nGlVVnbVgYNs4aL7E8ZHDcsAQdJvi8ALn4dNsUCvAhx6sj2DUxPEAllwRmcV8iuOTmMPXYR5jJU+SucXqM8pOK2sUpk7B1cgRVnkIDOchk3wWUs2CjFXwPb57er+Dv04kYHAbpwllH2PZj3Hcx+dn+N6BkXYxyqpcwSiTCzl8gWmQi1CO1Mfa/UiAd9qTr1xyEeeBIWwD3wSfTcH8wQmde3PFxCJzWH6GqQQMhQ3fg4hL/GMS63NsZmZzhQBJUhtdgVXsndHtrPjqzDObNSVTJBoUDEnxmkoA6coSiRLlwerMFYuYndhkp25yWFoF/6ZNWH7TrpC8E3tiE6KrVsGHKnfQ+kJystasZSRzEuw17bAIV7IX/kEwb5OYbS7h1JjasG5wk3huFPxBq6AWQVq7dmFrWnLZri355IVD+u/M7xl4wfyfC6dGPH2jzrttM2F75Nmm8Pcrc5Q5tUvfgjlf5KfIZtvjFywPsrcc9mb2DW6pYTo5wtXVAAAAAElFTkSuQmCC"},8225:function(e){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAD/SURBVHgB1VWBEcIgDEw9B2CEuoEbKBvoBrqBTqAbOELdQJ3AbiIj4AQ1SOxxuZLioZ79uy89EiB5khZg6CgkY9M0JQ4Vsoy4WOSyKAoT22MEMjbIuWCfIneCHcYgQ9GoeZSU3a1nvZdIkEIRTWS987fEEK10L4liUlhhcyCb7ZhvpRsHkTpo6cJSwKXru+RsfP0AXkUHTNFCHpR0wAI+DC6RRk4YNdkuwVyd4P8Ez8B0VJEh2WbgS1nR6Hxr5uuqCKQDYtiCb8SKzfUi6QCM9IiR1fi6At9Y59R+Sc0AaMM9vInhN9rPO/nKyywDd/cI/wcnYG2eAYNc536Z/wMP+7ZKA5x0TcMAAAAASUVORK5CYII="},8754:function(e){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAEHSURBVHgBxZXtDcIgEIavpv/tCN1AN7AjdAQ3ME7QbuAKbKAj0A10AhkBJ8AjucYG+Sgg8UkuNAe9F7jrFaAwlelQSp1w2EMaU1VVzDmLwXuVT7eMWRsaDY1ntBvE0aNd0FqfwIzEowqIAHcubf4NFKa4QO2bpIR14IfjdfIkAQo+QBgOiQLM9zIhfJNeAaokARn8t4owyaP5mUIkoRwICOcgXYAaFzP9eJDWsnwLsQI2dEPE4bp2fbQAfBrihPZc+Hp6lrkCM0xfIZ5IB+fke4CRs6wyXQTfUfAOReWvBILBNa4rahyVopmrZSARZ/AvdPdUcdzpmpzYfvpHHA4Q5oU2rtp5Sd6fe8/RT3qNxwAAAABJRU5ErkJggg=="},8880:function(e){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAC6SURBVHgB5ZKBDYMwDAQzSkfoCNmk3YAR8AZlg4zSURihI6RBeqmu5YaUPBISliyIk78PL0I4auWcL6VT6SGwC/A5fyoFVhn4pN77TQz86cy2m1g4niPFxAAEM8E6dpl4sai9uHaRv+A6lopm9C6zBhfMvmJxNDfsv0pfW+HVWLrhjbFsggtmZ48FhyccfmDNiUUJ7hDMy9dgFilwJRRrQoO3mHTDayY0+A+TgQp3TPhwZbL8XWkXOKveq6uMzCEH958AAAAASUVORK5CYII="},8939:function(e){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAFjSURBVHgBvVSBcYMwDJR7GYARGIFuQDdoJygbtN2ATlA2SDdINkg7QdiAbAAbuFJ5J7ocRoa75O90cWy9Jb/fEC2A977h+KJbgTfvJJZwHujeEAk4isjas0RkrTDl44TSj+ivi2CDCkWm1npwS6tIhcQa/98UWaOTXOS8Y66iFIQO+XerTrSTooidKrTVnGTgLgQtRz6xnnMckbPMuiAHGbKZvAw5tvZXxO9UTZUxmql1hw6D9Qbn3F6OzuOCxy6hgPB7jpbTH2HjcOq9luNfEpA6v+DF6nwlmSDfSNccn+EEipdTOiS3xfhDnWCgSEdN6sX58eGd7ZoEdXHHm7gI5OCkQ+QdZHgjs927mQLS+YFDXuiJ44fjF8sy90qj1jL/wg4aaA38+GnQzgiQz0dt8U2fq0IlXZx1otH367pWm9beRj23x8aoMaBbKyeKJIliXbJEtcVNLeAjBUy+JVHAE63EH75C7d3KUKleAAAAAElFTkSuQmCC"}}]);