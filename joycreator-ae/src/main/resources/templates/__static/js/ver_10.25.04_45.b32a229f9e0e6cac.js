"use strict";(self["webpackChunkjoy_creator"]=self["webpackChunkjoy_creator"]||[]).push([[45],{1573:function(e,t,n){n.d(t,{A:function(){return $}});var o=function(){var e=this,t=e._self._c;return t("div",{staticClass:"editor-container"},[t("joy-edit",{ref:"editorRef",attrs:{env:"dev",editorOptions:e.editorOptions},on:{load:e.handleLoad,save:e.handleSave}}),e.isWait?t("div",{staticClass:"loading-box"},[t("WriteLoading")],1):e._e(),e.loading?t("div",{staticClass:"opera-btn",on:{click:e.handleStop}},[t("i",{staticClass:"joyIcon icon-pause"}),t("p",[e._v("停止")])]):e._e(),e.isShowSuccess?t("div",{staticClass:"opera-btn"},[t("i",{staticClass:"joyIcon icon-chenggong"}),t("p",[e._v("写作完成")])]):e._e()],1)},i=[],s=n(3002),r=n(2607),a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"write-loading",class:{paused:e.isCancelled}},[t("el-image",{attrs:{src:e.LoadingSvg}}),e._m(0)],1)},c=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"dashed-lines-container"},[t("div",{staticClass:"line-segment",staticStyle:{"--segment-index":"0"}}),t("div",{staticClass:"line-segment",staticStyle:{"--segment-index":"1"}}),t("div",{staticClass:"line-segment",staticStyle:{"--segment-index":"2"}}),t("div",{staticClass:"line-segment",staticStyle:{"--segment-index":"3"}})])}],l=n.p+"__static/img/ver_10.25.04_loading.40f7a870be240f98.svg",d={name:"WriteLoading",data(){return{LoadingSvg:l,isCancelled:!1}}},u=d,h=n(6108),p=(0,h.A)(u,a,c,!1,null,"117a2f00",null),f=p.exports,v={name:"EditorComp",components:{WriteLoading:f},props:{value:{type:String,default:""},editable:{type:Boolean,default:!0},isShowToolbar:{type:Boolean,default:!0},sessionId:{type:String,default:""},isShowUpload:{type:Boolean,default:!1}},watch:{value(){this.$refs.editorRef&&this.$refs.editorRef.setContent(this.value,{isScrollBottom:!1})}},data(){return{loading:!1,isWait:!1,isShowSuccess:!1,editor:null,editorOptions:{editable:this.editable,isShowToolbar:this.isShowToolbar,ai:{enable:!0},base:{upload:{isShow:this.isShowUpload}}}}},mounted(){this.$refs.editorRef&&this.$emit("editorInstance",this.$refs.editorRef)},methods:{handleLoad(){this.$refs.editorRef&&this.$emit("editorInstance",this.$refs.editorRef)},toSearch(e,t){this.isWait=!0,this.loading=!0,this.$refs.editorRef.setContent("",{isEditable:!1});let n="",o=s.nw;"resetWrite"===t&&(o=s.zU),o({params:e,onopen:e=>{this.controller=e},onmessage:e=>{this.isWait&&(this.isWait=!1);const t=JSON.parse(e.data);if(n+=t.newContent,this.$refs.editorRef.setContent(n,{isEditable:t.finished}),this.forceStop)return this.controller.abort(),this.loading=!1,this.isShowSuccess=!1,void this.$refs.editorRef.setEditable(!0)},onerror:e=>{this.loading=!1,console.error(e)},onclose:()=>{this.loading=!1,setTimeout((()=>{this.isShowSuccess=!0,this.$refs.editorRef.setEditable(!0)}),0),setTimeout((()=>{this.isShowSuccess=!1}),2e3)}})},handleSave(e){if(!["writeDetail","chatHistoryForm"].includes(this.$route.name))return;const t=this.$refs.editorRef.getValue();(0,r.T3)({sessionId:this.sessionId,documentContent:e,textContent:t}).then((e=>{this.$eventBus.$emit("savePromise",e)})).catch((e=>{this.$eventBus.$emit("savePromise",e)}))},clearValue(){this.$refs.editorRef.setContent("")},handleStop(){this.controller&&this.controller.abort(),this.loading=!1},getHtml(){return this.$refs.editorRef.getHtml()}}},m=v,g=(0,h.A)(m,o,i,!1,null,null,null),$=g.exports},2607:function(e,t,n){n.d(t,{JO:function(){return d},Rm:function(){return r},T3:function(){return l},aA:function(){return c},um:function(){return a},wX:function(){return u}});var o=n(908),i=n.n(o),s=n(6858);const r=e=>i().$http.post(`${s.VY}/api/v1/chat-history/list`,e),a=e=>i().$http.get(`${s.VY}/api/v1/chat-history/dtl/${e.sessionId}`),c=e=>i().$http.get(`${s.VY}/api/v1/edit-document/dtl/${e.sessionId}`),l=e=>i().$http.post(`${s.VY}/api/v1/edit-document/save`,e),d=e=>i().$http.post(`${s.VY}/api/v1/chat-history/collection`,e),u=e=>i().$http.delete(`${s.VY}/api/v1/chat-history/del/${e.sessionId}`)},3002:function(e,t,n){n.d(t,{UV:function(){return d},V1:function(){return u},Xv:function(){return r},bP:function(){return l},nw:function(){return a},ud:function(){return h},v5:function(){return p},zU:function(){return c}});var o=n(908),i=n.n(o),s=n(6858);const r=({params:e,onopen:t,onmessage:n,onerror:o,onclose:i})=>{(0,s.Wp)({url:`${s.VY}/api/v1/llm/chat/doron`,params:e,onopen:t,onmessage:n,onclose:i,onerror:o})},a=({params:e,onopen:t,onmessage:n,onerror:o,onclose:i})=>{(0,s.Wp)({url:`${s.VY}/api/v1/llm/chat/create`,params:e,onopen:t,onmessage:n,onerror:o,onclose:i})},c=({params:e,onopen:t,onmessage:n,onerror:o,onclose:i})=>{(0,s.Wp)({url:`${s.VY}/api/v1/llm/chat/rewrite`,params:e,onopen:t,onmessage:n,onerror:o,onclose:i})},l=()=>i().$http.post(`${s.VY}/api/v1/chat-history/create`),d=e=>i().$http.post(`${s.VY}/api/v1/chat-history/operation`,e),u=e=>i().$http.get(`${s.VY}/api/v1/dict/dtl/${e.dict}`),h=e=>i().$http.get(`${s.VY}/api/v1/suggest-govern/list`,e),p=e=>i().$http.get(`${s.VY}/api/v1/edit-document/convert-html/${e}`)},6858:function(e,t,n){async function o(e,t){const n=e.getReader();let o;while(!(o=await n.read()).done)t(o.value)}function i(e){let t,n,o,i=!1;return function(s){void 0===t?(t=s,n=0,o=-1):t=r(t,s);const a=t.length;let c=0;while(n<a){i&&(10===t[n]&&(c=++n),i=!1);let s=-1;for(;n<a&&-1===s;++n)switch(t[n]){case 58:-1===o&&(o=n-c);break;case 13:i=!0;case 10:s=n;break}if(-1===s)break;e(t.subarray(c,s),o),c=n,o=-1}c===a?t=void 0:0!==c&&(t=t.subarray(c),n-=c)}}function s(e,t,n){let o=a();const i=new TextDecoder;return function(s,r){if(0===s.length)null===n||void 0===n||n(o),o=a();else if(r>0){const n=i.decode(s.subarray(0,r)),a=r+(32===s[r+1]?2:1),c=i.decode(s.subarray(a));switch(n){case"data":o.data=o.data?o.data+"\n"+c:c;break;case"event":o.event=c;break;case"id":e(o.id=c);break;case"retry":const n=parseInt(c,10);isNaN(n)||t(o.retry=n);break}}}}function r(e,t){const n=new Uint8Array(e.length+t.length);return n.set(e),n.set(t,e.length),n}function a(){return{data:"",event:"",id:"",retry:void 0}}n.d(t,{VY:function(){return m},n2:function(){return b},Rh:function(){return $},Wp:function(){return g}});var c=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var i=0;for(o=Object.getOwnPropertySymbols(e);i<o.length;i++)t.indexOf(o[i])<0&&Object.prototype.propertyIsEnumerable.call(e,o[i])&&(n[o[i]]=e[o[i]])}return n};const l="text/event-stream",d=1e3,u="last-event-id";function h(e,t){var{signal:n,headers:r,onopen:a,onmessage:h,onclose:f,onerror:v,openWhenHidden:m,fetch:g}=t,$=c(t,["signal","headers","onopen","onmessage","onclose","onerror","openWhenHidden","fetch"]);return new Promise(((t,c)=>{const b=Object.assign({},r);let y;function w(){y.abort(),document.hidden||R()}b.accept||(b.accept=l),m||document.addEventListener("visibilitychange",w);let S=d,C=0;function V(){document.removeEventListener("visibilitychange",w),window.clearTimeout(C),y.abort()}null===n||void 0===n||n.addEventListener("abort",(()=>{V(),t()}));const j=null!==g&&void 0!==g?g:window.fetch,O=null!==a&&void 0!==a?a:p;async function R(){var n;y=new AbortController;try{const n=await j(e,Object.assign(Object.assign({},$),{headers:b,signal:y.signal}));await O(n),await o(n.body,i(s((e=>{e?b[u]=e:delete b[u]}),(e=>{S=e}),h))),null===f||void 0===f||f(),V(),t()}catch(r){if(!y.signal.aborted)try{const e=null!==(n=null===v||void 0===v?void 0:v(r))&&void 0!==n?n:S;window.clearTimeout(C),C=window.setTimeout(R,e)}catch(a){V(),c(a)}}}R()}))}function p(e){const t=e.headers.get("content-type");if(!(null===t||void 0===t?void 0:t.startsWith(l)))throw new Error(`Expected content-type to be ${l}, Actual: ${t}`)}var f=n(908),v=n.n(f);let m="";"app.dima.jd.com"!==location.hostname&&"joyb.jd.com"!==location.hostname||(m="http://joyedit-dev.jd.com",localStorage.setItem("baseURL",m));const g=({url:e,params:t,onopen:n,onmessage:o,onerror:i,onclose:s})=>{const r=new AbortController;h(e,{method:"POST",credentials:"include",headers:{"Content-Type":"application/json","Cache-Control":"no-cache",Connection:"keep-alive",accept:"text/event-stream"},signal:r.signal,body:t,openWhenHidden:!0,onopen(){n&&n(r)},onmessage:e=>{o&&o(e)},onerror:e=>(i&&i(e),console.log("🚀 ~ onerror ~ error:",e),!1),onclose:()=>{s&&s()}})},$=e=>v().$http.post(`${m}/api/v1/model/${e.modelName}`,e),b=e=>v().$http.get(`${m}/api/v1/model/${e.modelName}/${e.id}`)}}]);