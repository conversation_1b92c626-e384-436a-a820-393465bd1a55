<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:jsf="http://jsf.jd.com/schema/jsf"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
        http://jsf.jd.com/schema/jsf http://jsf.jd.com/schema/jsf/jsf.xsd"
       default-autowire="byName">

    <jsf:server id="joycreator"  protocol="jsf"/>
    <jsf:filter id="jsfInvokerFilter"  class="com.jd.jdt.joycreator.ae.config.JsfInvokerFilter" />


</beans>
 