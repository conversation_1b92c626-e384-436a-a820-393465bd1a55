{"ipmsRoleMenuList": [{"menuCode": "ceshidemos_records_v4", "roleCode": "default"}, {"menuCode": "write_detail", "roleCode": "default"}, {"menuCode": "chat_history_records_v4", "roleCode": "default"}, {"menuCode": "chat_history_form_v4", "roleCode": "default"}, {"menuCode": "chat_history_detail_v4", "roleCode": "default"}], "ipmsRoleModelList": [{"ipmsRoleModelFieldList": [{"accessPms": "Write", "fieldName": "id", "modelName": "template_library", "roleCode": "default"}, {"accessPms": "Write", "fieldName": "name", "modelName": "template_library", "roleCode": "default"}, {"accessPms": "Write", "fieldName": "owner_id", "modelName": "template_library", "roleCode": "default"}, {"accessPms": "Write", "fieldName": "create_time", "modelName": "template_library", "roleCode": "default"}, {"accessPms": "Write", "fieldName": "update_time", "modelName": "template_library", "roleCode": "default"}, {"accessPms": "Write", "fieldName": "created_user", "modelName": "template_library", "roleCode": "default"}, {"accessPms": "Write", "fieldName": "modified_user", "modelName": "template_library", "roleCode": "default"}, {"accessPms": "Write", "fieldName": "yn", "modelName": "template_library", "roleCode": "default"}, {"accessPms": "Write", "fieldName": "tag", "modelName": "template_library", "roleCode": "default"}, {"accessPms": "Write", "fieldName": "content", "modelName": "template_library", "roleCode": "default"}, {"accessPms": "Write", "fieldName": "description", "modelName": "template_library", "roleCode": "default"}], "modelName": "template_library", "readAccessPms": "7", "roleCode": "default", "writeAccessPms": "1"}, {"ipmsRoleModelFieldList": [{"accessPms": "Write", "fieldName": "id", "modelName": "chat_history", "roleCode": "default"}, {"accessPms": "Write", "fieldName": "name", "modelName": "chat_history", "roleCode": "default"}, {"accessPms": "Write", "fieldName": "owner_id", "modelName": "chat_history", "roleCode": "default"}, {"accessPms": "Write", "fieldName": "create_time", "modelName": "chat_history", "roleCode": "default"}, {"accessPms": "Write", "fieldName": "update_time", "modelName": "chat_history", "roleCode": "default"}, {"accessPms": "Write", "fieldName": "created_user", "modelName": "chat_history", "roleCode": "default"}, {"accessPms": "Write", "fieldName": "modified_user", "modelName": "chat_history", "roleCode": "default"}, {"accessPms": "Write", "fieldName": "yn", "modelName": "chat_history", "roleCode": "default"}, {"accessPms": "Write", "fieldName": "erp", "modelName": "chat_history", "roleCode": "default"}, {"accessPms": "Write", "fieldName": "session_id", "modelName": "chat_history", "roleCode": "default"}], "modelName": "chat_history", "readAccessPms": "1", "roleCode": "default", "writeAccessPms": "1"}, {"ipmsRoleModelFieldList": [{"accessPms": "Write", "fieldName": "id", "modelName": "suggest_govern", "roleCode": "default"}, {"accessPms": "Write", "fieldName": "name", "modelName": "suggest_govern", "roleCode": "default"}, {"accessPms": "Write", "fieldName": "owner_id", "modelName": "suggest_govern", "roleCode": "default"}, {"accessPms": "Write", "fieldName": "create_time", "modelName": "suggest_govern", "roleCode": "default"}, {"accessPms": "Write", "fieldName": "update_time", "modelName": "suggest_govern", "roleCode": "default"}, {"accessPms": "Write", "fieldName": "created_user", "modelName": "suggest_govern", "roleCode": "default"}, {"accessPms": "Write", "fieldName": "modified_user", "modelName": "suggest_govern", "roleCode": "default"}, {"accessPms": "Write", "fieldName": "yn", "modelName": "suggest_govern", "roleCode": "default"}, {"accessPms": "Write", "fieldName": "icon", "modelName": "suggest_govern", "roleCode": "default"}, {"accessPms": "Write", "fieldName": "is_enabled", "modelName": "suggest_govern", "roleCode": "default"}, {"accessPms": "Write", "fieldName": "priority", "modelName": "suggest_govern", "roleCode": "default"}, {"accessPms": "Write", "fieldName": "preset_prompt", "modelName": "suggest_govern", "roleCode": "default"}, {"accessPms": "Write", "fieldName": "background", "modelName": "suggest_govern", "roleCode": "default"}, {"accessPms": "Write", "fieldName": "recommendation_category_enum", "modelName": "suggest_govern", "roleCode": "default"}, {"accessPms": "Write", "fieldName": "category", "modelName": "suggest_govern", "roleCode": "default"}], "modelName": "suggest_govern", "readAccessPms": "7", "roleCode": "default", "writeAccessPms": "7"}], "roleCode": "default", "roleDescription": "缺省角色", "roleName": "缺省角色", "roleType": "2"}