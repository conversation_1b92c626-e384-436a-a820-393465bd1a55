{"description": "", "draft": false, "editable": true, "edition": "v1", "isTree": false, "modelFields": [{"businessType": "number", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "主键id", "fieldFormat": "", "fieldLength": 64, "fieldName": "id", "fieldShow": true, "fieldText": "主键id", "fieldTrack": false, "fieldType": "number", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": true, "outDbRule": "", "pointLength": 0, "relatedOriginModel": "0", "searchShow": true, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "string", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "名称", "fieldFormat": "", "fieldLength": 100, "fieldName": "name", "fieldShow": true, "fieldText": "名称", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": true, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "user", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "归属人（erp）", "fieldFormat": "", "fieldLength": 64, "fieldName": "owner_id", "fieldShow": true, "fieldText": "归属人", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "dateTime", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "创建时间", "fieldFormat": "", "fieldLength": 0, "fieldName": "create_time", "fieldShow": true, "fieldText": "创建时间", "fieldTrack": false, "fieldType": "date", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "dateTime", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "更新时间", "fieldFormat": "", "fieldLength": 0, "fieldName": "update_time", "fieldShow": true, "fieldText": "更新时间", "fieldTrack": false, "fieldType": "date", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "user", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "创建人", "fieldFormat": "", "fieldLength": 100, "fieldName": "created_user", "fieldShow": true, "fieldText": "创建人", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "user", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "修改人", "fieldFormat": "", "fieldLength": 100, "fieldName": "modified_user", "fieldShow": true, "fieldText": "修改人", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "checkBox", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "是否有效(1:有效,0:无效)", "fieldFormat": "", "fieldLength": 1, "fieldName": "yn", "fieldShow": true, "fieldText": "是否有效", "fieldTrack": false, "fieldType": "boolean", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "string", "defaultField": false, "defaultValueType": 0, "entityField": false, "fieldComment": "模型名称", "fieldFormat": "", "fieldLength": 64, "fieldName": "modelName", "fieldShow": true, "fieldText": "模型名称", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": false, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "string", "defaultField": false, "defaultValueType": 0, "entityField": false, "fieldComment": "页面标识", "fieldFormat": "", "fieldLength": 32, "fieldName": "page<PERSON><PERSON>", "fieldShow": true, "fieldText": "页面标识", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": false, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "string", "defaultField": false, "defaultValueType": 0, "entityField": false, "fieldComment": "视图名称", "fieldFormat": "", "fieldLength": 32, "fieldName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fieldShow": true, "fieldText": "视图名称", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": false, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"actualDefaultValue": true, "businessType": "checkBox", "defaultField": false, "defaultValue": "true", "defaultValueType": 0, "entityField": false, "fieldComment": "是否默认", "fieldFormat": "", "fieldLength": 1, "fieldName": "defaultState", "fieldShow": true, "fieldText": "是否默认", "fieldTrack": false, "fieldType": "boolean", "fileMultiple": "0", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": false, "outDbRule": "", "pointLength": 0, "relatedOriginModel": "0", "relationshipRule": "", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "syncModify": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "number", "defaultField": false, "defaultValueType": 0, "entityField": false, "fieldComment": "排序", "fieldFormat": "", "fieldLength": 9, "fieldName": "showIndex", "fieldShow": true, "fieldText": "排序", "fieldTrack": false, "fieldType": "number", "fileMultiple": "0", "fileSuffix": "[]", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": false, "outDbRule": "", "pointLength": 0, "relatedOriginModel": "0", "relationshipRule": "", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "syncModify": false, "tableFieldName": "", "thousandsSeparator": true}, {"actualDefaultValue": "", "businessType": "richText", "defaultField": false, "defaultValue": "", "defaultValueType": 0, "entityField": false, "fieldComment": "视图数据", "fieldFormat": "", "fieldName": "viewData", "fieldShow": true, "fieldText": "视图数据", "fieldTrack": false, "fieldType": "file", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": false, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"actualDefaultValue": false, "businessType": "checkBox", "defaultField": false, "defaultValue": "false", "defaultValueType": 0, "entityField": false, "fieldComment": "高级查询启用状态", "fieldFormat": "", "fieldName": "openCombinationRule", "fieldShow": true, "fieldText": "高级查询启用状态", "fieldTrack": false, "fieldType": "boolean", "fileMultiple": "0", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": false, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"actualDefaultValue": "", "businessType": "string", "defaultField": false, "defaultValue": "", "defaultValueType": 0, "entityField": false, "fieldComment": "高级查询：组合规则", "fieldFormat": "", "fieldLength": 1024, "fieldName": "combinationRule", "fieldShow": true, "fieldText": "高级查询：组合规则", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": false, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"actualDefaultValue": "", "businessType": "string", "defaultField": false, "defaultValue": "", "defaultValueType": 0, "entityField": false, "fieldComment": "", "fieldFormat": "", "fieldLength": 64, "fieldName": "displayDensity", "fieldShow": true, "fieldText": "展示密度", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": false, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"actualDefaultValue": true, "businessType": "checkBox", "defaultField": false, "defaultValue": "true", "defaultValueType": 0, "entityField": false, "fieldComment": "", "fieldFormat": "", "fieldName": "freezeFirstColumn", "fieldShow": true, "fieldText": "冻结首列", "fieldTrack": false, "fieldType": "boolean", "fileMultiple": "0", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": false, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"actualDefaultValue": true, "businessType": "checkBox", "defaultField": false, "defaultValue": "true", "defaultValueType": 0, "entityField": false, "fieldComment": "", "fieldFormat": "", "fieldName": "freezeOperateColumn", "fieldShow": true, "fieldText": "冻结操作列", "fieldTrack": false, "fieldType": "boolean", "fileMultiple": "0", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": false, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"actualDefaultValue": "", "beginNum": 1, "businessType": "string", "defaultField": false, "defaultValue": "", "defaultValueType": 0, "entityField": false, "fieldComment": "视图标识", "fieldFormat": "", "fieldLength": 32, "fieldName": "editor<PERSON><PERSON><PERSON>C<PERSON>", "fieldShow": true, "fieldText": "视图标识", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "fileSuffix": "[]", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": false, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}], "modelIndices": [{"indexCode": "idx_editorViewCode", "indexField": "[\"editorViewCode\"]", "indexName": "idx_editorViewCode"}], "modelName": "common_model_config_list_view", "modelText": "应用运行时列表视图", "modelType": 1, "tableName": "", "vn": 0, "wfOpen": false}