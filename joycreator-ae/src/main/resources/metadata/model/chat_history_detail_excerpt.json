{"description": "", "draft": false, "editable": true, "edition": "v2", "isTree": false, "modelFields": [{"businessType": "number", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "主键id", "fieldFormat": "", "fieldLength": 64, "fieldName": "id", "fieldShow": false, "fieldText": "主键id", "fieldTrack": false, "fieldType": "number", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": true, "maxFileCount": "1", "must": true, "outDbRule": "", "pointLength": 0, "relatedOriginModel": "0", "searchShow": true, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "string", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "名称", "fieldFormat": "", "fieldLength": 100, "fieldName": "name", "fieldShow": true, "fieldText": "名称", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": true, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": true, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "user", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "归属人（erp）", "fieldFormat": "", "fieldLength": 64, "fieldName": "owner_id", "fieldShow": false, "fieldText": "归属人", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": true, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "dateTime", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "创建时间", "fieldFormat": "", "fieldLength": 0, "fieldName": "create_time", "fieldShow": false, "fieldText": "创建时间", "fieldTrack": false, "fieldType": "date", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": true, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "dateTime", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "更新时间", "fieldFormat": "", "fieldLength": 0, "fieldName": "update_time", "fieldShow": false, "fieldText": "更新时间", "fieldTrack": false, "fieldType": "date", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": true, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "user", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "创建人", "fieldFormat": "", "fieldLength": 100, "fieldName": "created_user", "fieldShow": false, "fieldText": "创建人", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": true, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "user", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "修改人", "fieldFormat": "", "fieldLength": 100, "fieldName": "modified_user", "fieldShow": false, "fieldText": "修改人", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": true, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "checkBox", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "是否有效(1:有效,0:无效)", "fieldFormat": "", "fieldLength": 1, "fieldName": "yn", "fieldShow": false, "fieldText": "是否有效", "fieldTrack": false, "fieldType": "boolean", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": true, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"actualDefaultValue": "", "businessType": "select", "combinationRule": "", "defaultField": false, "defaultSelect": "[]", "defaultValue": "", "defaultValueType": 0, "entityField": false, "fieldComment": "", "fieldFormat": "{\"showType\":\"select\"}", "fieldLength": 128, "fieldName": "msg_type", "fieldShow": true, "fieldText": "引用类型", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "fileSuffix": "[]", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "matchQueryField": "", "maxFileCount": "1", "must": false, "outDbRule": "", "preFieldName": "", "queryConditions": "[]", "relatedOriginModel": "0", "searchShow": false, "selectDataJson": "[]", "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"actualDefaultValue": "", "businessType": "findRelated", "calculationFieldName": "name", "combinationRule": "", "defaultField": false, "defaultValue": "", "defaultValueType": 0, "entityField": false, "fieldComment": "", "fieldFormat": "", "fieldName": "chat_history_detail_id", "fieldShow": true, "fieldText": "聊天记录明细", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "fileSuffix": "[]", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": false, "outDbRule": "", "queryConditions": "[]", "relatedModel": {"description": "", "draft": false, "editable": true, "edition": "v2", "isTree": false, "modelName": "chat_history_detail", "modelText": "聊天记录明细", "modelType": 0, "tableName": "", "vn": 0, "wfOpen": false}, "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"actualDefaultValue": "", "businessType": "findRelated", "calculationFieldName": "name", "combinationRule": "", "defaultField": false, "defaultValue": "", "defaultValueType": 0, "entityField": false, "fieldComment": "", "fieldFormat": "", "fieldName": "file_id", "fieldShow": true, "fieldText": "附件", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "fileSuffix": "[]", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": false, "outDbRule": "", "queryConditions": "[]", "relatedModel": {"description": "业务附件", "draft": false, "editable": true, "edition": "v2", "isTree": false, "modelName": "file", "modelText": "文件", "modelType": 0, "tableName": "", "vn": 0, "wfOpen": false}, "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"actualDefaultValue": "", "businessType": "text", "defaultField": false, "defaultValue": "", "defaultValueType": 0, "entityField": false, "fieldComment": "", "fieldFormat": "", "fieldLength": 20000, "fieldName": "document_content", "fieldShow": true, "fieldText": "文档内容", "fieldTrack": false, "fieldType": "text", "fileMultiple": "0", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": false, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"actualDefaultValue": "", "businessType": "text", "defaultField": false, "defaultValue": "", "defaultValueType": 0, "entityField": false, "fieldComment": "", "fieldFormat": "", "fieldLength": 20000, "fieldName": "text_content", "fieldShow": true, "fieldText": "文档文本内容", "fieldTrack": false, "fieldType": "text", "fileMultiple": "0", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": false, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}], "modelName": "chat_history_detail_excerpt", "modelText": "聊天记录明细引用", "modelType": 0, "tableName": "", "vn": 0, "wfOpen": false}