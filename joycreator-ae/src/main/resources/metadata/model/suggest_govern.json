{"description": "推荐管理配置", "draft": false, "editable": true, "edition": "v2", "isTree": false, "modelFields": [{"businessType": "number", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "主键id", "fieldFormat": "", "fieldLength": 64, "fieldName": "id", "fieldShow": false, "fieldText": "主键id", "fieldTrack": false, "fieldType": "number", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": true, "maxFileCount": "1", "must": true, "outDbRule": "", "pointLength": 0, "relatedOriginModel": "0", "searchShow": true, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "string", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "名称", "fieldFormat": "", "fieldLength": 100, "fieldName": "name", "fieldShow": true, "fieldText": "推荐主题", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": true, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": true, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "user", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "归属人（erp）", "fieldFormat": "", "fieldLength": 64, "fieldName": "owner_id", "fieldShow": false, "fieldText": "归属人", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": true, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "dateTime", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "创建时间", "fieldFormat": "", "fieldLength": 0, "fieldName": "create_time", "fieldShow": false, "fieldText": "创建时间", "fieldTrack": false, "fieldType": "date", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": true, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "dateTime", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "更新时间", "fieldFormat": "", "fieldLength": 0, "fieldName": "update_time", "fieldShow": false, "fieldText": "更新时间", "fieldTrack": false, "fieldType": "date", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": true, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "user", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "创建人", "fieldFormat": "", "fieldLength": 100, "fieldName": "created_user", "fieldShow": false, "fieldText": "创建人", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": true, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "user", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "修改人", "fieldFormat": "", "fieldLength": 100, "fieldName": "modified_user", "fieldShow": false, "fieldText": "修改人", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": true, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "checkBox", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "是否有效(1:有效,0:无效)", "fieldFormat": "", "fieldLength": 1, "fieldName": "yn", "fieldShow": false, "fieldText": "是否有效", "fieldTrack": false, "fieldType": "boolean", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": true, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"actualDefaultValue": "Y", "businessType": "select", "combinationRule": "", "defaultField": false, "defaultSelect": "[{\"label\":\"是\",\"value\":\"Y\",\"icon\":\"\",\"iconBgColor\":\"\",\"iconUrl\":\"\",\"default\":true,\"enable\":true},{\"label\":\"否\",\"value\":\"N\",\"icon\":\"\",\"iconBgColor\":\"\",\"iconUrl\":\"\",\"default\":false,\"enable\":true}]", "defaultValue": "Y", "defaultValueType": 0, "entityField": false, "fieldComment": "", "fieldFormat": "{\"showType\":\"select\"}", "fieldLength": 64, "fieldName": "is_enabled", "fieldShow": true, "fieldText": "是否启用", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "fileSuffix": "[]", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "matchQueryField": "", "maxFileCount": "1", "must": true, "outDbRule": "", "preFieldName": "", "queryConditions": "[]", "relatedOriginModel": "0", "searchShow": false, "selectDataJson": "[]", "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "number", "defaultField": false, "defaultValue": "", "defaultValueType": 0, "entityField": false, "fieldComment": "", "fieldFormat": "", "fieldLength": 10, "fieldName": "priority", "fieldShow": true, "fieldText": "推荐优先级", "fieldTrack": false, "fieldType": "number", "fileMultiple": "0", "fileSuffix": "[]", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": true, "outDbRule": "", "pointLength": 2, "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": true}, {"actualDefaultValue": "", "businessType": "text", "defaultField": false, "defaultValue": "", "defaultValueType": 0, "entityField": false, "fieldComment": "", "fieldFormat": "", "fieldLength": 2000, "fieldName": "preset_prompt", "fieldShow": true, "fieldText": "预置提示词", "fieldTrack": false, "fieldType": "text", "fileMultiple": "0", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"actualDefaultValue": "#FFA641", "businessType": "string", "defaultField": false, "defaultValue": "#FFA641", "defaultValueType": 0, "entityField": false, "fieldComment": "", "fieldFormat": "", "fieldLength": 64, "fieldName": "background", "fieldShow": true, "fieldText": "图标背景色", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": false, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"actualDefaultValue": "", "businessType": "string", "defaultField": false, "defaultValue": "", "defaultValueType": 0, "entityField": false, "fieldComment": "", "fieldFormat": "", "fieldLength": 64, "fieldName": "recommendation_category_enum", "fieldShow": true, "fieldText": "推荐分类枚举", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": false, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"actualDefaultValue": "", "businessType": "string", "defaultField": false, "defaultValue": "", "defaultValueType": 0, "entityField": false, "fieldComment": "", "fieldFormat": "{\"showType\":\"select\"}", "fieldLength": 256, "fieldName": "category", "fieldShow": true, "fieldText": "推荐分类", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "fileSuffix": "[]", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataJson": "[]", "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"actualDefaultValue": "", "businessType": "text", "defaultField": false, "defaultValue": "", "defaultValueType": 0, "entityField": false, "fieldComment": "", "fieldFormat": "", "fieldLength": 500, "fieldName": "recommendation_description", "fieldShow": true, "fieldText": "推荐描述", "fieldTrack": false, "fieldType": "text", "fileMultiple": "0", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"actualDefaultValue": "", "businessType": "string", "defaultField": false, "defaultValue": "", "defaultValueType": 0, "entityField": false, "fieldComment": "", "fieldFormat": "", "fieldLength": 512, "fieldName": "icon", "fieldShow": true, "fieldText": "推荐图标", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}], "modelName": "suggest_govern", "modelText": "推荐管理", "modelType": 0, "tableName": "", "vn": 0, "wfOpen": false}