{"description": "", "draft": false, "editable": true, "edition": "v1", "isTree": false, "modelFields": [{"businessType": "number", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "主键id", "fieldFormat": "", "fieldLength": 32, "fieldName": "id", "fieldShow": false, "fieldText": "主键id", "fieldTrack": false, "fieldType": "number", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": true, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": true, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": true}, {"businessType": "string", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "名称", "fieldFormat": "", "fieldLength": 100, "fieldName": "name", "fieldShow": true, "fieldText": "名称", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": true, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": true, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": true}, {"businessType": "dateTime", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "创建时间", "fieldFormat": "", "fieldLength": 0, "fieldName": "create_time", "fieldShow": false, "fieldText": "创建时间", "fieldTrack": false, "fieldType": "date", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": true, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": true, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": true}, {"businessType": "dateTime", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "更新时间", "fieldFormat": "", "fieldLength": 0, "fieldName": "update_time", "fieldShow": false, "fieldText": "更新时间", "fieldTrack": false, "fieldType": "date", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": true, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": true, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": true}, {"businessType": "string", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "创建人", "fieldFormat": "", "fieldLength": 100, "fieldName": "created_user", "fieldShow": false, "fieldText": "创建人", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": true, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": true, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": true}, {"businessType": "string", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "修改人", "fieldFormat": "", "fieldLength": 100, "fieldName": "modified_user", "fieldShow": false, "fieldText": "修改人", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": true, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": true, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": true}, {"businessType": "checkBox", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "是否有效(1:有效,0:无效)", "fieldFormat": "", "fieldLength": 1, "fieldName": "yn", "fieldShow": false, "fieldText": "是否有效", "fieldTrack": false, "fieldType": "boolean", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": true, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": true, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": true}, {"businessType": "number", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "版本号", "fieldFormat": "", "fieldLength": 10, "fieldName": "vn", "fieldShow": false, "fieldText": "版本号", "fieldTrack": false, "fieldType": "number", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": true, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": true, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": true}, {"businessType": "text", "defaultField": false, "defaultValueType": 0, "entityField": false, "fieldComment": "", "fieldFormat": "", "fieldName": "data_info", "fieldShow": true, "fieldText": "暂存的数据", "fieldTrack": false, "fieldType": "text", "fileMultiple": "0", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": true, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": true}, {"actualDefaultValue": "", "businessType": "string", "defaultField": false, "defaultValue": "", "defaultValueType": 0, "entityField": false, "fieldComment": "", "fieldFormat": "", "fieldLength": 200, "fieldName": "model_name", "fieldShow": true, "fieldText": "模型", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": true, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "relationshipRule": "", "searchShow": true, "selectDataSource": 0, "syncFieldData": false, "syncModify": false, "tableFieldName": "", "thousandsSeparator": true}], "modelName": "common_draft", "modelText": " 暂存箱", "modelType": 1, "tableName": "", "vn": 0, "wfOpen": false}