{"description": "", "draft": false, "editable": true, "edition": "v1", "isTree": false, "modelFields": [{"businessType": "number", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "主键id", "fieldFormat": "", "fieldName": "id", "fieldShow": true, "fieldText": "主键id", "fieldTrack": false, "fieldType": "number", "fileMultiple": "0", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "string", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "名称", "fieldFormat": "", "fieldName": "name", "fieldShow": true, "fieldText": "名称", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "dateTime", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "创建时间", "fieldFormat": "", "fieldName": "create_time", "fieldShow": true, "fieldText": "创建时间", "fieldTrack": false, "fieldType": "date", "fileMultiple": "0", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "dateTime", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "更新时间", "fieldFormat": "", "fieldName": "update_time", "fieldShow": true, "fieldText": "更新时间", "fieldTrack": false, "fieldType": "date", "fileMultiple": "0", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "string", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "创建人", "fieldFormat": "", "fieldName": "created_user", "fieldShow": true, "fieldText": "创建人", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "string", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "修改人", "fieldFormat": "", "fieldName": "modified_user", "fieldShow": true, "fieldText": "修改人", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "checkBox", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "是否有效(1:有效,0:无效)", "fieldFormat": "", "fieldName": "yn", "fieldShow": true, "fieldText": "是否有效", "fieldTrack": false, "fieldType": "boolean", "fileMultiple": "0", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "number", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "版本号", "fieldFormat": "", "fieldName": "vn", "fieldShow": true, "fieldText": "版本号", "fieldTrack": false, "fieldType": "number", "fileMultiple": "0", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"actualDefaultValue": "", "businessType": "string", "combinationRule": "", "cumulativeType": "COUNT", "defaultField": false, "defaultValue": "", "defaultValueType": 0, "entityField": false, "fieldComment": "", "fieldFormat": "", "fieldName": "field_key", "fieldShow": true, "fieldText": "需要自增的字段", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "formulaReturnType": "", "formulaValue": "", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": true, "maxFileCount": "1", "must": true, "outDbRule": "", "queryConditions": "[]", "relatedOriginModel": "0", "relationshipRule": "", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "syncModify": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "number", "combinationRule": "", "cumulativeType": "COUNT", "defaultField": false, "defaultValue": "", "defaultValueType": 0, "entityField": false, "fieldComment": "", "fieldFormat": "", "fieldName": "start_num", "fieldShow": true, "fieldText": "起始值", "fieldTrack": false, "fieldType": "number", "fileMultiple": "0", "formulaReturnType": "", "formulaValue": "", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": true, "maxFileCount": "1", "must": true, "outDbRule": "", "queryConditions": "[]", "relatedOriginModel": "0", "relationshipRule": "", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "syncModify": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "number", "combinationRule": "", "cumulativeType": "COUNT", "defaultField": false, "defaultValue": "", "defaultValueType": 0, "entityField": false, "fieldComment": "", "fieldFormat": "", "fieldName": "max_num", "fieldShow": true, "fieldText": "最大值", "fieldTrack": false, "fieldType": "number", "fileMultiple": "0", "formulaReturnType": "", "formulaValue": "", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": true, "maxFileCount": "1", "must": true, "outDbRule": "", "queryConditions": "[]", "relatedOriginModel": "0", "relationshipRule": "", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "syncModify": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "number", "combinationRule": "", "cumulativeType": "COUNT", "defaultField": false, "defaultValue": "", "defaultValueType": 0, "entityField": false, "fieldComment": "", "fieldFormat": "", "fieldName": "current_num", "fieldShow": true, "fieldText": "当前值", "fieldTrack": false, "fieldType": "number", "fileMultiple": "0", "formulaReturnType": "", "formulaValue": "", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": true, "maxFileCount": "1", "must": true, "outDbRule": "", "queryConditions": "[]", "relatedOriginModel": "0", "relationshipRule": "", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "syncModify": false, "tableFieldName": "", "thousandsSeparator": false}], "modelIndices": [{"indexCode": "uniq_field_key", "indexField": "[\"field_key\"]", "indexName": "uniq_field_key"}], "modelName": "common_auto_increment", "modelText": "自增字段表", "modelType": 1, "tableName": "", "vn": 0, "wfOpen": false}