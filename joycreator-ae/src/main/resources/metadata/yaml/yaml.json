[{"pre": {"server": {"port": 8091}, "logging": {"config": "classpath:log/log4j2.yaml"}, "app4s": {"application": {"id": "2943"}, "datahub": {"open": false, "env": "dev"}, "zk": {"url": "testpubli.zk.jddb.com:2181", "namespace": "publictest", "userName": "admin", "password": "admin888"}, "useCodeTemplate": false, "permissionInterceptor": "com.jd.jdt.app4s.permission.embed.interceptor.JoyBuilderPermissionEmbedInterceptor", "permissionType": "IPMS", "permissionAutoSave": "user"}, "spring": {"datasource": {"type": "com.alibaba.druid.pool.DruidDataSource", "druid": {"driverClassName": "com.p6spy.engine.spy.P6SpyDriver", "url": "************************************************************************************************************************************************************************************************************************************", "username": "y_joycreator_pre", "password": "rQdR/owTju3D5IVuxeAiVQ=="}}}, "workflow": {"alias": "pro-operateFlowApi:0.0.1"}, "rpc": {"llm": {"url": "http://gpt-proxy.jd.com", "apiKey": "aa051d32-db0b-4294-98a0-b02040240d48", "jdcloudPin": "jcloud_doNGoJh"}, "office": {"url": "http://joyoffice.jd.com"}}, "oss": {"accessKey": "RE3LMZKZ4PD833HFTLYKD2XP3LH46GO0", "secretKey": "7KQWTOYZBJZ9WWNIK9J8PBCQQXR4KNYK", "endpoint": "https://s3.jdpay.com", "intranetEndpoint": "http://s3-internal-office-tech-north-1.jdfmgt.com", "bucket": "joycreator-pre", "protocol": "http"}, "r2m": {"appName": "xinghui_r2m", "3cConnectionStr": "r2m3c.jdfin.local", "3cToken": "a4bc676ca4", "password": "UiZ5p7e1MfgMqVX3KnYxS5JcV+Y376kCr2iQ4OsNmYU="}, "ip": {"whitelist": "************"}}, "prod": {"server": {"port": 8091}, "logging": {"config": "classpath:log/log4j2.yaml"}, "app4s": {"application": {"id": "2943"}, "datahub": {"open": false, "env": "dev"}, "zk": {"url": "testpubli.zk.jddb.com:2181", "namespace": "publictest", "userName": "admin", "password": "admin888"}, "useCodeTemplate": false, "permissionInterceptor": "com.jd.jdt.app4s.permission.embed.interceptor.JoyBuilderPermissionEmbedInterceptor", "permissionType": "IPMS", "permissionAutoSave": "user"}, "spring": {"datasource": {"type": "com.alibaba.druid.pool.DruidDataSource", "druid": {"driverClassName": "com.p6spy.engine.spy.P6SpyDriver", "url": "*****************************************************************************************************************************************************************************************************************************", "username": "joy_creator", "password": "Ed8XjEGtRaHeIfTRGcoBUw=="}}}, "workflow": {"alias": "pro-operateFlowApi:0.0.1"}, "rpc": {"llm": {"url": "http://gpt-proxy.jd.com", "apiKey": "aa051d32-db0b-4294-98a0-b02040240d48", "jdcloudPin": "jcloud_doNGoJh"}, "office": {"url": "http://joyoffice.jd.com"}}, "oss": {"accessKey": "A25E92A1A3D6433D8033EBF93307F2B0", "secretKey": "7A1C958DC39F415EA4C7631D98C1A83E", "endpoint": "https://s3.jdpay.com", "intranetEndpoint": "http://s3-internal-office-tech-north-1.jdfmgt.com", "bucket": "joycreator", "protocol": "http"}, "r2m": {"appName": "xinghui_r2m", "3cConnectionStr": "r2m3c.jdfin.local", "3cToken": "a4bc676ca4", "password": "UiZ5p7e1MfgMqVX3KnYxS5JcV+Y376kCr2iQ4OsNmYU="}, "ip": {"whitelist": "************"}}, "dev": {"server": {"port": 8091}, "logging": {"config": "classpath:log/log4j2.yaml"}, "app4s": {"application": {"id": "2943"}, "datahub": {"open": false, "env": "dev"}, "zk": {"url": "testpubli.zk.jddb.com:2181", "namespace": "publictest", "userName": "admin", "password": "admin888"}, "useCodeTemplate": false, "permissionType": "IPMS", "permissionInterceptor": "com.jd.jdt.app4s.permission.embed.interceptor.JoyBuilderPermissionEmbedInterceptor", "permissionAutoSave": "user"}, "spring": {"datasource": {"type": "com.alibaba.druid.pool.DruidDataSource", "druid": {"driverClassName": "com.p6spy.engine.spy.P6SpyDriver", "url": "***********************************************************************************************************************************************************************************************************************************************", "username": "on_apaas_dev_ddl", "password": "X15RCWfoS0KIoaixa6jzBA=="}}}, "workflow": {"alias": "test-operateFlowApi:0.0.1"}, "file": {"endpoint": "s3.cn-north-1.jdcloud-oss.com", "bucket": "oss-joybuilder", "accessKey": "JDC_585715941E261CEB3F4836A8F9D7", "secretKey": "5BC227332D86536A1713E8A750854259"}, "rpc": {"llm": {"url": "http://gpt-proxy.jd.com", "apiKey": "aa051d32-db0b-4294-98a0-b02040240d48", "jdcloudPin": "jcloud_doNGoJh"}, "office": {"url": "http://joyoffice.jd.com"}}, "oss": {"accessKey": "RE3LMZKZ4PD833HFTLYKD2XP3LH46GO0", "secretKey": "7KQWTOYZBJZ9WWNIK9J8PBCQQXR4KNYK", "endpoint": "https://s3.jdpay.com", "intranetEndpoint": "http://s3-internal-office-tech-north-1.jdfmgt.com", "bucket": "joycreator-pre", "protocol": "http"}, "r2m": {"appName": "xinghui_r2m", "3cConnectionStr": "r2m3c.jdfin.local", "3cToken": "a4bc676ca4", "password": "UiZ5p7e1MfgMqVX3KnYxS5JcV+Y376kCr2iQ4OsNmYU="}, "ip": {"whitelist": "************"}}, "test": {"server": {"port": 8091}, "logging": {"config": "classpath:log/log4j2.yaml"}, "app4s": {"application": {"id": "2943"}, "datahub": {"open": false, "env": "dev"}, "zk": {"url": "testpubli.zk.jddb.com:2181", "namespace": "publictest", "userName": "admin", "password": "admin888"}, "useCodeTemplate": false, "permissionType": "IPMS", "permissionInterceptor": "com.jd.jdt.app4s.permission.embed.interceptor.JoyBuilderPermissionEmbedInterceptor", "permissionAutoSave": "user"}, "spring": {"datasource": {"type": "com.alibaba.druid.pool.DruidDataSource", "druid": {"driverClassName": "com.p6spy.engine.spy.P6SpyDriver", "url": "***********************************************************************************************************************************************************************************************************************************************", "username": "on_apaas_dev_ddl", "password": "X15RCWfoS0KIoaixa6jzBA=="}}}, "workflow": {"alias": "test-operateFlowApi:0.0.1"}, "file": {"endpoint": "s3.cn-north-1.jdcloud-oss.com", "bucket": "oss-joybuilder", "accessKey": "JDC_585715941E261CEB3F4836A8F9D7", "secretKey": "5BC227332D86536A1713E8A750854259"}, "rpc": {"llm": {"url": "http://gpt-proxy.jd.com", "apiKey": "aa051d32-db0b-4294-98a0-b02040240d48", "jdcloudPin": "jcloud_doNGoJh"}, "office": {"url": "http://joyoffice.jd.com"}}, "oss": {"accessKey": "RE3LMZKZ4PD833HFTLYKD2XP3LH46GO0", "secretKey": "7KQWTOYZBJZ9WWNIK9J8PBCQQXR4KNYK", "endpoint": "https://s3.jdpay.com", "intranetEndpoint": "http://s3-internal-office-tech-north-1.jdfmgt.com", "bucket": "joycreator-dev", "protocol": "http"}, "r2m": {"appName": "jd-tob-server", "3cConnectionStr": "r2m3c.jdd-beta.local:1025", "3cToken": "a3725e24c4", "password": "d3vKN3PGWpevTB9v"}, "ip": {"whitelist": "************"}}, "local": {"server": {"port": 8091}, "logging": {"config": "classpath:log/log4j2.yaml"}, "app4s": {"application": {"id": "2943"}, "datahub": {"open": false, "env": "local"}, "zk": {"url": "testpubli.zk.jddb.com:2181", "namespace": "publictest", "userName": "admin", "password": "admin888"}, "useCodeTemplate": false, "permissionInterceptor": "com.jd.jdt.app4s.permission.embed.interceptor.JoyBuilderPermissionEmbedInterceptor", "permissionType": "IPMS", "permissionAutoSave": "user"}, "spring": {"datasource": {"p6spy": true, "type": "com.alibaba.druid.pool.DruidDataSource", "druid": {"driverClassName": "com.p6spy.engine.spy.P6SpyDriver", "url": "*******************************************************************************************************************************************************************************************************************", "username": "c_xingkong_lcKEx", "password": "2VvKnmXy7oUj1rRmjPOontbGBTAj7U8YwwiX5/8dkqw="}}}, "workflow": {"alias": "test-operateFlowApi:0.0.1"}, "rpc": {"llm": {"url": "http://gpt-proxy.jd.com", "apiKey": "aa051d32-db0b-4294-98a0-b02040240d48", "jdcloudPin": "jcloud_doNGoJh"}, "office": {"url": "http://joyoffice.jd.com"}}, "oss": {"accessKey": "A25E92A1A3D6433D8033EBF93307F2B0", "secretKey": "7A1C958DC39F415EA4C7631D98C1A83E", "endpoint": "https://s3.jdpay.com", "intranetEndpoint": "http://s3-internal-office-tech-north-1.jdfmgt.com", "bucket": "joy-contract", "protocol": "http"}, "office": {"url": "http://**************:8080"}, "r2m": {"appName": "jd-tob-server", "3cConnectionStr": "r2m-3c-dev.jd.local", "3cToken": "a3725e24c4", "password": "9vPeb7uo95klmBKC"}, "ip": {"whitelist": "************"}}}]