[{"combinationRule": "", "defaultState": true, "editable": true, "editorViewCode": "962022550917718019", "editorViewName": "全部", "enableState": true, "freezeFirstColumn": true, "freezeOperateColumn": true, "keyWordList": [{"fieldName": "name"}], "modelName": "dict", "openCombinationRule": false, "pageKey": "962022550896746498", "showIndex": 0, "system": true, "viewInfoList": [{"displayWidth": "", "fieldName": "name", "isEnable": true, "showIndex": 1, "union": false}, {"displayWidth": "", "fieldName": "create_time", "isEnable": true, "showIndex": 2, "union": false}, {"displayWidth": "", "fieldName": "update_time", "isEnable": true, "showIndex": 3, "union": false}, {"displayWidth": "", "fieldName": "created_user", "isEnable": true, "showIndex": 4, "union": false}, {"displayWidth": "", "fieldName": "modified_user", "isEnable": true, "showIndex": 5, "union": false}, {"displayWidth": "", "fieldName": "dict_code", "isEnable": true, "showIndex": 6, "union": false}], "viewPlanInfoList": [{"conditionsJson": "{\"fieldName\":\"name\",\"valueType\":0,\"conditionType\":\"LIKE\",\"value\":\"\",\"isShow\":true}", "fieldName": "name", "showIndex": 1}, {"conditionsJson": "{\"fieldName\":\"dict_code\",\"valueType\":0,\"conditionType\":\"LIKE\",\"value\":\"\",\"isShow\":true}", "fieldName": "dict_code", "showIndex": 2}], "viewSortList": [{"fieldName": "update_time", "showIndex": 0, "sortOrder": "desc"}], "viewType": 0}]