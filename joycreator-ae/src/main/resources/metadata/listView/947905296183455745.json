[{"combinationRule": "", "defaultState": true, "editable": true, "editorViewCode": "947905296196038658", "editorViewName": "全部", "enableState": true, "freezeFirstColumn": true, "freezeOperateColumn": true, "keyWordList": [{"fieldName": "name"}], "modelName": "chat_history", "openCombinationRule": false, "pageKey": "947905296183455745", "showIndex": 0, "system": true, "viewInfoList": [{"displayWidth": "", "fieldName": "name", "isEnable": true, "showIndex": 1, "union": false}, {"displayWidth": "", "fieldName": "create_time", "isEnable": true, "showIndex": 2, "union": false}, {"displayWidth": "", "fieldName": "update_time", "isEnable": true, "showIndex": 3, "union": false}, {"displayWidth": "", "fieldName": "created_user", "isEnable": true, "showIndex": 4, "union": false}, {"displayWidth": "", "fieldName": "modified_user", "isEnable": true, "showIndex": 5, "union": false}, {"displayWidth": "", "fieldName": "erp", "isEnable": true, "showIndex": 6, "union": false}, {"displayWidth": "", "fieldName": "session_id", "isEnable": true, "showIndex": 7, "union": false}], "viewPlanInfoList": [{"conditionsJson": "{\"fieldName\":\"name\",\"valueType\":0,\"conditionType\":\"LIKE\",\"value\":\"\",\"isShow\":true}", "fieldName": "name", "showIndex": 1}, {"conditionsJson": "{\"fieldName\":\"erp\",\"valueType\":0,\"conditionType\":\"EQ\",\"value\":\"\",\"isShow\":true}", "fieldName": "erp", "showIndex": 2}, {"conditionsJson": "{\"fieldName\":\"session_id\",\"valueType\":0,\"conditionType\":\"LIKE\",\"value\":\"\",\"isShow\":true}", "fieldName": "session_id", "showIndex": 3}], "viewSortList": [{"fieldName": "update_time", "showIndex": 0, "sortOrder": "desc"}], "viewType": 0}]