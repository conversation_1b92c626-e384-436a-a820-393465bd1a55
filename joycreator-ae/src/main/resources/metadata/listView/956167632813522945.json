[{"combinationRule": "", "defaultState": true, "editable": true, "editorViewCode": "956167632830300163", "editorViewName": "全部", "enableState": true, "freezeFirstColumn": true, "freezeOperateColumn": true, "keyWordList": [{"fieldName": "name"}], "modelName": "prompt_words_config", "openCombinationRule": false, "pageKey": "956167632813522945", "showIndex": 0, "system": true, "viewInfoList": [{"displayWidth": "", "fieldName": "name", "isEnable": true, "showIndex": 0, "union": false}, {"displayWidth": "", "fieldName": "create_time", "isEnable": true, "showIndex": 1, "union": false}, {"displayWidth": "", "fieldName": "update_time", "isEnable": true, "showIndex": 2, "union": false}, {"displayWidth": "", "fieldName": "created_user", "isEnable": true, "showIndex": 3, "union": false}, {"displayWidth": "", "fieldName": "modified_user", "isEnable": true, "showIndex": 4, "union": false}, {"displayWidth": "", "fieldName": "prompt_word_code", "isEnable": true, "showIndex": 5, "union": false}, {"displayWidth": "", "fieldName": "model", "isEnable": true, "showIndex": 6, "union": false}, {"displayWidth": "", "fieldName": "prompt_word", "isEnable": true, "showIndex": 7, "union": false}, {"displayWidth": "", "fieldName": "description", "isEnable": true, "showIndex": 8, "union": false}, {"displayWidth": "", "fieldName": "temperature", "isEnable": true, "showIndex": 9, "union": false}, {"displayWidth": "", "fieldName": "top_k", "isEnable": true, "showIndex": 10, "union": false}, {"displayWidth": "", "fieldName": "max_tokens", "isEnable": true, "showIndex": 11, "union": false}], "viewPlanInfoList": [{"conditionsJson": "{\"fieldName\":\"name\",\"valueType\":0,\"conditionType\":\"LIKE\",\"value\":\"\",\"isShow\":true}", "fieldName": "name", "showIndex": 1}, {"conditionsJson": "{\"fieldName\":\"prompt_word_code\",\"valueType\":0,\"conditionType\":\"LIKE\",\"value\":\"\",\"isShow\":true}", "fieldName": "prompt_word_code", "showIndex": 2}, {"conditionsJson": "{\"fieldName\":\"model\",\"valueType\":0,\"conditionType\":\"IN\",\"value\":\"\",\"isShow\":true}", "fieldName": "model", "showIndex": 3}, {"conditionsJson": "{\"fieldName\":\"prompt_word\",\"valueType\":0,\"conditionType\":\"LIKE\",\"value\":\"\",\"isShow\":true}", "fieldName": "prompt_word", "showIndex": 4}, {"conditionsJson": "{\"fieldName\":\"description\",\"valueType\":0,\"conditionType\":\"LIKE\",\"value\":\"\",\"isShow\":true}", "fieldName": "description", "showIndex": 5}], "viewSortList": [{"fieldName": "update_time", "showIndex": 0, "sortOrder": "desc"}], "viewType": 0}]