[{"combinationRule": "", "componentCode": "ipms@1.0.0", "defaultState": true, "editable": false, "editorViewCode": "888024611684769794", "editorViewName": "全部", "enableState": true, "freezeFirstColumn": true, "freezeOperateColumn": true, "keyWordList": [{"fieldName": "name"}, {"fieldName": "login_name"}], "modelName": "ipms_user", "openCombinationRule": false, "pageKey": "888024611672186882", "showIndex": 0, "system": true, "viewInfoList": [{"displayWidth": "", "fieldName": "login_name", "isEnable": true, "showIndex": 0, "union": false}, {"displayWidth": "", "fieldName": "name", "isEnable": true, "showIndex": 1, "union": false}, {"displayWidth": "", "fieldName": "office_organization", "isEnable": true, "relatedFieldList": [{"displayWidth": "", "fieldName": "office_organization", "isEnable": true, "relatedFieldName": "full_path_name", "relatedModelName": "ipms_office_organization", "showIndex": 2}], "showIndex": 2, "union": true}], "viewPlanInfoList": [{"conditionsJson": "{\"fieldName\":\"name\",\"valueType\":0,\"conditionType\":\"LIKE\",\"value\":\"\",\"isShow\":true}", "fieldName": "name", "showIndex": 1}, {"conditionsJson": "{\"fieldName\":\"vn\",\"valueType\":0,\"conditionType\":\"EQ\",\"value\":\"\",\"isShow\":true}", "fieldName": "vn", "showIndex": 2}, {"conditionsJson": "{\"fieldName\":\"login_name\",\"valueType\":0,\"conditionType\":\"LIKE\",\"value\":\"\",\"isShow\":true}", "fieldName": "login_name", "showIndex": 3}, {"conditionsJson": "{\"fieldName\":\"phone\",\"valueType\":0,\"conditionType\":\"LIKE\",\"value\":\"\",\"isShow\":true}", "fieldName": "phone", "showIndex": 4}, {"conditionsJson": "{\"fieldName\":\"fax\",\"valueType\":0,\"conditionType\":\"LIKE\",\"value\":\"\",\"isShow\":true}", "fieldName": "fax", "showIndex": 5}, {"conditionsJson": "{\"fieldName\":\"mobile\",\"valueType\":0,\"conditionType\":\"LIKE\",\"value\":\"\",\"isShow\":true}", "fieldName": "mobile", "showIndex": 6}, {"conditionsJson": "{\"fieldName\":\"email\",\"valueType\":0,\"conditionType\":\"LIKE\",\"value\":\"\",\"isShow\":true}", "fieldName": "email", "showIndex": 7}, {"conditionsJson": "{\"fieldName\":\"title\",\"valueType\":0,\"conditionType\":\"LIKE\",\"value\":\"\",\"isShow\":true}", "fieldName": "title", "showIndex": 8}, {"conditionsJson": "{\"fieldName\":\"titlecode\",\"valueType\":0,\"conditionType\":\"LIKE\",\"value\":\"\",\"isShow\":true}", "fieldName": "titlecode", "showIndex": 9}, {"conditionsJson": "{\"fieldName\":\"office_organization.full_path_name\",\"valueType\":0,\"conditionType\":\"EQ\",\"value\":\"\",\"isShow\":true}", "fieldName": "office_organization", "showIndex": 10}, {"conditionsJson": "{\"fieldName\":\"isusing\",\"valueType\":0,\"conditionType\":\"EQ\",\"value\":\"\",\"isShow\":true}", "fieldName": "isusing", "showIndex": 11}, {"conditionsJson": "{\"fieldName\":\"pwdd\",\"valueType\":0,\"conditionType\":\"LIKE\",\"value\":\"\",\"isShow\":true}", "fieldName": "pwdd", "showIndex": 12}], "viewSortList": [{"fieldName": "update_time", "showIndex": 0, "sortOrder": "desc"}], "viewType": 0}]