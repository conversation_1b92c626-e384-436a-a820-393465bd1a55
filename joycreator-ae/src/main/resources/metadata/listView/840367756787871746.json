[{"combinationRule": "", "componentCode": "ipms@1.0.0", "defaultState": true, "editable": false, "editorViewCode": "840367756792066051", "editorViewName": "全部", "enableState": true, "freezeFirstColumn": true, "freezeOperateColumn": true, "keyWordList": [{"fieldName": "id"}, {"fieldName": "name"}], "modelName": "ipms_business_organization", "openCombinationRule": false, "pageKey": "840367756787871746", "showIndex": 0, "system": true, "viewInfoList": [{"displayWidth": "", "fieldName": "name", "isEnable": true, "showIndex": 1, "union": false}, {"displayWidth": "", "fieldName": "create_time", "isEnable": true, "showIndex": 2, "union": false}, {"displayWidth": "", "fieldName": "update_time", "isEnable": true, "showIndex": 3, "union": false}, {"displayWidth": "", "fieldName": "created_user", "isEnable": true, "showIndex": 4, "union": false}, {"displayWidth": "", "fieldName": "modified_user", "isEnable": true, "showIndex": 5, "union": false}, {"displayWidth": "", "fieldName": "parent_id", "isEnable": true, "showIndex": 6, "union": false}, {"displayWidth": "", "fieldName": "order", "isEnable": true, "showIndex": 7, "union": false}, {"displayWidth": "", "fieldName": "leaf", "isEnable": true, "showIndex": 8, "union": false}, {"displayWidth": "", "fieldName": "full_path_name", "isEnable": true, "showIndex": 9, "union": false}, {"displayWidth": "", "fieldName": "full_path_code", "isEnable": true, "showIndex": 10, "union": false}, {"displayWidth": "", "fieldName": "remark", "isEnable": true, "showIndex": 11, "union": false}], "viewPlanInfoList": [{"conditionsJson": "{\"fieldName\":\"name\",\"valueType\":0,\"conditionType\":\"LIKE\",\"value\":\"\",\"isShow\":true}", "fieldName": "name", "showIndex": 1}, {"conditionsJson": "{\"fieldName\":\"parent_id.name\",\"valueType\":0,\"conditionType\":\"EQ\",\"value\":\"\",\"isShow\":true}", "fieldName": "parent_id", "showIndex": 2}, {"conditionsJson": "{\"fieldName\":\"order\",\"valueType\":0,\"conditionType\":\"EQ\",\"value\":\"\",\"isShow\":true}", "fieldName": "order", "showIndex": 3}, {"conditionsJson": "{\"fieldName\":\"leaf\",\"valueType\":0,\"conditionType\":\"EQ\",\"value\":\"\",\"isShow\":true}", "fieldName": "leaf", "showIndex": 4}, {"conditionsJson": "{\"fieldName\":\"full_path_name\",\"valueType\":0,\"conditionType\":\"LIKE\",\"value\":\"\",\"isShow\":true}", "fieldName": "full_path_name", "showIndex": 5}, {"conditionsJson": "{\"fieldName\":\"full_path_code\",\"valueType\":0,\"conditionType\":\"LIKE\",\"value\":\"\",\"isShow\":true}", "fieldName": "full_path_code", "showIndex": 6}, {"conditionsJson": "{\"fieldName\":\"remark\",\"valueType\":0,\"conditionType\":\"LIKE\",\"value\":\"\",\"isShow\":true}", "fieldName": "remark", "showIndex": 7}], "viewSortList": [{"fieldName": "update_time", "showIndex": 0, "sortOrder": "desc"}], "viewType": 0}]