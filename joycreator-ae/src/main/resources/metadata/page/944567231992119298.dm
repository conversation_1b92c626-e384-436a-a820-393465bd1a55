{
	"usePlatform":"",
	"enableAdvanced":true,
	"editable":true,
	"pageTemplate":"PC标准增删改查模板",
	"edition":"v2",
	"pageKey":"944567231992119298",
	"pageSettingDsl":"{\"detailPath\":\"/template_library/detail\",\"formPath\":\"/template_library/form\",\"listPath\":\"/template_library/list\",\"modelName\":\"template_library\",\"pageKey\":\"944567231992119298\",\"pagePath\":\"/template_library/list\"}",
	"pageName":"模板库_列表",
	"pageTemplateCode":"PCDefault",
	"modelName":"template_library",
	"pageType":"records_v4",
	"pageDsl":{
		"components":[
			{
				"componentType":"custom",
				"children":[
					{
						"componentName":"Include",
						"id":"Include-ZxhXTnGb",
						"props":{
							"v-loading":"${$.shareData.var_findRelationMultiInfo.loading}",
							":onLoad":"${function handler(){/*dm*/
\n$.$emit('findRelation',/*dm*/
\n  $.shareData.var_findRelationMultiInfo,/*dm*/
\n  $.shareData.var_activePlanView.conditionsValue[$.shareData.var_findRelationMultiInfo.index].value,/*dm*/
\n  $.shareData.var_pageTag/*dm*/
\n)/*dm*/
\n$.shareData.var_findRelationMultiInfoAction({/*dm*/
\n  loading: false/*dm*/
\n})}}",
							"run":"${$.shareData.var_findRelationMultiInfo.visible}",
							"pageId":"${() => {/*dm*/
\n  const { modelName, fieldName}= $.shareData.var_findRelationMultiInfo;/*dm*/
\n  const custom = $.shareData.var_setting?.fieldInfo?.[fieldName]?.url/*dm*/
\n  return custom || `/${modelName}/list`;/*dm*/
\n}}",
							"key":"${$.shareData.var_findRelationMultiInfo.modelName + $.shareData.var_findRelationMultiInfo.fieldName}"
						},
						"attrs":{
							"style":{
								"height":"50vh"
							}
						}
					},
					{
						"componentType":"custom",
						"children":[
							{
								"componentType":"custom",
								"action":{
									"fire":{
										"click":{
											"actions":[
												{
													"handler":"${function handler(){$.shareData.var_findRelationMultiInfoAction({/*dm*/
\n  visible: false/*dm*/
\n})/*dm*/
\n$.shareData.var_findRelatedFormAction(null);}}",
													"type":"function"
												}
											]
										}
									}
								},
								"componentName":"Button",
								"id":"Button-KNEFAZDd",
								"attrs":{},
								"props":{
									"size":"small",
									"children":"取消"
								}
							},
							{
								"componentType":"custom",
								"action":{
									"fire":{
										"click":{
											"actions":[
												{
													"handler":"${function Fun24728(){const { modelName, fieldName, index } = $.shareData.var_findRelationMultiInfo;/*dm*/
\n/*dm*/
\nconst newActionValue = $.shareData.var_findRelatedForm/*dm*/
\nif (newActionValue?.length) {/*dm*/
\n/*dm*/
\n  // 使用$set追加响应式/*dm*/
\n/*dm*/
\n  this.$set($.shareData.var_activePlanView.conditionsValue[index], 'value', newActionValue);/*dm*/
\n/*dm*/
\n  $.shareData.var_findRelationMultiInfoAction({/*dm*/
\n    visible: false/*dm*/
\n  })/*dm*/
\n  $.shareData.var_findRelatedFormAction(null);/*dm*/
\n/*dm*/
\n/*dm*/
\n} else {/*dm*/
\n  this.$message.error('请点击表格选择数据!');/*dm*/
\n}/*dm*/
\n}}",
													"type":"function"
												}
											]
										}
									}
								},
								"componentName":"Button",
								"id":"Button-pXbm27jj",
								"attrs":{},
								"props":{
									"size":"small",
									"children":"确定",
									"type":"primary"
								},
								"chosen":false
							}
						],
						"componentName":"DivWrapper",
						"id":"DivWrapper-D63EXchz",
						"props":{
							"name":"footer",
							"slot":"footer"
						}
					}
				],
				"action":{
					"fire":{
						"close":{
							"actions":[
								{
									"handler":"${function Fun50778(){$.$emit('findRelation', null)/*dm*/
\n$.shareData.var_findRelationMultiInfoAction({/*dm*/
\n  visible: false/*dm*/
\n})}}",
									"type":"function"
								}
							]
						}
					}
				},
				"componentName":"Dialog",
				"id":"Dialog-fcpWXGiQ",
				"title":"被关联模型",
				"key":"beiguanlianmoxing71321save",
				"props":{
					"modal-append-to-body":"${true}",
					"append-to-body":"${true}",
					"destroy-on-close":"${true}",
					"visible":"${$.shareData.var_findRelationMultiInfo.visible}",
					"width":"${\"60%\"}",
					"title":"${() => {/*dm*/
\n/*dm*/
\n  return `请选择【${$.shareData.var_findRelationMultiInfo.modelText}】`/*dm*/
\n}}",
					"close-on-click-modal":"",
					"close-on-press-escape":false,
					"zIndex":"${200}"
				}
			},
			{
				"componentType":"custom",
				"children":[
					{
						"libraryName":"@jd/joyui",
						"componentType":"UILibrary",
						"__setting":{
							"settingData":{
								"searchMethod":"advancedSearch",
								"showSearch":true,
								"showTable":true
							}
						},
						"children":[
							{
								"libraryName":"@jd/joyui",
								"componentType":"UILibrary",
								"children":[
									{
										"libraryName":"@jd/joyui",
										"componentType":"UILibrary",
										"action":{
											"fire":{
												"getTabsSearchRule":{
													"arguments":[
														"data"
													],
													"actions":[
														{
															"handler":"${function handler(){/*dm*/
\n                                if (!$.shareData.var_tabsSearchData) {/*dm*/
\n                                  return;/*dm*/
\n                                };/*dm*/
\n                                const {/*dm*/
\n                                  standardQueryConditions = [],/*dm*/
\n                                  ...rest/*dm*/
\n                                } = data/*dm*/
\n                                $.shareData.var_tabsSearchDataAction({/*dm*/
\n                                  ...rest,/*dm*/
\n                                  standardQueryConditions: standardQueryConditions.map(item => {/*dm*/
\n                                    if (item.value?.organizationCode) {/*dm*/
\n                                      item.value = item.value?.organizationCode || ''/*dm*/
\n                                    }/*dm*/
\n                                    return item;/*dm*/
\n                                  })/*dm*/
\n                                });/*dm*/
\n                                this.$refs.condition.search();/*dm*/
\n                              }}",
															"type":"function"
														}
													]
												},
												"gettabsActiveKey":{
													"arguments":[
														"data"
													],
													"actions":[
														{
															"handler":"${function handler(){/*dm*/
\n                                $.shareData.var_searchRuleValueAction(data)/*dm*/
\n                                $.functions.fun_loadTablePlanData();/*dm*/
\n                              }}",
															"type":"function"
														}
													]
												}
											}
										},
										"componentName":"Tab",
										"id":"Tab-KSc86aJn",
										"version":"1.8.3",
										"props":{
											"tabsActiveKeyValue":"${$.shareData.var_searchRuleValue}",
											"showPageTab":"${$.shareData.var_settingData.showPageTab}",
											"tabs":"${$.shareData.api_getConditionsPlan.data.yeqian}",
											"slot":"pageTab"
										}
									},
									{
										"libraryName":"@jd/joyui",
										"componentType":"UILibrary",
										"action":{
											"fire":{
												"changePlan":{
													"arguments":[
														"data"
													],
													"actions":[
														{
															"handler":"${function handler(data){// if (data.planCode == $.shareData.var_currentPlan.planCode) { return } /*dm*/
\n/*dm*/
\nif (data.editorViewCode === $.shareData.var_activePlanView.editorViewCode) {/*dm*/
\n  return/*dm*/
\n}/*dm*/
\n/*dm*/
\n$.functions.fun_setActiveView(data)/*dm*/
\n/*dm*/
\n// let res = [];/*dm*/
\n// if (data.conditionsJson) {/*dm*/
\n//   res = JSON.parse(data.conditionsJson)/*dm*/
\n// }/*dm*/
\n/*dm*/
\n// this.$refs.condition.queryConditions = [...res[0]?.standardQueryConditions] || [];/*dm*/
\n// this.$refs.condition.combinationRule = res[0]?.standardCombinationRule || '';/*dm*/
\nthis.$nextTick(async () => {/*dm*/
\n  await this.$refs.condition.search();/*dm*/
\n})}}",
															"type":"function"
														}
													]
												},
												"addPlan":{
													"arguments":[
														"data"
													],
													"actions":[
														{
															"handler":"${function Fun92329(data){/*dm*/
\n/*dm*/
\n/*dm*/
\nif (data.planName && data.planCode !== '') {/*dm*/
\n  await $.shareData.api_reViewNameAction({/*dm*/
\n    modelName: $.shareData.var_setting.modelName,/*dm*/
\n    pageKey: $.shareData.var_setting.pageKey,/*dm*/
\n    editorViewCode: data.editorViewCode,/*dm*/
\n    editorViewName: data.planName/*dm*/
\n  });/*dm*/
\n/*dm*/
\n  $.shareData.var_pageViewListAction($.shareData.var_pageViewList.map(item => {/*dm*/
\n    if (item.editorViewCode === data.editorViewCode) {/*dm*/
\n      return {/*dm*/
\n        ...item,/*dm*/
\n        label: data.planName,/*dm*/
\n        editorViewName: data.planName/*dm*/
\n      }/*dm*/
\n    }/*dm*/
\n    return item;/*dm*/
\n  }))/*dm*/
\n  return;/*dm*/
\n}/*dm*/
\n/*dm*/
\nconst newData = {/*dm*/
\n  editorViewCode: data.editorViewCode,/*dm*/
\n  editorViewName: data.planName,/*dm*/
\n  viewPlanInfoList: data.conditionsValue.map((item, index) => {/*dm*/
\n    return {/*dm*/
\n      fieldName: item.fieldName,/*dm*/
\n      conditionsJson: JSON.stringify(item),/*dm*/
\n      editorViewCode: data.editorViewCode,/*dm*/
\n      \"showIndex\": index/*dm*/
\n    }/*dm*/
\n  })/*dm*/
\n}/*dm*/
\n/*dm*/
\nconst info = await $.functions.fun_savePlanView(newData);/*dm*/
\nif (info.code === 200) {/*dm*/
\n  this.$message.success('保存成功');/*dm*/
\n  // await $.functions.fun_loadConditionPlanList();/*dm*/
\n  //  $.shareData.var_currentPlanAction({ ...list}); /*dm*/
\n} else {/*dm*/
\n  this.$message.error(info.msg);/*dm*/
\n}/*dm*/
\n/*dm*/
\n// let list = {/*dm*/
\n//   planCode: data.planCode,/*dm*/
\n//   name: data.planName || data.name,/*dm*/
\n//   modelName: data.modelName,/*dm*/
\n//   defaultPlan: data.defaultPlan,/*dm*/
\n//   conditionsJson: data.conditionsJson,/*dm*/
\n// };/*dm*/
\n// await $.shareData.api_saveConditionsPlanAction(list);/*dm*/
\n// if ($.shareData.api_saveConditionsPlan.code === 200) {/*dm*/
\n//   this.$message.success('保存成功');/*dm*/
\n//   await $.functions.fun_loadConditionPlanList();/*dm*/
\n//   $.shareData.var_currentPlanValueAction($.shareData.api_saveConditionsPlan.data);/*dm*/
\n//   let listInfo = { ...data, planCode: $.shareData.api_saveConditionsPlan.data };/*dm*/
\n//   let list = JSON.parse(data.conditionsJson);/*dm*/
\n//   this.$refs.condition.queryConditions = list[0]?.standardQueryConditions || [];/*dm*/
\n//   this.$refs.condition.combinationRule = list[0]?.standardCombinationRule || '';/*dm*/
\n//   this.$refs.condition.search();/*dm*/
\n//   await $.shareData.var_currentPlanAction(listInfo);/*dm*/
\n// }/*dm*/
\n}}",
															"type":"function"
														}
													]
												},
												"planDelte":{
													"arguments":[
														"data"
													],
													"actions":[
														{
															"handler":"${function handler(){// await $.shareData.api_deleteConditionsPlanAction({ planCode: data.planCode, modelName: $.shareData.var_setting.modelName });/*dm*/
\n/*dm*/
\nawait $.shareData.api_deleteViewAction({/*dm*/
\n  modelName: $.shareData.var_setting.modelName,/*dm*/
\n  pageKey: $.shareData.var_setting.pageKey,/*dm*/
\n  viewCode: data.editorViewCode/*dm*/
\n})/*dm*/
\n/*dm*/
\nconst { code, msg } = $.shareData.api_deleteView/*dm*/
\n/*dm*/
\nif (code && code !== 200) {/*dm*/
\n  this.$message.error(msg)/*dm*/
\n} else {/*dm*/
\n/*dm*/
\n  $.shareData.var_pageViewListAction($.shareData.var_pageViewList.filter(item => item.editorViewCode !== data.editorViewCode))/*dm*/
\n/*dm*/
\n  this.$message.success('删除成功');/*dm*/
\n  // const { columnPlanId } = $.shareData.var_tableSetting/*dm*/
\n/*dm*/
\n  // if (columnPlanId) {/*dm*/
\n/*dm*/
\n  //   await $.shareData.api_tablePlanListAction({ modelName:$.shareData.var_setting?.modelName, planId: columnPlanId })/*dm*/
\n/*dm*/
\n/*dm*/
\n  // }/*dm*/
\n}}}",
															"type":"function"
														}
													]
												},
												"planSetDefault":{
													"arguments":[
														"data"
													],
													"actions":[
														{
															"handler":"${function handler(data){/*dm*/
\nawait $.shareData.api_setViewDefaultAction({/*dm*/
\n  modelName: $.shareData.var_setting?.modelName,/*dm*/
\n  viewCode: data.editorViewCode,/*dm*/
\n  pageKey: $.shareData.var_setting.pageKey/*dm*/
\n}).catch(() => {/*dm*/
\n  return/*dm*/
\n}).then(() => {/*dm*/
\n  $.shareData.var_pageViewListAction($.shareData.var_pageViewList.map(item => {/*dm*/
\n    if (item.editorViewCode === data.editorViewCode) {/*dm*/
\n      return {/*dm*/
\n        ...item,/*dm*/
\n        isDefault: true,/*dm*/
\n        defaultState: true,/*dm*/
\n      }/*dm*/
\n    } else {/*dm*/
\n      return {/*dm*/
\n        ...item,/*dm*/
\n        isDefault: false,/*dm*/
\n        defaultState: false,/*dm*/
\n      }/*dm*/
\n    }/*dm*/
\n  }))/*dm*/
\n  this.$message.success(\"设置成功\");/*dm*/
\n})}}",
															"type":"function"
														}
													]
												}
											}
										},
										"componentName":"Programme",
										"id":"Programme-kwZzFsWT",
										"version":"1.8.3",
										"props":{
											"ref":"programme",
											"currentPlanKeyValue":"${$.shareData.var_activePlanView.editorViewCode}",
											"prop":"${{/*dm*/
\n  id: 'editorViewCode',/*dm*/
\n  name: 'editorViewName',/*dm*/
\n  isDefault: 'defaultState',/*dm*/
\n  system: 'system',/*dm*/
\n}}",
											"showMore":"${!$.shareData.var_findRelationDialog}",
											"showPlan":"${$.shareData.var_setting.enableView}",
											"showQueryConditions":"${$.shareData.var_pageViewList.map(item => {/*dm*/
\n  /*dm*/
\n  return {/*dm*/
\n    ...item,/*dm*/
\n    name: item.editorViewName/*dm*/
\n  }/*dm*/
\n})}"
										},
										"attrs":{
											"style":{}
										}
									},
									{
										"children":[
											{
												"componentName":"TextWrapper",
												"id":"TextWrapper-tB7kzyzS",
												"title":"分割线",
												"props":{
													"children":""
												},
												"attrs":{
													"style":{
														"margin-left":"16px",
														"background":"rgba(220, 223, 230, 1)",
														"display":"inline-block",
														"width":"1px",
														"margin-top":"0",
														"margin-bottom":"0",
														"height":"16px",
														"margin-right":"16px"
													}
												}
											}
										],
										"componentName":"VIf",
										"id":"VIf-X3CXAAZf",
										"props":{
											"conditions":"${[$.shareData.var_settingData.showKeywordSearch && $.shareData.var_setting.enableView]}"
										}
									},
									{
										"libraryName":"@jd/joyui",
										"componentType":"UILibrary",
										"action":{
											"fire":{
												"keywordSearch":{
													"arguments":[
														"data"
													],
													"actions":[
														{
															"handler":"${function handler(){/*dm*/
\n                                await $.shareData.var_pageParamsAction({ pageNum: 1 });/*dm*/
\n                                $.shareData.var_searchTypeAction({ searchData: data });/*dm*/
\n                                await this.$refs.condition.search();/*dm*/
\n                              }}",
															"type":"function"
														}
													]
												},
												"keywordInput":{
													"arguments":[
														"data"
													],
													"actions":[
														{
															"handler":"${function Handler(){/*dm*/
\n                                $.shareData.var_searchTypeAction({ searchData: data });/*dm*/
\n                              }}",
															"type":"function"
														}
													]
												}
											}
										},
										"componentName":"KeywordSearch",
										"id":"KeywordSearch-NGEYwZjN",
										"version":"1.8.3",
										"props":{
											"showKeywordSearch":"${$.shareData.var_setting.enableKeyword}",
											"attributes":"${$.shareData.var_activePlanView.keyWordList.map(item => item.fieldName)}",
											"placeholder":"${\"输入模版名称搜索\"}"
										},
										"attrs":{}
									},
									{
										"libraryName":"@jd/joyui",
										"componentType":"UILibrary",
										"action":{
											"fire":{
												"change":{
													"arguments":[
														"data"
													],
													"actions":[
														{
															"handler":"${function handler(data){$.shareData.var_switchFlagAction(!data);\r/*dm*/
\n}}",
															"type":"function"
														}
													]
												}
											}
										},
										"componentName":"AdvancedFilter",
										"id":"AdvancedFilter-RcFcH4fY",
										"version":"1.8.3",
										"props":{
											"showFlodButton":"${$.shareData.var_setting.enableQuery}",
											"disableds":"${$.functions.fun_getAdvancedFilter(false);}",
											"badgeNumber":"${$.functions.fun_getAdvancedFilter(true);}"
										},
										"attrs":{
											"style":{
												"margin-left":"16px"
											}
										}
									},
									{
										"libraryName":"@jd/joyui",
										"componentType":"UILibrary",
										"children":[
											{
												"children":[
													{
														"libraryName":"@jd/joyui",
														"componentType":"UILibrary",
														"action":{
															"fire":{
																"change":{
																	"arguments":[
																		"data"
																	],
																	"actions":[
																		{
																			"handler":"${function handler(){/*dm*/
\n// $.shareData.var_currentPlan.conditionsValue[$index] = data;/*dm*/
\nthis.$set($.shareData.var_activePlanView.conditionsValue, $index, data);/*dm*/
\n$.functions.fun_getAdvancedFilter(true);/*dm*/
\n}}",
																			"type":"function"
																		}
																	]
																},
																"findRelatedOpen":{
																	"arguments":[
																		"fieldProps"
																	],
																	"actions":[
																		{
																			"handler":"${function Handler(){const { modelName, modelText } = fieldProps.relatedModel;/*dm*/
\nconst { fieldName, businessType } = fieldProps;/*dm*/
\n/*dm*/
\n$.shareData.var_findRelationMultiInfoAction({/*dm*/
\n  modelName,/*dm*/
\n  modelText,/*dm*/
\n  fieldName,/*dm*/
\n  businessType,/*dm*/
\n  index: $index,/*dm*/
\n  visible: true/*dm*/
\n})}}",
																			"type":"function"
																		}
																	]
																}
															}
														},
														"componentName":"ModelField",
														"id":"ModelField-5fPpxWmJ",
														"title":"ModelField",
														"version":"1.8.3",
														"props":{
															"editField":{
																"disabled":false
															},
															"originProps":{},
															"formType":"search",
															"fieldName":"${__item.field.fieldName}",
															"isShowField":"${__item?.value?.isShow}",
															"fieldProps":"${() => {/*dm*/
\n  const res = { ...__item.field }/*dm*/
\n  if (res.businessType == 'findRelatedMulti') {/*dm*/
\n    res.isPopup = true;/*dm*/
\n  }/*dm*/
\n  // if ($.shareData.var_setting?.fieldInfo?.[item.fieldName]?.fieldName) {/*dm*/
\n  //   res.fieldText = $.shareData.var_setting.fieldInfo[item.fieldName]?.fieldName/*dm*/
\n  // }/*dm*/
\n  return res;/*dm*/
\n}}",
															"businessType":"${__item.field.businessType}",
															"fieldType":"${__item.field.fieldType}",
															"value":"${__item.value}",
															"fieldText":""
														},
														"attrs":{
															"setting":"${__item.field.businessType == 'findRelatedMulti' ? JSON.stringify( {/*dm*/
\n  name: 'field',/*dm*/
\n  type: 'form',/*dm*/
\n  label: '关联关系弹窗页面',/*dm*/
\n  fieldInfo: {/*dm*/
\n    fieldName: __item.field.fieldName,/*dm*/
\n    businessType: __item.field.businessType,/*dm*/
\n    modelName: __item.field?.relatedModel?.modelName || __item.field.modelName,/*dm*/
\n    fieldText: __item.field.fieldText/*dm*/
\n  }/*dm*/
\n}) : ''}"
														}
													}
												],
												"componentName":"VFor",
												"id":"VFor-bBMzx3xS",
												"props":{
													"forEach":"${$.shareData.var_activePlanView.conditionsValue.map(item => {/*dm*/
\n    const field = $.global.shareData.modelsInfoData.fieldsMap[$.shareData.var_setting.modelName].get(item.fieldName.split('.')[0]);/*dm*/
\n    if(field) {/*dm*/
\n      return {/*dm*/
\n        value: item, field: field/*dm*/
\n      }/*dm*/
\n    } else {/*dm*/
\n      console.warn(item.fieldName + '找不到对应的模型字段')/*dm*/
\n      return null/*dm*/
\n    }/*dm*/
\n  }/*dm*/
\n).filter(Boolean);}",
													"forEachKey":"__item",
													"index":"$index"
												}
											}
										],
										"action":{
											"fire":{
												"search":{
													"arguments":[
														"data"
													],
													"actions":[
														{
															"handler":"${function handler(){/*dm*/
\n                                await $.shareData.var_pageParamsAction({ pageNum: 1 });/*dm*/
\n                                /*dm*/
\n                                $.shareData.var_searchTypeAction({ type: 'advanced', data });/*dm*/
\n                                $.functions.fun_loadData();/*dm*/
\n                              }}",
															"type":"function"
														}
													]
												},
												"saveAs":{
													"arguments":[
														"data"
													],
													"actions":[
														{
															"handler":"${function handler(){/*dm*/
\n                                  this.$refs.programme.nameSetTitle = data.nameSetTitle;/*dm*/
\n                                  this.$refs.programme.nameSetVisible = data.nameSetVisible;/*dm*/
\n                                  this.$refs.programme.currentPlan = JSON.parse(JSON.stringify(data.currentPlan));/*dm*/
\n                                  this.$refs.programme.queryConditions = { ...data.currentPlan.conditionsJson[0] };/*dm*/
\n                               }}",
															"type":"function"
														}
													]
												},
												"change":{
													"arguments":[
														"data"
													],
													"actions":[
														{
															"handler":"${function handler(){$.shareData.var_activePlanView.conditionsValue = data}}",
															"type":"function"
														}
													]
												},
												"addSave":{
													"arguments":[
														"data"
													],
													"actions":[
														{
															"handler":"${function handler(data){/*dm*/
\nconst newData = {/*dm*/
\n  editorViewCode: data.editorViewCode,/*dm*/
\n  viewPlanInfoList: data.conditionsValue.map((item, index) => {/*dm*/
\n    return {/*dm*/
\n      fieldName: item.fieldName,/*dm*/
\n      conditionsJson: JSON.stringify(item),/*dm*/
\n      editorViewCode: data.editorViewCode,/*dm*/
\n      \"showIndex\": index/*dm*/
\n    }/*dm*/
\n  })/*dm*/
\n}/*dm*/
\n// let list = {/*dm*/
\n//   planCode: data.planCode,/*dm*/
\n//   name: data.planName || data.name,/*dm*/
\n//   modelName: data.modelName,/*dm*/
\n//   defaultPlan: data.defaultPlan,/*dm*/
\n//   conditionsJson: data.conditionsJson,/*dm*/
\n// };/*dm*/
\nconst info = await $.functions.fun_savePlanView(newData, true);/*dm*/
\nif (info.code === 200) {/*dm*/
\n  this.$message.success('保存成功');/*dm*/
\n  // await $.functions.fun_loadConditionPlanList();/*dm*/
\n  //  $.shareData.var_currentPlanAction({ ...list}); /*dm*/
\n} else {/*dm*/
\n  this.$message.error(info.msg);/*dm*/
\n}}}",
															"type":"function"
														}
													]
												},
												"reset":{
													"arguments":[
														"data"
													],
													"actions":[
														{
															"handler":"${function handler(data){/*dm*/
\nsetTimeout(() => {/*dm*/
\n  $.shareData.var_searchTypeAction({ type: 'advanced', data: this.$refs.condition.getSearchQuery() });/*dm*/
\n  $.functions.fun_loadData();/*dm*/
\n}, 100);}}",
															"type":"function"
														}
													]
												}
											}
										},
										"componentName":"ModelCondition",
										"id":"ModelCondition-MEzpG6nx",
										"version":"1.8.3",
										"props":{
											"mode":"simple",
											"ref":"condition",
											"queryConditions":"${$.shareData.var_activePlanView.conditionsValue}",
											"modelData":"${$.global.shareData.modelsInfoData?.fields[$.shareData.var_setting?.modelName]}",
											"seniorFlag":false,
											"currentPlan":"${$.shareData.var_activePlanView}",
											"showSearch":"${$.shareData.var_setting.enableQuery}",
											"displaySwitch":"${$.shareData.var_switchFlag}",
											"slot":"search",
											"saveShow":"${!$.shareData.var_findRelationDialog}"
										}
									},
									{
										"children":[
											{
												"componentType":"custom",
												"children":[
													{
														"libraryName":"@jd/joyui",
														"componentType":"UILibrary",
														"children":[
															{
																"children":[
																	{
																		"children":[
																			{
																				"action":{
																					"fire":{
																						"click":{
																							"arguments":[],
																							"actions":[
																								{
																									"handler":"${function(){ console.log(\"button click\");}}",
																									"type":"function"
																								}
																							]
																						}
																					}
																				},
																				"componentName":"Button",
																				"id":"Button-MrcnZihs",
																				"attrs":{},
																				"props":{
																					"size":"small",
																					"children":"${__item.label}",
																					"icon":"${`joyIconFont ${__item.icon}`}",
																					"type":"text",
																					"native-type":"button"
																				}
																			}
																		],
																		"componentName":"VFor",
																		"id":"VFor-hiTCtCsi",
																		"props":{
																			"forEach":"${$.functions.fun_getTableConfig()?.customButtons?.filter(v => v.enable)}",
																			"forEachKey":"__item",
																			"index":"$index"
																		}
																	}
																],
																"componentName":"VIf",
																"id":"VIf-ARxw6eaW",
																"props":{
																	"conditions":"${[$.functions.fun_getTableConfig()?.customButtons?.filter(v => v.enable).length]}"
																}
															},
															{
																"children":[
																	{
																		"action":{
																			"fire":{
																				"click":{
																					"arguments":[],
																					"actions":[
																						{
																							"handler":"${function handler(){$.shareData.var_importDialogVisibleAction(true);}}",
																							"type":"function"
																						}
																					]
																				}
																			}
																		},
																		"componentName":"Button",
																		"id":"Button-TXPTByeh",
																		"title":"导入",
																		"props":{
																			"size":"small",
																			"children":"导入",
																			"icon":"joyIconFont joy-upload",
																			"type":"text",
																			"native-type":"button"
																		}
																	}
																],
																"componentName":"VIf",
																"id":"VIf-22wh5PxB",
																"props":{
																	"conditions":"${[$.functions.fun_verifRole(\"import\") && $.shareData.var_setting.enableImport]}"
																}
															},
															{
																"children":[
																	{
																		"action":{
																			"fire":{
																				"click":{
																					"arguments":[],
																					"actions":[
																						{
																							"handler":"${function handler(){// 关键词查询/*dm*/
\n                                            const modelName = $.shareData.var_setting?.modelName/*dm*/
\n                                            let attributes = $.shareData.var_columnsShow.map(item => {/*dm*/
\n                                                return item.fieldName/*dm*/
\n                                            })/*dm*/
\n                                            await $.shareData.api_derivedExcelAction({/*dm*/
\n                                                modelName,/*dm*/
\n                                                ...$.shareData.var_searchType.cacheData,/*dm*/
\n                                                attributes/*dm*/
\n                                            })/*dm*/
\n                                            const url = window.URL.createObjectURL(new Blob([$.shareData.api_derivedExcel?.data]));/*dm*/
\n                                            let link = document.createElement('a');/*dm*/
\n                                            link.style.display = 'none';/*dm*/
\n                                            link.href = url;/*dm*/
\n                                            link.setAttribute('download', $.shareData.var_setting.modelText +'.xlsx');/*dm*/
\n                                            document.body.appendChild(link);/*dm*/
\n                                            link.click();/*dm*/
\n                                            URL.revokeObjectURL(link.href);/*dm*/
\n                                            document.body.removeChild(link);/*dm*/
\n                                            link = null;/*dm*/
\n                                            }}",
																							"type":"function"
																						}
																					]
																				}
																			}
																		},
																		"componentName":"Button",
																		"id":"Button-7WczD5hn",
																		"title":"导出",
																		"props":{
																			"size":"small",
																			"children":"导出",
																			"icon":"joyIconFont joy-download",
																			"type":"text",
																			"native-type":"button"
																		}
																	}
																],
																"componentName":"VIf",
																"id":"VIf-AmSZ8kN7",
																"props":{
																	"conditions":"${[$.functions.fun_verifRole(\"export\") && $.shareData.var_setting.enableExport]}"
																}
															},
															{
																"children":[
																	{
																		"action":{
																			"fire":{
																				"click":{
																					"arguments":[],
																					"actions":[
																						{
																							"handler":"${function Fun89688(){/*dm*/
\n                                            this.$confirm('是否确认删除?', '提示', {/*dm*/
\n                                              confirmButtonText: '确定',/*dm*/
\n                                              cancelButtonText: '取消',/*dm*/
\n                                              type: 'warning'/*dm*/
\n                                            }).then(() => {/*dm*/
\n                                              const modelName = $.shareData.var_setting.modelName/*dm*/
\n                                              const ids = $.shareData.var_multipleSelection.reduce((prev, cur) => [...prev, cur.id], []);/*dm*/
\n                                              $.shareData.api_deleteInBatchAction({modelName, data:{ids}}).then(()=>{/*dm*/
\n                                                if ($.shareData.api_deleteInBatch) {/*dm*/
\n                                                  this.$message.success(\"删除成功!\");/*dm*/
\n                                                  const { totalSize, pageNum, pageSize } = $.shareData.var_pageParams;/*dm*/
\n                                                  if (totalSize - pageNum * (pageSize-ids.length) - 1 <= 0 && pageNum > 1) {/*dm*/
\n                                                    $.shareData.var_pageParamsAction({ pageNum: pageNum - 1 });/*dm*/
\n                                                  }/*dm*/
\n                                                  $.shareData.var_multipleSelectionAction([])/*dm*/
\n                                                  $.functions.fun_loadData();/*dm*/
\n                                                } else {/*dm*/
\n                                                  this.$message.error('删除失败');/*dm*/
\n                                                }/*dm*/
\n                                              }).catch((error) => {/*dm*/
\n                                                this.$message.error('删除错误')/*dm*/
\n                                              })/*dm*/
\n                                            })/*dm*/
\n                                          }}",
																							"type":"function"
																						}
																					]
																				}
																			}
																		},
																		"componentName":"Button",
																		"id":"Button-jeJ7Gs2m",
																		"title":"批量删除",
																		"props":{
																			"size":"small",
																			"children":"批量删除",
																			"icon":"joyIconFont joy-finished",
																			"disabled":"${$.shareData.var_multipleSelection.length==0}",
																			"type":"text",
																			"native-type":"button"
																		}
																	}
																],
																"componentName":"VIf",
																"id":"VIf-4XtDw7a2",
																"props":{
																	"conditions":"${[$.functions.fun_verifRole(\"batch_delete\") && $.shareData.var_setting.enableBatchDelete]}"
																}
															},
															{
																"children":[
																	{
																		"children":[
																			{
																				"componentName":"TextWrapper",
																				"id":"TextWrapper-P4mKMQab",
																				"title":"新建事件",
																				"props":{
																					"children":"新建"
																				}
																			},
																			{
																				"children":[
																					{
																						"children":[
																							{
																								"children":[
																									{
																										"children":"${__item.label}",
																										"componentName":"TextWrapper",
																										"id":"TextWrapper23781"
																									}
																								],
																								"componentName":"DropdownItem",
																								"id":"DropdownItem52986",
																								"props":{
																									"value":"${__item.value}",
																									"key":"${__item.label}"
																								}
																							}
																						],
																						"componentName":"VFor",
																						"id":"VFor31799",
																						"props":{
																							"forEach":"${[{ label: '草稿箱', icon:'', value: 'one' }]}",
																							"forEachKey":"__item"
																						}
																					}
																				],
																				"componentName":"DropdownMenu",
																				"id":"DropdownMenu31150",
																				"props":{
																					"slot":"dropdown"
																				}
																			}
																		],
																		"action":{
																			"fire":{
																				"click":{
																					"arguments":[],
																					"actions":[
																						{
																							"handler":"${function Fun86820(){/*dm*/
\nif ($.shareData.var_isQuery) {/*dm*/
\n  const { fieldName, value, modelName } = $.shareData.var_isQuery[0];/*dm*/
\n  const obj = { formId: value.id, fieldName, modelName };/*dm*/
\n  const jumpUrl = $.functions.fun_getJumpUrl('add', obj);/*dm*/
\n  if (jumpUrl) {/*dm*/
\n    $.bom.page.open(jumpUrl, obj, () => {/*dm*/
\n      $.functions.fun_loadData();/*dm*/
\n    }, {});/*dm*/
\n  }/*dm*/
\n} else {/*dm*/
\n  const jumpUrl = $.functions.fun_getJumpUrl('add');/*dm*/
\n  if (jumpUrl) {/*dm*/
\n    $.bom.page.open(jumpUrl, {}, () => {/*dm*/
\n      $.functions.fun_loadData();/*dm*/
\n    }, {});/*dm*/
\n  }/*dm*/
\n}/*dm*/
\n}}",
																							"type":"function"
																						}
																					]
																				},
																				"command":{
																					"arguments":[
																						"value"
																					],
																					"actions":[
																						{
																							"handler":"${function Fun21011(){/*dm*/
\n                                            $.functions.fun_loadDraftData();/*dm*/
\n                                            $.shareData.var_draftDrawerAction(true);/*dm*/
\n                                          }}",
																							"type":"function"
																						}
																					]
																				}
																			}
																		},
																		"componentName":"Dropdown",
																		"id":"Dropdown-wyKC7k8c",
																		"title":"新建事件",
																		"props":{
																			"size":"small",
																			"placement":"bottom-end",
																			"trigger":"hover",
																			"hide-on-click":true,
																			"type":"primary",
																			"splitButton":"split-button"
																		},
																		"attrs":{
																			"style":{
																				"margin-left":"16px"
																			}
																		}
																	}
																],
																"componentName":"VIf",
																"id":"VIf32720",
																"props":{
																	"conditions":"${[$.global.shareData?.modelsInfoData?.draft[$.shareData.var_setting?.modelName] && $.functions.fun_verifRole(\"add\") && $.shareData.var_setting.enableAdd ]}"
																}
															},
															{
																"children":[
																	{
																		"action":{
																			"fire":{
																				"click":{
																					"arguments":[],
																					"actions":[
																						{
																							"handler":"${function Fun60494(){if ($.shareData.var_isQuery) {/*dm*/
\n  const { fieldName, value, modelName } = $.shareData.var_isQuery[0];/*dm*/
\n  const obj = { formId: value.id, fieldName, modelName };/*dm*/
\n  const jumpUrl = $.functions.fun_getJumpUrl('add', obj);/*dm*/
\n  if (jumpUrl) {/*dm*/
\n    $.bom.page.open(jumpUrl, obj, () => {/*dm*/
\n      $.functions.fun_loadData();/*dm*/
\n    }, {});/*dm*/
\n  }/*dm*/
\n} else {/*dm*/
\n  const jumpUrl = $.functions.fun_getJumpUrl('add');/*dm*/
\n  if (jumpUrl) {/*dm*/
\n    $.bom.page.open(jumpUrl, {}, () => {/*dm*/
\n      $.functions.fun_loadData();/*dm*/
\n    }, {});/*dm*/
\n  }/*dm*/
\n}/*dm*/
\n}}",
																							"type":"function"
																						}
																					]
																				}
																			},
																			"id":"Button78424"
																		},
																		"componentName":"Button",
																		"id":"Button-EjSj3sts",
																		"attrs":{
																			"style":{
																				"margin-left":"16px"
																			}
																		},
																		"props":{
																			"size":"small",
																			"children":"新建",
																			"v-open":"${$.functions.fun_getJumpUrl('add')}",
																			"type":"primary"
																		}
																	}
																],
																"componentName":"VIf",
																"id":"VIf80994",
																"props":{
																	"conditions":"${[!$.global.shareData?.modelsInfoData?.draft[$.shareData.var_setting?.modelName] && $.functions.fun_verifRole(\"add\") && $.shareData.var_setting.enableAdd]}"
																}
															}
														],
														"componentName":"MoreButton",
														"id":"MoreButton-BdMmCQam",
														"version":"1.8.3",
														"props":{},
														"attrs":{
															"style":{}
														}
													}
												],
												"componentName":"DivWrapper",
												"id":"DivWrapper-WwHmSdRi",
												"title":"按钮组",
												"attrs":{
													"style":{
														"padding":"4px",
														"z-index":"1",
														"top":"6px",
														"position":"absolute",
														"right":"0px"
													},
													"setting":"${JSON.stringify({ name: 'buttons', editorViewCode: $.shareData.var_activePlanView.editorViewCode})}"
												},
												"props":{
													"name":"DivWrapper",
													"slot":"other"
												}
											}
										],
										"componentName":"VIf",
										"id":"VIf-xsTtHbSA",
										"props":{
											"conditions":"${[$.shareData.var_setting.enableButtons && !$.shareData.var_findRelationDialog]}"
										}
									}
								],
								"action":{
									"fire":{}
								},
								"componentName":"SearchPage",
								"id":"SearchPage-kYKCcPYk",
								"title":"新高级查询",
								"version":"1.8.3",
								"props":{
									"mode":"simple",
									"showPageTab":true,
									"showKeywordSearch":true,
									"showSearchButton":true,
									"showSearch":true,
									"showPlan":true,
									"slot":"advancedSearch"
								},
								"attrs":{
									"style":{
										"min-height":"48px",
										"margin-bottom":"8px"
									}
								}
							},
							{
								"libraryName":"@jd/joyui",
								"componentType":"UILibrary",
								"__setting":{
									"settingData":{
										"pageSizes":"${[10,20,30,40]}",
										"editPage":"796413391765319682",
										"headerEllipsis":true,
										"resizeColumn":true,
										"pageSize":10,
										"showRefresh":true,
										"batchDelFlag":true,
										"buttonShowText":true,
										"mode":"normal",
										"columnShowIcon":false,
										"tableBorderBottom":true,
										"showPlanView":true,
										"columnPlanId":"792010462761443330",
										"stripe":true,
										"addPage":"796413391765319682",
										"paginationMode":"complete",
										"showOverflowTooltip":true,
										"border":false,
										"showEdit":true,
										"columnShowText":true,
										"buttonShowIcon":false,
										"textAlign":"left",
										"paginationAlign":"right",
										"showPagination":true,
										"addFlag":true,
										"showDetail":true,
										"exportDataFlag":false,
										"cellEllipsis":true,
										"showNo":true,
										"showDel":true,
										"detailPage":"792010485771395074",
										"draftFlag":false
									}
								},
								"children":[
									{
										"componentType":"custom",
										"children":[
											{
												"libraryName":"@jd/joyui",
												"componentType":"UILibrary",
												"children":[
													{
														"componentType":"custom",
														"children":[
															{
																"componentType":"custom",
																"children":[
																	{
																		"componentType":"custom",
																		"componentName":"Image",
																		"id":"Image-TBxZahGF",
																		"props":{
																			"src":"${\"http://s3.cn-north-1.jdcloud-oss.com/jz-material/lcapicon/1658309481895-3905270251763847417.png\"}"
																		},
																		"chosen":false,
																		"attrs":{
																			"style":{
																				"width":"120px"
																			}
																		}
																	},
																	{
																		"componentType":"custom",
																		"componentName":"TextWrapper",
																		"id":"TextWrapper-bsH7baxF",
																		"attrs":{
																			"style":{
																				"margin":"-30px 0 0 0",
																				"color":"rgba(129,128,148,1)",
																				"font-weight":"normal",
																				"display":"block",
																				"width":"100%",
																				"font-size":"14px"
																			}
																		},
																		"props":{
																			"children":" 暂无内容"
																		},
																		"chosen":false
																	}
																],
																"componentName":"DivWrapper",
																"id":"DivWrapper-n4YJGzfw",
																"title":"暂无数据",
																"attrs":{
																	"style":{
																		"background-color":"#ffffff",
																		"padding":"100px 24px 24px",
																		"width":"100%",
																		"min-height":"320px",
																		"height":"100%"
																	}
																},
																"props":{
																	"v-if":"${!$.shareData.var_pageLoading}",
																	"name":"DivWrapper",
																	"slot":"empty"
																},
																"chosen":false
															},
															{
																"componentType":"custom",
																"children":[
																	{
																		"componentType":"custom",
																		"componentName":"TableColumn",
																		"id":"TableColumn-7KxTJSdX",
																		"props":{
																			"show-overflow-tooltip":true,
																			"reserve-selection":true,
																			":selectable":"${function s(scope){ return scope['$extra']?.dataPermission == 2 }}",
																			"width":"48",
																			"fixed":"left",
																			"type":"selection",
																			"align":"center",
																			"v-slot":"scope"
																		}
																	},
																	{
																		"children":[
																			{
																				"action":{
																					"fire":{}
																				},
																				"componentName":"Radio",
																				"id":"Radio-j3fYzDTr",
																				"props":{
																					"label":"${scope.row.id}",
																					"v-model":"${$.shareData.var_singleSelection}"
																				},
																				"attrs":{
																					"class":"${'find-relation-radio'}"
																				}
																			}
																		],
																		"componentName":"TableColumn",
																		"id":"TableColumn-cZcRk4KG",
																		"props":{
																			"width":"${'40px'}",
																			"fixed":"${'left'}",
																			"v-slot":"scope"
																		}
																	}
																],
																"componentName":"VIf",
																"id":"VIf-S36P2b6Y",
																"props":{
																	"conditions":"${() => {/*dm*/
\n  if ($.shareData.var_findRelationDialog) {/*dm*/
\n    return [/*dm*/
\n      $.shareData.var_findRelationDialog?.businessType == 'findRelatedMulti',/*dm*/
\n      $.shareData.var_findRelationDialog?.businessType == 'findRelated'/*dm*/
\n    ]/*dm*/
\n  }/*dm*/
\n  return [/*dm*/
\n    $.shareData.var_tableSetting?.selectRowType === 'many' || $.shareData.var_tableSetting?.batchDelFlag,/*dm*/
\n    false/*dm*/
\n  ]/*dm*/
\n}}"
																}
															},
															{
																"componentType":"custom",
																"children":[
																	{
																		"componentType":"custom",
																		"componentName":"TableColumn",
																		"id":"TableColumn78522",
																		"props":{
																			"resizable":false,
																			"width":"60",
																			"fixed":"left",
																			"label":"序号",
																			"type":"index",
																			"align":"center",
																			"key":"${Math.random()}",
																			"class-name":"color-text-secondary"
																		}
																	}
																],
																"componentName":"VIf",
																"id":"VIf-X3A3JkZB",
																"props":{
																	"conditions":"${[$.shareData.var_tableSetting?.showNo || $.shareData.var_findRelationDialog]}"
																}
															},
															{
																"componentType":"custom",
																"children":[
																	{
																		"componentType":"custom",
																		"children":[
																			{
																				"libraryName":"@jd/joyui",
																				"componentType":"UILibrary",
																				"action":{
																					"fire":{
																						"navtoDetail":{
																							"arguments":[
																								"field",
																								"modelName"
																							],
																							"actions":[
																								{
																									"handler":"${function Fun19319(field, modelName){/*dm*/
\n//const params = scope.row.$extra?.dataPermission;/*dm*/
\n/*dm*/
\nconst obj = {/*dm*/
\n  detailId: scope.row[item.name]?.id || scope.row.id,/*dm*/
\n  permission: scope.row.$extra?.dataPermission/*dm*/
\n}/*dm*/
\nlet jumpUrl/*dm*/
\nif (item.modelField.businessType == 'findRelated') {/*dm*/
\n  jumpUrl = $.functions.fun_getJumpUrl('fieldName', obj, item.modelField.relatedModel.modelName, item.fieldName);/*dm*/
\n} else if (item.fieldName == 'name') {/*dm*/
\n  jumpUrl = $.functions.fun_getJumpUrl('nameField', obj);/*dm*/
\n} else {/*dm*/
\n  jumpUrl = $.functions.fun_getJumpUrl('detail', obj);/*dm*/
\n}/*dm*/
\n$.bom.page.open(jumpUrl, {}, () => {/*dm*/
\n  $.functions.fun_loadData();/*dm*/
\n}, {});/*dm*/
\n}}",
																									"type":"function"
																								}
																							]
																						},
																						"handleDownload":{
																							"arguments":[
																								"file"
																							],
																							"actions":[
																								{
																									"handler":"${function handler(file){/*dm*/
\nawait $.shareData.api_getPreDownloadAction({ originFileName: file.originFileName })/*dm*/
\n/*dm*/
\nfetch($.shareData.api_getPreDownload.presignedObjectUrl, {/*dm*/
\n  method: 'GET',/*dm*/
\n  responseType: 'blob',/*dm*/
\n}).then(/*dm*/
\n  res => {/*dm*/
\n    return res.blob();/*dm*/
\n  })/*dm*/
\n  .then(blob => {/*dm*/
\n/*dm*/
\n    let bl = new Blob([blob]);/*dm*/
\n/*dm*/
\n    var link = document.createElement('a');/*dm*/
\n/*dm*/
\n    link.href = window.URL.createObjectURL(bl);/*dm*/
\n/*dm*/
\n    link.download = file.name;/*dm*/
\n/*dm*/
\n    link.click();/*dm*/
\n/*dm*/
\n    window.URL.revokeObjectURL(link.href);/*dm*/
\n/*dm*/
\n  }).catch(err => {/*dm*/
\n    console.log('error', error)/*dm*/
\n  });/*dm*/
\n}}",
																									"type":"function"
																								}
																							]
																						}
																					}
																				},
																				"componentName":"ModelField",
																				"id":"ModelField-QdB68Y3N",
																				"title":"模型字段",
																				"version":"1.8.3",
																				"props":{
																					"originProps":{},
																					"formType":"table",
																					"fieldProps":"${() => {/*dm*/
\n  const res = { ...item.modelField }/*dm*/
\n  if ($.shareData.var_findRelationDialog) {/*dm*/
\n    res.jumpOrNot = false;/*dm*/
\n  }/*dm*/
\n  return res;/*dm*/
\n}/*dm*/
\n}",
																					"v-open":"${() => {\r/*dm*/
\n  const pageId = $.shareData.var_findRelatedPages[item.modelField.fieldName];\r/*dm*/
\n  if (!pageId) { return; }\r/*dm*/
\n  const obj = {\r/*dm*/
\n    detailId: scope.row[item.name]?.id || scope.row.id,\r/*dm*/
\n    permission: scope.row.$extra?.dataPermission\r/*dm*/
\n  }\r/*dm*/
\n  let jumpUrl\r/*dm*/
\n  if (item.modelField.businessType == 'findRelated') {\r/*dm*/
\n    jumpUrl = $.functions.fun_getJumpUrl('fieldName', obj, item.modelField.fieldName);\r/*dm*/
\n  } else {\r/*dm*/
\n    jumpUrl = $.functions.fun_getJumpUrl('detail', obj);\r/*dm*/
\n  }\r/*dm*/
\n\r/*dm*/
\n  return jumpUrl\r/*dm*/
\n}}",
																					"businessType":"${item.businessType}",
																					"value":"${scope.row[item.name]}"
																				}
																			}
																		],
																		"componentName":"TableColumn",
																		"id":"TableColumn-xk8HSh8C",
																		"props":{
																			"show-overflow-tooltip":"${$.shareData.var_tableSetting.cellEllipsis && !['multipleDepartment', 'department'].includes(item.businessType) }",
																			"prop":"${item.fieldName}",
																			"fixed":"${() => {/*dm*/
\n  const head = $.shareData.var_activePlanView.freezeFirstColumn/*dm*/
\n  return head && $index == 0 ? \"left\" : false/*dm*/
\n}}",
																			"label":"${ $.functions.fun_getTableConfig()?.fieldInfo?.[item.fieldName]?.fieldName || item.fieldText}",
																			"sortable":"${() => {\r/*dm*/
\n  if (\r/*dm*/
\n    [\"text\", \"richText\", \"attachment\", \"user\", \"multipleUser\", \"url\"].includes(item.businessType)\r/*dm*/
\n    ||\r/*dm*/
\n    item.modelField.entityField === false\r/*dm*/
\n  ) {\r/*dm*/
\n    return false;\r/*dm*/
\n  }\r/*dm*/
\n  return \"custom\";\r/*dm*/
\n}}",
																			"align":"${$.shareData.var_tableSetting.textAlign}",
																			"v-slot":"scope",
																			"min-width":"${() => {/*dm*/
\n/*dm*/
\n/*dm*/
\n  const { minWidth } = $.functions?.fun_getFieldsBusinessType($.shareData.var_setting.modelName, item.fieldName);/*dm*/
\n  const resizeWidth = $.shareData.tableColumnWidth?.widths[item.fieldName]/*dm*/
\n  if (resizeWidth) {/*dm*/
\n    return resizeWidth + 'px'/*dm*/
\n  }/*dm*/
\n  return minWidth || \"140px\"/*dm*/
\n}/*dm*/
\n}"
																		},
																		"attrs":{
																			"setting":"${JSON.stringify({/*dm*/
\n  name: 'field',/*dm*/
\n  editorViewCode: $.shareData.var_activePlanView.editorViewCode,/*dm*/
\n  type: 'table',/*dm*/
\n  mainTableJump:true,/*dm*/
\n  fieldInfo: {/*dm*/
\n    fieldText: item.fieldText,/*dm*/
\n    fieldName: item.fieldName,/*dm*/
\n    modelName: item.modelField?.relatedModel?.modelName || item.modelField.modelName,/*dm*/
\n    businessType: item.businessType/*dm*/
\n  }/*dm*/
\n})}"
																		}
																	}
																],
																"componentName":"VFor",
																"id":"VFor-CneTmk5a",
																"title":"table列循环容器",
																"props":{
																	"forEach":"${$.shareData.var_columnsShow.map(item => {/*dm*/
\n        return {/*dm*/
\n          name: item.fieldName,/*dm*/
\n          label: item.fieldText,/*dm*/
\n          defaultSelectValue: item.defaultSelect,/*dm*/
\n          ...item,/*dm*/
\n        }/*dm*/
\n      })}",
																	"forEachKey":"item",
																	"index":"$index"
																}
															},
															{
																"componentType":"custom",
																"children":[
																	{
																		"componentType":"custom",
																		"children":[
																			{
																				"libraryName":"@jd/joyui",
																				"componentType":"UILibrary",
																				"action":{
																					"fire":{
																						"tab-click":{
																							"arguments":[
																								"tab"
																							],
																							"actions":[
																								{
																									"handler":"${function Fun15029(){/*dm*/
\nconst modelName = $.shareData.var_setting.modelName/*dm*/
\nconst detailId = scope.row?.id;/*dm*/
\nif (tab == 'edit') {/*dm*/
\n  const obj = {/*dm*/
\n    detailId/*dm*/
\n  }/*dm*/
\n  const jumpUrl = $.functions.fun_getJumpUrl('edit', obj);/*dm*/
\n  if (jumpUrl) {/*dm*/
\n    $.bom.page.open(jumpUrl, {}, () => {/*dm*/
\n      $.functions.fun_loadData();/*dm*/
\n    }, {});/*dm*/
\n  };/*dm*/
\n}/*dm*/
\nif (tab == 'copy') {/*dm*/
\n  const obj = {/*dm*/
\n    copyId: detailId,/*dm*/
\n  }/*dm*/
\n  const jumpUrl = $.functions.fun_getJumpUrl('copy', obj);/*dm*/
\n  if (jumpUrl) {/*dm*/
\n    $.bom.page.open(jumpUrl, {}, () => {/*dm*/
\n      $.functions.fun_loadData();/*dm*/
\n    }, {});/*dm*/
\n  };/*dm*/
\n}/*dm*/
\nif (tab == 'detail') {/*dm*/
\n  const obj = {/*dm*/
\n    detailId,/*dm*/
\n    permission: scope.row.$extra?.dataPermission/*dm*/
\n  }/*dm*/
\n  const jumpUrl = $.functions.fun_getJumpUrl('detail', obj);/*dm*/
\n  $.bom.page.open(jumpUrl, {}, () => {/*dm*/
\n    $.functions.fun_loadData();/*dm*/
\n  }, {});/*dm*/
\n}/*dm*/
\nif (tab == 'submit') {/*dm*/
\n  const formData = {};/*dm*/
\n  if ($.shareData.var_setting.wfOpen) {/*dm*/
\n    // 流程标识/*dm*/
\n    formData.id = scope.row.id;/*dm*/
\n    formData.name = scope.row.name;/*dm*/
\n    formData.$__saveAndSubmitWf = true;/*dm*/
\n  }/*dm*/
\n  formData.modelName = modelName;/*dm*/
\n  await $.shareData.api_saveDataAction(formData);/*dm*/
\n  if ($.shareData.api_saveData.code == 200) {/*dm*/
\n    this.$message.success('提交成功!');/*dm*/
\n    $.functions.fun_loadData();/*dm*/
\n  } else {/*dm*/
\n    this.$message.error($.shareData.api_saveData.msg);/*dm*/
\n  }/*dm*/
\n}/*dm*/
\nif (tab == 'delete') {/*dm*/
\n  this.$confirm('此操作将永久删除, 是否继续?', '提示', {/*dm*/
\n    confirmButtonText: '确定',/*dm*/
\n    cancelButtonText: '取消',/*dm*/
\n    type: 'warning'/*dm*/
\n  }).then(() => {/*dm*/
\n    $.shareData.api_deleteDataAction({ modelName, id: scope.row?.id }).then(() => {/*dm*/
\n      if ($.shareData.api_deleteData) {/*dm*/
\n        this.$message.success('删除成功!');/*dm*/
\n        const { totalSize, pageNum, pageSize } = $.shareData.var_pageParams/*dm*/
\n        if (totalSize - pageSize * (pageNum - 1) - 1 <= 0 && pageNum > 1) {/*dm*/
\n          $.shareData.var_pageParamsAction({ pageNum: pageNum - 1 })/*dm*/
\n        }/*dm*/
\n        $.functions.fun_loadData();/*dm*/
\n      } else {/*dm*/
\n        this.$message.error('删除失败')/*dm*/
\n      }/*dm*/
\n    }).catch((error) => {/*dm*/
\n      this.$message.error(error)/*dm*/
\n    })/*dm*/
\n  }).catch(() => { });/*dm*/
\n}/*dm*/
\n}}",
																									"type":"function"
																								}
																							]
																						}
																					}
																				},
																				"componentName":"MoreButton",
																				"id":"MoreButton-Cj4iMYM8",
																				"version":"1.8.3",
																				"props":{
																					"styleState":"${[]}",
																					"padding":"0 16px 0px 0px",
																					"data":"${() => {/*dm*/
\n  let list = [];/*dm*/
\n  let edit = $.functions.fun_verifRole(\"edit\");/*dm*/
\n  let detail = $.functions.fun_verifRole(\"detail\");/*dm*/
\n  let deletes = $.functions.fun_verifRole(\"delete\");/*dm*/
\n  let copy = $.functions.fun_verifRole(\"copy\");/*dm*/
\n  if ($.functions.fun_getTableConfig()?.edit && scope.row['$extra'].dataPermission == 2 && edit) {/*dm*/
\n    const obj = {/*dm*/
\n      detailId: scope.row?.id/*dm*/
\n    }/*dm*/
\n    list.push({ \"label\": \"编辑\", \"name\": \"edit\", \"type\": \"text\", \"round\": \"round\", \"size\": \"small\", \"v-open\": $.functions.fun_getJumpUrl('edit', obj) })/*dm*/
\n  };/*dm*/
\n  // 流程提交按钮/*dm*/
\n  const { wfOpen } = $.shareData.var_setting;/*dm*/
\n  let submit = ['1', '4'].includes(scope.row.approval_status);/*dm*/
\n  if (wfOpen && submit) {/*dm*/
\n    list.push({ \"label\": \"提交\", \"name\": \"submit\", \"type\": \"text\", \"round\": \"round\", \"size\": \"small\" })/*dm*/
\n  };/*dm*/
\n  if ($.functions.fun_getTableConfig()?.detail && [1, 2].some((i) => i == scope.row['$extra'].dataPermission) && detail) {/*dm*/
\n    const obj = {/*dm*/
\n      detailId: scope.row?.id,/*dm*/
\n      permission: scope.row.$extra?.dataPermission/*dm*/
\n    }/*dm*/
\n    list.push({ \"label\": \"详情\", \"name\": \"detail\", \"type\": \"text\", \"round\": \"round\", \"size\": \"small\", \"v-open\": $.functions.fun_getJumpUrl('detail', obj) });/*dm*/
\n  };/*dm*/
\n  if ($.functions.fun_getTableConfig()?.delete && scope.row['$extra'].dataPermission == 2 && deletes) {/*dm*/
\n    list.push({ \"label\": \"删除\", \"name\": \"delete\", \"type\": \"text\", \"round\": \"round\", \"size\": \"small\" });/*dm*/
\n  };/*dm*/
\n  if ($.functions.fun_getTableConfig()?.copy && scope.row['$extra'].dataPermission == 2 && copy) {/*dm*/
\n    list.push({ \"label\": \"复制\", \"name\": \"copy\", \"type\": \"text\", \"round\": \"round\", \"size\": \"small\" });/*dm*/
\n  };/*dm*/
\n/*dm*/
\n  const customButtons = $.functions.fun_getTableConfig()?.columnCustomButtons?.filter(v => v.enable) || []/*dm*/
\n  if (customButtons.length) {/*dm*/
\n    for (let i = 0; i < customButtons.length; i++) {/*dm*/
\n      list.push({ \"label\": customButtons[i].label, \"name\": customButtons[i].label, \"type\": \"text\", \"round\": \"round\", \"size\": \"small\" });/*dm*/
\n    }/*dm*/
\n  }/*dm*/
\n  return list;/*dm*/
\n}/*dm*/
\n}",
																					"moreRound":"${false}",
																					"max-count":"${3}",
																					"textAlign":"left",
																					"marginLeft":"${0 * 1}"
																				}
																			}
																		],
																		"componentName":"TableColumn",
																		"id":"TableColumn86155",
																		"props":{
																			"resizable":false,
																			"prop":"action",
																			"fixed":"${() => {/*dm*/
\n  const action = $.shareData.var_activePlanView.freezeOperateColumn/*dm*/
\n  return action ? \"right\" : false/*dm*/
\n}}",
																			"label":"操作",
																			"v-slot":"scope",
																			"min-width":"202px"
																		}
																	}
																],
																"componentName":"VIf",
																"id":"VIf-wt6GARWG",
																"props":{
																	"conditions":"${[ !$.shareData.var_findRelationDialog && $.shareData.var_tableSetting?.mode==='normal']}"
																}
															}
														],
														"action":{
															"fire":{
																"header-dragend":{
																	"arguments":[
																		"newWidth",
																		"oldWidth",
																		"column",
																		"event"
																	],
																	"actions":[
																		{
																			"handler":"${function handler(newWidth,oldWidth,column,event){const genId = $.functions.fun_genTableId();\r/*dm*/
\nconst { property } = column\r/*dm*/
\n\r/*dm*/
\nconst { businessType, fieldWidth, minWidth } = $.functions.fun_getFieldsBusinessType($.shareData.var_setting.modelName, property);\r/*dm*/
\n\r/*dm*/
\nconst minNewWidth = Math.max(minWidth, newWidth)\r/*dm*/
\n\r/*dm*/
\nconst newColumnWidth = {\r/*dm*/
\n  ...$.shareData.var_tableColumnWidth.widths,\r/*dm*/
\n  [property]: minNewWidth\r/*dm*/
\n}\r/*dm*/
\n$.shareData.var_tableColumnWidth.widths = newColumnWidth;\r/*dm*/
\n  // 修复数据、重置表格宽度\r/*dm*/
\n  this.$refs['jtable'].store.states._columns = this.$refs['jtable']?.store.states._columns.map(item => {\r/*dm*/
\n    if (item.id === column.id && item.minWidth && item.width) {\r/*dm*/
\n      return {\r/*dm*/
\n        ...item,\r/*dm*/
\n        realWidth: minNewWidth,\r/*dm*/
\n        width: minNewWidth\r/*dm*/
\n      }\r/*dm*/
\n    }\r/*dm*/
\n    return item\r/*dm*/
\n  })\r/*dm*/
\n  this.$refs['jtable']?.store?.scheduleLayout(true);\r/*dm*/
\n  this.$nextTick(() => {\r/*dm*/
\n    // 更新列的宽度\r/*dm*/
\n    this.$refs['jtable'].layout.updateColumnsWidth();\r/*dm*/
\n  })\r/*dm*/
\nlocalStorage.setItem(genId, JSON.stringify(newColumnWidth))}}",
																			"type":"function"
																		}
																	]
																},
																"current-change":{
																	"arguments":[
																		"val"
																	],
																	"actions":[
																		{
																			"handler":"${function Fun33173(val){/*dm*/
\nif ($.shareData.var_tableSetting?.selectRowType === 'single') {/*dm*/
\n/*dm*/
\n  $.shareData.var_singleSelectionAction(val.id)/*dm*/
\n/*dm*/
\n}/*dm*/
\n// 查找关系弹窗 单选 数据回传。/*dm*/
\nif ($.shareData.var_findRelationDialog?.businessType == 'findRelated') {/*dm*/
\n  // $.shareData.var_findRelationSelectedDataAction(val)/*dm*/
\n  $.$emit('findRelationCallback', { ...val }, $.shareData.var_pageTag);/*dm*/
\n  // $.shareData.var_tableKeyAction('key' + (+new Date).toString())/*dm*/
\n}/*dm*/
\n// 查找关系弹窗 多选。/*dm*/
\n/*dm*/
\nif ($.shareData.var_findRelationDialog?.businessType == 'findRelatedMulti') {/*dm*/
\n  this.$refs['jtable'].toggleRowSelection(val)/*dm*/
\n}}}",
																			"type":"function"
																		}
																	]
																},
																"sort-change":{
																	"arguments":[
																		"column",
																		"prop"
																	],
																	"actions":[
																		{
																			"handler":"${function Fun_47sXNY(column, prop){if ($.shareData.var_querySorts.length > 1){\r/*dm*/
\n  $.shareData.var_querySorts.splice(0, 1);\r/*dm*/
\n}\r/*dm*/
\n\r/*dm*/
\n\r/*dm*/
\nif (column.order){\r/*dm*/
\n  $.shareData.var_querySorts.unshift({\r/*dm*/
\n    fieldName: column.prop,\r/*dm*/
\n    desc: column.order == \"descending\"\r/*dm*/
\n  })\r/*dm*/
\n}\r/*dm*/
\n    \r/*dm*/
\n\r/*dm*/
\nawait $.functions.fun_loadData();\r/*dm*/
\n}}",
																			"type":"function"
																		}
																	]
																},
																"selection-change":{
																	"arguments":[
																		"selection"
																	],
																	"actions":[
																		{
																			"handler":"${function Fun49955(selection){/*dm*/
\n// if($.shareData.var_tableSetting?.selectRowType==='many'){/*dm*/
\n$.shareData.var_multipleSelectionAction(selection)/*dm*/
\n// 多选不是响应式的，需要额外的处理/*dm*/
\n$.shareData.var_findRelationSelectedDataAction(selection)/*dm*/
\n/*dm*/
\n// 查找关系弹窗 数据回传/*dm*/
\nif ($.shareData.var_findRelationDialog?.businessType == 'findRelatedMulti') {/*dm*/
\n  $.$emit('findRelationCallback', selection, $.shareData.var_pageTag);/*dm*/
\n}/*dm*/
\n                                            // }/*dm*/
\n}}",
																			"type":"function"
																		}
																	]
																}
															}
														},
														"componentName":"Table",
														"id":"Table-R2zWZRBr",
														"title":"表格区域",
														"attrs":{
															"class":"${$.shareData.var_tableSetting.headerEllipsis?'headerEllipsis':''}",
															"setting":"${JSON.stringify({ name: 'table', editorViewCode: $.shareData.var_activePlanView.editorViewCode })}"
														},
														"props":{
															"border":"${$.shareData.var_tableSetting?.resizeColumn || $.shareData.var_tableSetting?.border}",
															"headerEllipsis":"${$.shareData.var_tableSetting.headerEllipsis}",
															"data":"${$.shareData.var_tableList}",
															"show-header":"${$.shareData.var_tableSetting.showHeader}",
															"ref":"jtable",
															"empty-text":"${\" \"}",
															"size":"${$.shareData.var_activePlanView?.displayDensity}",
															"v-loading":"${$.shareData.var_pageLoading}",
															"tooltip-effect":"dark",
															"tree-props":"${{ hasChildren: \"hasChildren\", children: \"children\" }}",
															"stripe":"${$.shareData.var_tableSetting.stripe}",
															"style":"width: 100%",
															"key":"${'abc' || $.shareData.var_tableKey || $.shareData.var_pageTag}",
															"height":"100%",
															"highlight-current-row":"${$.shareData.var_tableSetting?.selectRowType==='single'}",
															"row-key":"${'id'}"
														}
													}
												],
												"componentName":"ModelDrivenMarkup",
												"id":"ModelDrivenMarkup-YDCFxzZz",
												"title":"模型辅助配置",
												"version":"1.8.3",
												"props":{
													"modelName":"ceshimoxing",
													"modelText":"测试模型",
													"columnType":"record",
													"formType":"table"
												},
												"attrs":{
													"style":{
														"height":"100%"
													}
												}
											}
										],
										"componentName":"DivWrapper",
										"id":"DivWrapper-3ZbebtzY",
										"title":"表格+操作按钮",
										"props":{
											"name":"DivWrapper"
										},
										"attrs":{
											"class":"records-joy-table-body"
										}
									}
								],
								"action":{
									"fire":{
										"addPlan":{
											"arguments":[
												"value"
											],
											"actions":[
												{
													"handler":"${function Handler(){/*dm*/
\n// const { isDefault, planInfoDtoList, system, ...rest } = value/*dm*/
\n// await $.shareData.api_addTablePlanAction({ ...rest, planInfoDtoList: rest.fieldList })/*dm*/
\n// const { columnPlanId } = $.shareData.var_tableSetting/*dm*/
\n/*dm*/
\nconst allList = this.$refs['record-table'].$refs['view-edit'].getCheckAllData();/*dm*/
\nconst newData = {/*dm*/
\n  editorViewName: value.label,/*dm*/
\n  viewInfoList: allList.map(item => {/*dm*/
\n      return {/*dm*/
\n        editorViewCode: $.shareData.var_activePlanView.editorViewCode,/*dm*/
\n        fieldName: item.name,/*dm*/
\n        displayWidth: item.fieldWidth,/*dm*/
\n        showIndex: item.index,/*dm*/
\n        isEnable: item.show,/*dm*/
\n        relatedFieldList: item.relatedDtoList.map(child => {/*dm*/
\n      return {/*dm*/
\n            \"editorViewCode\": $.shareData.var_activePlanView.editorViewCode,/*dm*/
\n            \"fieldName\": item.name,/*dm*/
\n            \"displayWidth\": child.displayWidth,/*dm*/
\n            \"showIndex\": child.index,/*dm*/
\n            \"relatedModelName\": child.relatedModelName,/*dm*/
\n            \"relatedFieldName\": child.relatedFieldName,/*dm*/
\n            \"isEnable\": child.show/*dm*/
\n          }/*dm*/
\n        })/*dm*/
\n      }/*dm*/
\n  }),/*dm*/
\n  displayDensity: value.tableWidth, // 展示密度/*dm*/
\n  freezeFirstColumn: value.freeze?.head, // 冻结首列/*dm*/
\n  freezeOperateColumn: value.freeze?.action // 冻结尾列/*dm*/
\n}/*dm*/
\n/*dm*/
\nconst info = await $.functions.fun_savePlanView(newData, false)/*dm*/
\n/*dm*/
\n// const info = $.shareData.api_addTablePlan/*dm*/
\nif (info?.code !== 200) {/*dm*/
\n  this.$message.error(info.msg);/*dm*/
\n} else {/*dm*/
\n  // this.$refs['record-table'].changePlan({/*dm*/
\n  //   ...value,/*dm*/
\n  //   system: false,/*dm*/
\n  //   id: info.data/*dm*/
\n  // });/*dm*/
\n  // await $.shareData.api_setViewDefaultAction({/*dm*/
\n/*dm*/
\n  // })/*dm*/
\n  this.$message.success(\"保存成功\");/*dm*/
\n}/*dm*/
\n/*dm*/
\n// if (columnPlanId) {/*dm*/
\n/*dm*/
\n//   await $.shareData.api_tablePlanListAction({ modelName: $.shareData.var_setting?.modelName, planId: columnPlanId })/*dm*/
\n/*dm*/
\n// }/*dm*/
\n/*dm*/
\n}}",
													"type":"function"
												}
											]
										},
										"changePlan":{
											"arguments":[
												"value",
												"reset"
											],
											"actions":[
												{
													"handler":"${function Handler(){/*dm*/
\n/*dm*/
\n$.functions.fun_setActiveView(value)/*dm*/
\n  /*dm*/
\nthis.$nextTick(async () => {/*dm*/
\n  await this.$refs.condition.search();/*dm*/
\n})}}",
													"type":"function"
												}
											]
										},
										"planSaveAndDefault":{
											"arguments":[
												"value"
											],
											"actions":[
												{
													"handler":"${function Handler(){/*dm*/
\nif (value.planInfoDtoList && value.planInfoDtoList.length === 0) {/*dm*/
\n  this.$message.error(\"表格显示设置最少要一个字段\");/*dm*/
\n  return/*dm*/
\n}/*dm*/
\n$.shareData.var_pageViewListAction($.shareData.var_pageViewList.map(item => {/*dm*/
\n  return {/*dm*/
\n    ...item,/*dm*/
\n    defaultState: false,/*dm*/
\n  }/*dm*/
\n}))/*dm*/
\nconst allList = this.$refs['record-table'].$refs['view-edit'].getCheckAllData();/*dm*/
\nconst newData = {/*dm*/
\n  id: value.id,/*dm*/
\n  editorViewCode: value.editorViewCode,/*dm*/
\n  viewInfoList: allList.map(item => {/*dm*/
\n    return {/*dm*/
\n      editorViewCode: $.shareData.var_activePlanView.editorViewCode,/*dm*/
\n      fieldName: item.name,/*dm*/
\n      displayWidth: item.fieldWidth,/*dm*/
\n      showIndex: item.index,/*dm*/
\n      isEnable: item.show,/*dm*/
\n      relatedFieldList: item.relatedDtoList.map(child => {/*dm*/
\n        return {/*dm*/
\n          \"editorViewCode\": $.shareData.var_activePlanView.editorViewCode,/*dm*/
\n          \"fieldName\": item.name,/*dm*/
\n          \"displayWidth\": child.displayWidth,/*dm*/
\n          \"showIndex\": child.index,/*dm*/
\n          \"relatedModelName\": child.relatedModelName,/*dm*/
\n          \"relatedFieldName\": child.relatedFieldName,/*dm*/
\n          \"isEnable\": child.show/*dm*/
\n        }/*dm*/
\n      })/*dm*/
\n    }/*dm*/
\n  }),/*dm*/
\n  defaultState: true,/*dm*/
\n  displayDensity: value.tableWidth, // 展示密度/*dm*/
\n  freezeFirstColumn: value.freeze?.head, // 冻结首列/*dm*/
\n  freezeOperateColumn: value.freeze?.action // 冻结尾列/*dm*/
\n}/*dm*/
\n/*dm*/
\nconst info = await $.functions.fun_savePlanView(newData, true)/*dm*/
\n// 刷新/*dm*/
\n/*dm*/
\n/*dm*/
\nif (info?.code !== 200) {/*dm*/
\n/*dm*/
\n  this.$message.error(info.msg);/*dm*/
\n} else {/*dm*/
\n  await $.shareData.api_setViewDefaultAction({/*dm*/
\n    modelName: $.shareData.var_setting?.modelName,/*dm*/
\n    viewCode: value.editorViewCode,/*dm*/
\n    pageKey: $.shareData.var_setting.pageKey/*dm*/
\n  }).catch(() => {/*dm*/
\n    return/*dm*/
\n  })/*dm*/
\n  this.$nextTick(() => {/*dm*/
\n    /*dm*/
\n    $.functions.fun_setActiveView($.shareData.var_pageViewList.find(item => item.editorViewCode === newData.editorViewCode))/*dm*/
\n  })/*dm*/
\n  this.$message.success(\"保存成功\");/*dm*/
\n}/*dm*/
\n                              // const { columnPlanId } = $.shareData.var_tableSetting/*dm*/
\n/*dm*/
\n                              // if (columnPlanId) {/*dm*/
\n/*dm*/
\n                              //   await $.shareData.api_tablePlanListAction({ modelName:$.shareData.var_setting?.modelName, planId: columnPlanId })/*dm*/
\n/*dm*/
\n                              // }/*dm*/
\n}}",
													"type":"function"
												}
											]
										},
										"planSave":{
											"arguments":[
												"value"
											],
											"actions":[
												{
													"handler":"${function Handler(){/*dm*/
\n// const newData = { ...value }/*dm*/
\nif (value.planInfoDtoList && value.planInfoDtoList.length === 0) {/*dm*/
\n  this.$message.error(\"表格显示设置最少要一个字段\");/*dm*/
\n  return/*dm*/
\n}/*dm*/
\n// if (newData.system) {/*dm*/
\n//   delete newData.id/*dm*/
\n//   delete newData.isDefault/*dm*/
\n// }/*dm*/
\n/*dm*/
\nconst allList = this.$refs['record-table'].$refs['view-edit'].getCheckAllData();/*dm*/
\nconst newData = {/*dm*/
\n  editorViewCode: value.editorViewCode,/*dm*/
\n  viewInfoList: allList.map(item => {/*dm*/
\n    return {/*dm*/
\n      editorViewCode: $.shareData.var_activePlanView.editorViewCode,/*dm*/
\n      fieldName: item.name,/*dm*/
\n      displayWidth: item.fieldWidth,/*dm*/
\n      showIndex: item.index,/*dm*/
\n      isEnable: item.show,/*dm*/
\n      relatedFieldList: item.relatedDtoList.map(child => {/*dm*/
\n        return {/*dm*/
\n          \"editorViewCode\": $.shareData.var_activePlanView.editorViewCode,/*dm*/
\n          \"fieldName\": item.name,/*dm*/
\n          \"displayWidth\": child.displayWidth,/*dm*/
\n          \"showIndex\": child.index,/*dm*/
\n          \"relatedModelName\": child.relatedModelName,/*dm*/
\n          \"relatedFieldName\": child.relatedFieldName,/*dm*/
\n          \"isEnable\": child.show/*dm*/
\n        }/*dm*/
\n      })/*dm*/
\n    }/*dm*/
\n  }),/*dm*/
\n  displayDensity: value.tableWidth, // 展示密度/*dm*/
\n  freezeFirstColumn: value.freeze?.head, // 冻结首列/*dm*/
\n  freezeOperateColumn: value.freeze?.action // 冻结尾列/*dm*/
\n}/*dm*/
\n/*dm*/
\ndebugger;/*dm*/
\n/*dm*/
\nconst info = await $.functions.fun_savePlanView(newData, true)/*dm*/
\n/*dm*/
\nif (info?.code !== 200) {/*dm*/
\n  this.$message.error(info.msg);/*dm*/
\n} else {/*dm*/
\n/*dm*/
\n  if(value.editorViewCode === $.shareData.var_activePlanView.editorViewCode) {/*dm*/
\n    $.functions.fun_setActiveView($.shareData.var_pageViewList.find(item => item.editorViewCode === value.editorViewCode));/*dm*/
\n  }/*dm*/
\n/*dm*/
\n/*dm*/
\n  // if (newData.system) {/*dm*/
\n  //   await $.shareData.api_setTablePlanDefaultAction({/*dm*/
\n  //     modelName: $.shareData.var_setting?.modelName,/*dm*/
\n  //     planId: info.data/*dm*/
\n  //   })/*dm*/
\n  //   this.$refs['record-table'].currentPlan = {/*dm*/
\n  //     ...newData,/*dm*/
\n  //     system: false,/*dm*/
\n  //     isDefault: true,/*dm*/
\n  //     id: info.data/*dm*/
\n  //   }/*dm*/
\n  // }/*dm*/
\n  this.$message.success(\"保存成功\");/*dm*/
\n  // const planInfoDtoList = this.$refs['record-table']?.$refs['view-edit']?.getCheckAllData()/*dm*/
\n  // $.shareData.var_tablePlanFieldsAction(planInfoDtoList)/*dm*/
\n}/*dm*/
\n// const planInfoDtoList = this.$refs['record-table']?.$refs['view-edit']?.getCheckAllData()/*dm*/
\n// $.shareData.var_tablePlanFieldsAction(planInfoDtoList)/*dm*/
\n/*dm*/
\n// const { columnPlanId } = $.shareData.var_tableSetting/*dm*/
\n/*dm*/
\n// if (columnPlanId) {/*dm*/
\n//   await $.shareData.api_tablePlanListAction({ modelName: $.shareData.var_setting?.modelName, planId: columnPlanId })/*dm*/
\n// }/*dm*/
\n}}",
													"type":"function"
												}
											]
										},
										"handleSizeChange":{
											"arguments":[
												"val"
											],
											"actions":[
												{
													"handler":"${function Fun81901(val){/*dm*/
\n                                      // 缓存到本地/*dm*/
\nlocalStorage.setItem(\"pageParams_ModelTable36800\", JSON.stringify({ pageSize: val }))/*dm*/
\nawait $.shareData.var_pageParamsAction({ pageSize: val });/*dm*/
\n$.functions.fun_loadData()/*dm*/
\n                                    }}",
													"type":"function"
												}
											]
										},
										"setChage":{
											"arguments":[
												"value"
											],
											"actions":[
												{
													"handler":"${function handler(value){                            //   const { planInfoDtoList,freeze,...other } = value/*dm*/
\n/*dm*/
\n                            //   const listInner = [...planInfoDtoList]/*dm*/
\n/*dm*/
\n                            //  $.functions.fun_filterTableColumn(this.$refs['record-table']?.$refs['view-edit']?.getSelectedTreeData())/*dm*/
\n/*dm*/
\n                            //   let freezeNew = {action:true,head:true}/*dm*/
\n                            //   if(freeze){/*dm*/
\n                            //     freezeNew = freeze/*dm*/
\n                            //   }/*dm*/
\n                            //   $.shareData.var_tablePlanOtherInfoAction({freeze:freezeNew, ...other})/*dm*/
\n                            //   $.functions.fun_loadData()/*dm*/
\n                            }}",
													"type":"function"
												}
											]
										},
										"planRename":{
											"arguments":[
												"value"
											],
											"actions":[
												{
													"handler":"${function Handler(){/*dm*/
\nawait $.shareData.api_reViewNameAction({ /*dm*/
\n  modelName: $.shareData.var_setting.modelName,/*dm*/
\n  pageKey: $.shareData.var_setting.pageKey,/*dm*/
\n  editorViewCode: value.editorViewCode,/*dm*/
\n  editorViewName: value.label/*dm*/
\n});/*dm*/
\n/*dm*/
\n$.shareData.var_pageViewListAction($.shareData.var_pageViewList.map(item => {/*dm*/
\n  if(item.editorViewCode === value.editorViewCode) {/*dm*/
\n    return {/*dm*/
\n      ...item,/*dm*/
\n      label: value.label,/*dm*/
\n      editorViewName: value.label/*dm*/
\n    }/*dm*/
\n  }/*dm*/
\n  return item;/*dm*/
\n}))/*dm*/
\n/*dm*/
\nconst info = $.shareData.api_reViewName;/*dm*/
\nif (info?.code!==200){/*dm*/
\n  this.$message.error(info.msg);/*dm*/
\n}else{/*dm*/
\n  this.$message.success(\"保存成功\");/*dm*/
\n}/*dm*/
\n// const { columnPlanId } = $.shareData.var_tableSetting/*dm*/
\n/*dm*/
\n// if (columnPlanId) {/*dm*/
\n/*dm*/
\n//   await $.shareData.api_tablePlanListAction({ modelName:$.shareData.var_setting?.modelName, planId: columnPlanId })/*dm*/
\n/*dm*/
\n// }/*dm*/
\n                            }}",
													"type":"function"
												}
											]
										},
										"planDelte":{
											"arguments":[
												"value"
											],
											"actions":[
												{
													"handler":"${function Handler(){/*dm*/
\nawait $.shareData.api_deleteViewAction({/*dm*/
\n  modelName: $.shareData.var_setting.modelName,/*dm*/
\n  pageKey: $.shareData.var_setting.pageKey,/*dm*/
\n  viewCode: value.editorViewCode/*dm*/
\n})/*dm*/
\n/*dm*/
\nconst {code,msg} = $.shareData.api_deleteView/*dm*/
\n/*dm*/
\nif (code&&code!==200){/*dm*/
\n  this.$message.error(msg)/*dm*/
\n}else{/*dm*/
\n/*dm*/
\n  $.shareData.var_pageViewListAction($.shareData.var_pageViewList.filter(item => item.editorViewCode !== value.editorViewCode))/*dm*/
\n/*dm*/
\n  // const { columnPlanId } = $.shareData.var_tableSetting/*dm*/
\n/*dm*/
\n/*dm*/
\n  // if (columnPlanId) {/*dm*/
\n/*dm*/
\n/*dm*/
\n  //   await $.shareData.api_tablePlanListAction({ modelName:$.shareData.var_setting?.modelName, planId: columnPlanId })/*dm*/
\n/*dm*/
\n/*dm*/
\n  // }/*dm*/
\n/*dm*/
\n/*dm*/
\n}/*dm*/
\n/*dm*/
\n/*dm*/
\n                            }}",
													"type":"function"
												}
											]
										},
										"handleCurrentChange":{
											"arguments":[
												"value"
											],
											"actions":[
												{
													"handler":"${function Fun63718(value){/*dm*/
\n                                      await $.shareData.var_pageParamsAction({ pageNum: value });/*dm*/
\n                                      $.functions.fun_loadData();/*dm*/
\n                                    }}",
													"type":"function"
												}
											]
										},
										"planSetDefault":{
											"arguments":[
												"value"
											],
											"actions":[
												{
													"handler":"${function Handler(){/*dm*/
\nawait $.shareData.api_setViewDefaultAction({/*dm*/
\n  modelName: $.shareData.var_setting?.modelName,/*dm*/
\n  viewCode: value.editorViewCode,/*dm*/
\n  pageKey: $.shareData.var_setting.pageKey/*dm*/
\n}).catch(() => {/*dm*/
\n  return/*dm*/
\n}).then(() => {/*dm*/
\n  $.shareData.var_pageViewListAction($.shareData.var_pageViewList.map(item => {/*dm*/
\n    if (item.editorViewCode === value.editorViewCode) {/*dm*/
\n      return {/*dm*/
\n        ...item,/*dm*/
\n        isDefault: true,/*dm*/
\n        defaultState: true,/*dm*/
\n      }/*dm*/
\n    } else {/*dm*/
\n      return {/*dm*/
\n        ...item,/*dm*/
\n        isDefault: false,/*dm*/
\n        defaultState: false,/*dm*/
\n      }/*dm*/
\n    }/*dm*/
\n  }))/*dm*/
\n  this.$message.success(\"设置成功\");/*dm*/
\n})/*dm*/
\n/*dm*/
\n                              // const { columnPlanId } = $.shareData.var_tableSetting/*dm*/
\n/*dm*/
\n                              // if (columnPlanId) {/*dm*/
\n/*dm*/
\n                              //   await $.shareData.api_tablePlanListAction({ modelName:$.shareData.var_setting?.modelName, planId: columnPlanId })/*dm*/
\n/*dm*/
\n                              // }/*dm*/
\n/*dm*/
\n}}",
													"type":"function"
												}
											]
										}
									}
								},
								"componentName":"ModelTable",
								"id":"ModelTable-4tGiAYFd",
								"title":"列表组件",
								"version":"1.8.3",
								"props":{
									"pageSizes":"${$.shareData.var_tableSetting?.pageSizes}",
									"editPage":"796413391765319682",
									"resizeColumn":"${$.shareData.var_tableSetting?.resizeColumn && !$.shareData.var_tableSetting?.border}",
									"tablePlanInfo":"${$.shareData.var_activePlanView}",
									"slot":"table",
									"showRefresh":false,
									"batchDelFlag":"",
									"displayFields":"${$.shareData.var_activePlanView.planInfoDtoList}",
									"buttonShowText":true,
									"mode":"${$.shareData.var_tableSetting?.mode}",
									"columnShowIcon":false,
									"tableBorderBottom":"${$.shareData.var_tableSetting?.tableBorderBottom}",
									"ref":"record-table",
									"showPlanView":"${!$.shareData.var_findRelationDialog && $.functions.fun_getTableConfig().showPlanView}",
									"columnPlanId":"792010462761443330",
									"addPage":"796413391765319682",
									"paginationMode":"${$.shareData.var_tableSetting?.paginationMode}",
									"model":"multiple",
									"activePlan":"${$.shareData.var_activePlanView}",
									"buttonShowIcon":false,
									"showPagination":"${$.shareData.var_tableSetting?.showPagination}",
									"addFlag":true,
									"modelName":"ceshimoxing",
									"exportDataFlag":false,
									"exportUrl":"${'/api/v1/model/'+ $.shareData.var_setting.modelName +'/export'}",
									"pageParams":"${$.shareData.var_pageParams}",
									"detailPage":"792010485771395074",
									"planList":"${$.shareData.var_pageViewList.map(item => {/*dm*/
\n  return {/*dm*/
\n    ...item,/*dm*/
\n    isDefault: item.defaultState,/*dm*/
\n    label: item.editorViewName/*dm*/
\n  }/*dm*/
\n})}",
									"draftFlag":false,
									"uniqueId":"var_tableSetting",
									"multipleSelection":"${$.shareData.var_multipleSelection}"
								},
								"attrs":{
									"style":{
										"height":"100%"
									}
								}
							}
						],
						"componentName":"Page",
						"id":"Page-nB2r8cK5",
						"title":"页面组件",
						"version":"1.8.3",
						"props":{
							"modelName":"ceshimoxing",
							"show-table":true,
							"show-search":true,
							"searchMethod":"advancedSearch"
						},
						"attrs":{
							"class":"records-joy-table"
						}
					}
				],
				"componentName":"DivWrapper",
				"id":"DivWrapper-G2E3JmwY",
				"title":"测试模型列表页面",
				"attrs":{
					"class":"${$.shareData.var_styleFlag ? 'recordsPageDialog' : 'recordsPage'}"
				},
				"props":{
					"name":"DivWrapper"
				}
			},
			{
				"children":[
					{
						"componentName":"TextWrapper",
						"id":"TextWrapper-kxCfhG8p",
						"title":"草稿箱标题",
						"props":{
							"children":"草稿箱",
							"slot":"title"
						}
					},
					{
						"children":[
							{
								"componentType":"custom",
								"children":[
									{
										"componentType":"custom",
										"children":[
											{
												"componentType":"custom",
												"componentName":"Image",
												"id":"Image-HwFXhrFk",
												"props":{
													"src":"${\"http://s3.cn-north-1.jdcloud-oss.com/jz-material/lcapicon/1658309481895-3905270251763847417.png\"}"
												},
												"chosen":false,
												"attrs":{
													"style":{
														"width":"120px"
													}
												}
											},
											{
												"componentType":"custom",
												"componentName":"TextWrapper",
												"id":"TextWrapper-jQGttwAF",
												"attrs":{
													"style":{
														"margin":"-30px 0 0 0",
														"color":"rgba(129,128,148,1)",
														"font-weight":"normal",
														"display":"block",
														"width":"100%",
														"font-size":"14px"
													}
												},
												"props":{
													"children":" 暂无内容"
												},
												"chosen":false
											}
										],
										"componentName":"DivWrapper",
										"id":"DivWrapper-zRcrYKMT",
										"title":"暂无数据",
										"attrs":{
											"style":{
												"background-color":"#ffffff",
												"padding":"100px 24px 24px",
												"width":"100%",
												"min-height":"320px",
												"height":"100%"
											}
										},
										"props":{
											"name":"DivWrapper",
											"slot":"empty"
										},
										"chosen":false
									},
									{
										"componentType":"custom",
										"children":[
											{
												"componentType":"custom",
												"componentName":"TableColumn",
												"id":"TableColumn-FkjSJG4c",
												"props":{
													"show-overflow-tooltip":true,
													"reserve-selection":true,
													":selectable":"${functions(scope){ return scope['$extra']?.dataPermission ==2 }}",
													"width":"48",
													"fixed":"left",
													"type":"selection",
													"align":"center",
													"v-slot":"scope"
												}
											}
										],
										"componentName":"VIf",
										"id":"VIf-iB6TSmQM",
										"props":{
											"conditions":"${[false]}"
										}
									},
									{
										"componentType":"custom",
										"children":[
											{
												"componentType":"custom",
												"componentName":"TableColumn",
												"id":"TableColumn56039",
												"props":{
													"resizable":false,
													"width":"60",
													"fixed":"left",
													"label":"序号",
													"type":"index",
													"align":"center",
													"class-name":"color-text-secondary"
												}
											}
										],
										"componentName":"VIf",
										"id":"VIf-QaRDPai8",
										"props":{
											"conditions":"${[$.shareData.var_tableSetting?.showNo]}"
										}
									},
									{
										"componentType":"custom",
										"children":[
											{
												"componentType":"custom",
												"children":[
													{
														"libraryName":"@jd/joyui",
														"componentType":"UILibrary",
														"action":{
															"fire":{
																"navtoDetail":{
																	"arguments":[
																		"field",
																		"modelName"
																	],
																	"actions":[
																		{
																			"handler":"${function Fun6837(field, modelName){/*dm*/
\nconst obj = {/*dm*/
\n  detailId: scope.row[item.name]?.id || scope.row.id,/*dm*/
\n  permission: scope.row.$extra?.dataPermission/*dm*/
\n}/*dm*/
\nlet jumpUrl/*dm*/
\nif (item.modelField.businessType == 'findRelated') {/*dm*/
\n  jumpUrl = $.functions.fun_getJumpUrl('fieldName', obj, item.modelField.relatedModel.modelName);/*dm*/
\n} else {/*dm*/
\n  jumpUrl = $.functions.fun_getJumpUrl('detail', obj);/*dm*/
\n}/*dm*/
\n$.bom.page.open(jumpUrl, {}, () => {/*dm*/
\n  $.functions.fun_loadData();/*dm*/
\n}, {});/*dm*/
\n}}",
																			"type":"function"
																		}
																	]
																},
																"handleDownload":{
																	"arguments":[
																		"file"
																	],
																	"actions":[
																		{
																			"handler":"${function handler(file){/*dm*/
\nawait $.shareData.api_getPreDownloadAction({ originFileName: file.originFileName })/*dm*/
\n/*dm*/
\nfetch($.shareData.api_getPreDownload.presignedObjectUrl, {/*dm*/
\n  method: 'GET',/*dm*/
\n  responseType: 'blob',/*dm*/
\n}).then(/*dm*/
\n  res => {/*dm*/
\n    return res.blob();/*dm*/
\n  })/*dm*/
\n  .then(blob => {/*dm*/
\n/*dm*/
\n    let bl = new Blob([blob]);/*dm*/
\n/*dm*/
\n    var link = document.createElement('a');/*dm*/
\n/*dm*/
\n    link.href = window.URL.createObjectURL(bl);/*dm*/
\n/*dm*/
\n    link.download = file.name;/*dm*/
\n/*dm*/
\n    link.click();/*dm*/
\n/*dm*/
\n    window.URL.revokeObjectURL(link.href);/*dm*/
\n/*dm*/
\n  }).catch(err => {/*dm*/
\n    console.log('error', error)/*dm*/
\n  });/*dm*/
\n}}",
																			"type":"function"
																		}
																	]
																}
															}
														},
														"componentName":"ModelField",
														"id":"ModelField-CRr8c3N2",
														"title":"模型字段",
														"version":"1.8.3",
														"props":{
															"editField":"${{\r/*dm*/
\n  jumpOrNot:false\r/*dm*/
\n}}",
															"originProps":{},
															"formType":"table",
															"fieldProps":"${item.modelField}",
															"v-open":"${() => {\r/*dm*/
\n  const pageId = $.shareData.var_findRelatedPages[item.modelField.fieldName];\r/*dm*/
\n  if (!pageId) { return; }\r/*dm*/
\n  const obj = {\r/*dm*/
\n    detailId: scope.row[item.name]?.id || scope.row.id,\r/*dm*/
\n    permission: scope.row.$extra?.dataPermission\r/*dm*/
\n  }\r/*dm*/
\n  let jumpUrl\r/*dm*/
\n  if (item.modelField.businessType == 'findRelated') {\r/*dm*/
\n    jumpUrl = $.functions.fun_getJumpUrl('fieldName', obj, item.modelField.fieldName);\r/*dm*/
\n  } else {\r/*dm*/
\n    jumpUrl = $.functions.fun_getJumpUrl('detail', obj);\r/*dm*/
\n  }\r/*dm*/
\n\r/*dm*/
\n  return jumpUrl\r/*dm*/
\n}}",
															"businessType":"${item.businessType}",
															"value":"${scope.row[item.name]}"
														}
													}
												],
												"componentName":"TableColumn",
												"id":"TableColumn-zJMyBZXk",
												"props":{
													"show-overflow-tooltip":"${$.shareData.var_tableSetting.cellEllipsis}",
													"prop":"${item.fieldName}",
													"fixed":false,
													"label":"${item.label}",
													"sortable":false,
													"align":"${$.shareData.var_tableSetting.textAlign}",
													"v-slot":"scope",
													"min-width":"${() => {\r/*dm*/
\n            const { minWidth } = $.functions?.fun_getFieldsBusinessType($.shareData.var_setting.modelName + 'Draft', item.fieldName);\r/*dm*/
\n            const fieldNameShow = {id: \"190px\"};\r/*dm*/
\n            const resizeWidth = $.shareData.tableColumnWidth?.widths[item.fieldName]\r/*dm*/
\n            if (resizeWidth) {\r/*dm*/
\n              return resizeWidth + 'px'\r/*dm*/
\n            }\r/*dm*/
\n            return  fieldNameShow[item.name]  || minWidth || \"140px\"\r/*dm*/
\n}\r/*dm*/
\n          }"
												}
											}
										],
										"componentName":"VFor",
										"id":"VFor-zZ8jZJxR",
										"title":"table列循环容器",
										"props":{
											"forEach":"${$.shareData.var_columnsShow.map(item => {/*dm*/
\n        return {/*dm*/
\n          name: item.fieldName,/*dm*/
\n          label: item.fieldText,/*dm*/
\n          defaultSelectValue: item.defaultSelect,/*dm*/
\n          ...item,/*dm*/
\n        }/*dm*/
\n      })}",
											"forEachKey":"item",
											"index":"$index"
										}
									},
									{
										"componentType":"custom",
										"children":[
											{
												"componentType":"custom",
												"children":[
													{
														"libraryName":"@jd/joyui",
														"componentType":"UILibrary",
														"action":{
															"fire":{
																"tab-click":{
																	"arguments":[
																		"tab"
																	],
																	"actions":[
																		{
																			"handler":"${function  Fun58813(){/*dm*/
\n                                        if (tab == 'edit') {/*dm*/
\n                                          let jumpUrl = $.functions.fun_getJumpUrl('edit', { draftId:scope.row.$__draftId })/*dm*/
\n                                          if(jumpUrl){/*dm*/
\n                                            $.shareData.var_draftDrawerAction(false);/*dm*/
\n                                            this.$nextTick(() => {/*dm*/
\n                                              $.bom.page.open(jumpUrl, {}, () => {/*dm*/
\n                                                $.functions.fun_loadData();/*dm*/
\n                                              }, {});/*dm*/
\n                                            })/*dm*/
\n                                          }/*dm*/
\n                                        }/*dm*/
\n                                        if (tab == 'delete') {/*dm*/
\n                                          this.$confirm('此操作将永久删除, 是否继续?', '提示', {/*dm*/
\n                                            confirmButtonText: '确定',/*dm*/
\n                                            cancelButtonText: '取消',/*dm*/
\n                                            type: 'warning'/*dm*/
\n                                          }).then(() => {/*dm*/
\n                                            $.shareData.api_deleteDraftAction({id: scope.row?.$__draftId}).then(() => {/*dm*/
\n                                              if ($.shareData.api_deleteDraft) {/*dm*/
\n                                                this.$message.success('删除成功!');/*dm*/
\n                                                $.functions.fun_loadDraftData();/*dm*/
\n                                              } else {/*dm*/
\n                                                this.$message.error('删除失败')/*dm*/
\n                                              }/*dm*/
\n                                            }).catch((error) => {/*dm*/
\n                                              this.$message.error(error)/*dm*/
\n                                            })/*dm*/
\n                                          }).catch(() => {})/*dm*/
\n                                        }/*dm*/
\n                                      }}",
																			"type":"function"
																		}
																	]
																}
															}
														},
														"componentName":"MoreButton",
														"id":"MoreButton-A6YrAbaj",
														"version":"1.8.3",
														"props":{
															"styleState":"${[]}",
															"padding":"0 16px 0px 0px",
															"data":"${/*dm*/
\n() => {/*dm*/
\n  let list = [];/*dm*/
\n  let edit = false ? $.shareData.permissionData?.edit : true;/*dm*/
\n  let deletes = false ? $.shareData.permissionData?.delete : true;/*dm*/
\n  if (edit) {/*dm*/
\n    const obj = {/*dm*/
\n      detailId: scope.row?.id/*dm*/
\n    }/*dm*/
\n    list.push({ \"label\": \"编辑\", \"name\": \"edit\", \"type\": \"text\", \"size\": \"small\", \"v-open\": $.functions.fun_getJumpUrl('edit', obj) })/*dm*/
\n  };/*dm*/
\n  if (deletes) {/*dm*/
\n    list.push({ \"label\": \"删除\", \"name\": \"delete\", \"type\": \"text\", \"size\": \"small\" });/*dm*/
\n  };/*dm*/
\n  return list;/*dm*/
\n}}",
															"moreRound":"${false}",
															"max-count":"${2}",
															"textAlign":"left",
															"marginLeft":"${0 * 1}"
														}
													}
												],
												"componentName":"TableColumn",
												"id":"TableColumn43082",
												"props":{
													"prop":"action",
													"fixed":"${() => {/*dm*/
\n  const { action=true } = $.shareData.var_tablePlanOtherInfo.freeze/*dm*/
\n  return action ? \"right\" : action/*dm*/
\n}}",
													"label":"操作",
													"v-slot":"scope",
													"min-width":"160px"
												}
											}
										],
										"componentName":"VIf",
										"id":"VIf-MZXw4Ket",
										"props":{
											"conditions":"${[ $.shareData.var_tableSetting?.mode==='normal']}"
										}
									}
								],
								"action":{
									"fire":{
										"current-change":{
											"arguments":[
												"val"
											],
											"actions":[
												{
													"handler":"${function Fun52627(val){/*dm*/
\n                                    if($.shareData.var_tableSetting?.selectRowType==='single'){/*dm*/
\n                                      $.shareData.var_singleSelectionAction(val?.id)/*dm*/
\n                                    }/*dm*/
\n                                  }}",
													"type":"function"
												}
											]
										},
										"summary-method":{
											"arguments":[
												"param"
											],
											"actions":[
												{
													"handler":"${function Fun81208(param){/*dm*/
\n                                          const { columns, data } = param;/*dm*/
\n                                          const sums = [];/*dm*/
\n                                          columns.forEach((column, index) => {/*dm*/
\n                                            if (index === 0) {/*dm*/
\n                                              sums[index] = '合计';/*dm*/
\n                                              return;/*dm*/
\n                                            }/*dm*/
\n                                            const values = data.map((item) => Number(item[column.property]));/*dm*/
\n                                            $.shareData.summaryList?.forEach((item) => {/*dm*/
\n                                              if (/*dm*/
\n                                                item == column.property && !values.every((value) => isNaN(value))/*dm*/
\n                                              ) {/*dm*/
\n                                                sums[index] = values.reduce((prev, curr) => {/*dm*/
\n                                                  const value = Number(curr);/*dm*/
\n                                                  if (!isNaN(value)) {/*dm*/
\n                                                    return prev + curr;/*dm*/
\n                                                  } else {/*dm*/
\n                                                    return prev;/*dm*/
\n                                                  }/*dm*/
\n                                                }, 0);/*dm*/
\n                                                sums[index] += '';/*dm*/
\n                                              } else if (item == column.property) {/*dm*/
\n                                                sums[index] = 'N/A';/*dm*/
\n                                              } else {/*dm*/
\n                                                sums[index] = '';/*dm*/
\n                                              }/*dm*/
\n                                            });/*dm*/
\n                                          });/*dm*/
\n                                          return sums;/*dm*/
\n                                        }}",
													"type":"function"
												}
											]
										},
										"sort-change":{
											"arguments":[
												"column",
												"prop"
											],
											"actions":[
												{
													"handler":"${function Fun_47sXNY(column, prop){if ($.shareData.var_querySorts.length > 1){\r/*dm*/
\n  $.shareData.var_querySorts.splice(0, 1);\r/*dm*/
\n}\r/*dm*/
\n\r/*dm*/
\n\r/*dm*/
\nif (column.order){\r/*dm*/
\n  $.shareData.var_querySorts.unshift({\r/*dm*/
\n    fieldName: column.prop,\r/*dm*/
\n    desc: column.order == \"descending\"\r/*dm*/
\n  })\r/*dm*/
\n}\r/*dm*/
\n    \r/*dm*/
\n\r/*dm*/
\nawait $.functions.fun_loadData();\r/*dm*/
\n}}",
													"type":"function"
												}
											]
										},
										"selection-change":{
											"arguments":[
												"selection"
											],
											"actions":[
												{
													"handler":"${function Fun93098(selection){/*dm*/
\n                                    if($.shareData.var_tableSetting?.selectRowType==='many'){/*dm*/
\n                                      $.shareData.var_multipleSelectionAction(selection)/*dm*/
\n                                    }/*dm*/
\n                                  }}",
													"type":"function"
												}
											]
										}
									}
								},
								"componentName":"Table",
								"id":"Table-SPMxYjy2",
								"title":"表格区域",
								"attrs":{
									"class":"${$.shareData.var_tableSetting?.headerEllipsis?'headerEllipsis':''}"
								},
								"props":{
									"border":"${$.shareData.var_tableSetting?.border}",
									"headerEllipsis":"${$.shareData.var_tableSetting.headerEllipsis}",
									"data":"${$.shareData.var_draftData || []}",
									"show-header":"${$.shareData.var_tableSetting.showHeader}",
									"show-summary":"${$.shareData.summaryList?.length>0}",
									"size":"small",
									"v-loading":"${$.shareData.var_pageLoading}",
									"tooltip-effect":"dark",
									"tree-props":"${{ hasChildren: \"hasChildren\", children: \"children\" }}",
									"stripe":"${$.shareData.var_tableSetting.stripe}",
									"style":"width: 100%",
									"height":"calc(100vh - 160px)",
									"row-key":"id",
									"highlight-current-row":"${$.shareData.var_tableSetting?.selectRowType==='single'}"
								}
							}
						],
						"componentName":"DivWrapper",
						"id":"DivWrapper-MEAxeczw",
						"title":"草稿箱内容"
					}
				],
				"action":{
					"fire":{
						"close":{
							"actions":[
								{
									"handler":"${function() {/*dm*/
\n $.shareData.var_draftDrawerAction(false);/*dm*/
\n}}",
									"type":"function"
								}
							]
						}
					}
				},
				"componentName":"Drawer",
				"id":"Drawer-sc67QXtM",
				"title":"草稿箱弹窗",
				"props":{
					"modal-append-to-body":false,
					"visible":"${$.shareData.var_draftDrawer}",
					"size":"80%"
				}
			},
			{
				"children":[
					{
						"componentType":"custom",
						"children":[
							{
								"componentType":"custom",
								"children":[
									{
										"componentType":"custom",
										"children":[
											{
												"componentName":"TextWrapper",
												"id":"TextWrapper-btk4RypD",
												"props":{
													"children":"${\"按照模版要求完善内容\"}"
												},
												"attrs":{
													"style":{}
												}
											}
										],
										"componentName":"DivWrapper",
										"id":"DivWrapper-TfMBtSTD",
										"attrs":{
											"style":{
												"margin-right":"6px"
											}
										},
										"props":{
											"name":"DivWrapper"
										}
									},
									{
										"action":{
											"fire":{
												"click":{
													"arguments":[],
													"actions":[
														{
															"handler":"${function handler(){/*dm*/
\nconst modelName = $.shareData?.var_setting?.modelName;/*dm*/
\nlet attributes = $.shareData.var_columnsShow.map(item => {/*dm*/
\n  return item.fieldName/*dm*/
\n})/*dm*/
\nawait $.shareData.api_downloadTemplateAction({/*dm*/
\n  modelName,/*dm*/
\n  attributes: attributes.join(',')/*dm*/
\n})/*dm*/
\nconst url = window.URL.createObjectURL(new Blob([$.shareData.api_downloadTemplate?.data]));/*dm*/
\nlet link = document.createElement('a');/*dm*/
\nlink.style.display = 'none';/*dm*/
\nlink.href = url;/*dm*/
\nlink.setAttribute('download', '导入模板.xlsx');/*dm*/
\ndocument.body.appendChild(link);/*dm*/
\nlink.click();/*dm*/
\nURL.revokeObjectURL(link.href);/*dm*/
\ndocument.body.removeChild(link);/*dm*/
\nlink = null;/*dm*/
\n}}",
															"type":"function"
														}
													]
												}
											}
										},
										"componentName":"Button",
										"id":"Button-XeWdMKeb",
										"attrs":{
											"style":{
												"font-size":"13px"
											}
										},
										"props":{
											"size":"small",
											"children":"${\"下载模板\"}",
											"type":"${\"text\"}"
										}
									}
								],
								"componentName":"DivWrapper",
								"id":"DivWrapper-Z4ASDEfy",
								"attrs":{
									"style":{
										"display":"flex",
										"font-size":"13px",
										"align-items":"center",
										"height":"30px"
									}
								},
								"props":{
									"name":"DivWrapper"
								}
							},
							{
								"componentType":"custom",
								"children":[
									{
										"componentName":"TextWrapper",
										"id":"TextWrapper-dikCFaEj",
										"props":{
											"children":""
										},
										"attrs":{
											"style":{
												"color":"#4871e4",
												"padding-left":"10px",
												"padding-right":"3px"
											},
											"class":"el-icon-info"
										}
									},
									{
										"componentName":"TextWrapper",
										"id":"TextWrapper-w3pGE4bj",
										"props":{
											"children":"上传文件，系统将按照匹配字段新增与更新已有数据"
										},
										"attrs":{
											"style":{
												"color":"rgba(96,98,102,1)",
												"font-weight":"normal",
												"font-size":"10px",
												"font-family":"PingFang SC",
												"height":"15px"
											}
										}
									}
								],
								"componentName":"DivWrapper",
								"id":"DivWrapper-JkeZNBHY",
								"attrs":{
									"style":{
										"border-radius":"3px",
										"background":"rgba(228,240,255,1)",
										"display":"flex",
										"width":"100%",
										"align-items":"center",
										"margin-bottom":"8px",
										"height":"28px"
									}
								},
								"props":{
									"name":"DivWrapper"
								}
							},
							{
								"componentType":"custom",
								"children":[
									{
										"componentType":"custom",
										"children":[
											{
												"children":[
													{
														"componentType":"custom",
														"children":[
															{
																"componentType":"custom",
																"componentName":"Image",
																"id":"Image-24xTeZK5",
																"title":"图片",
																"props":{
																	"src":"${'data:image/png;base64,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'}"
																},
																"attrs":{
																	"style":{
																		"width":"52px",
																		"height":"38px"
																	}
																}
															}
														],
														"componentName":"DivWrapper",
														"id":"DivWrapper-DrfXz72N",
														"attrs":{
															"style":{
																"width":"100%",
																"margin-bottom":"4px",
																"height":"38px"
															}
														},
														"props":{
															"name":"DivWrapper"
														}
													},
													{
														"componentType":"custom",
														"children":[
															{
																"componentName":"TextWrapper",
																"id":"TextWrapper-yxYTChxc",
																"props":{
																	"children":"${'将文件拖到此处，或'\r/*dm*/
\n}"
																},
																"attrs":{
																	"style":{}
																}
															},
															{
																"action":{
																	"fire":{
																		"click":{
																			"arguments":[],
																			"actions":[
																				{
																					"handler":"${function handler(){ \r/*dm*/
\n// setTimeout(()=>{\r/*dm*/
\n  $.shareData.fileStreamAction('')\r/*dm*/
\n// },1000)}}",
																					"type":"function"
																				}
																			]
																		}
																	}
																},
																"componentName":"Button",
																"id":"Button-J8cPW6Fs",
																"attrs":{},
																"props":{
																	"size":"small",
																	"children":"点击上传",
																	"plain":"${}",
																	"type":"${\"text\"}"
																}
															}
														],
														"componentName":"DivWrapper",
														"id":"DivWrapper-SH7RSaC7",
														"attrs":{
															"style":{}
														},
														"props":{
															"name":"DivWrapper"
														}
													},
													{
														"componentType":"custom",
														"children":[
															{
																"componentName":"TextWrapper",
																"id":"TextWrapper-84f4HG4f",
																"props":{
																	"children":"${'注：支持上传文件为Excel，且文件大小20MB以内'\r/*dm*/
\n}"
																},
																"attrs":{
																	"style":{
																		"color":"rgba(144,147,153,1)",
																		"font-size":"10px"
																	}
																}
															}
														],
														"componentName":"DivWrapper",
														"id":"DivWrapper-P86mZjWi",
														"title":"提示",
														"attrs":{
															"style":{
																"display":"flex",
																"margin-top":"4px"
															}
														},
														"props":{
															"name":"DivWrapper",
															"slot":"tip"
														}
													}
												],
												"componentName":"Upload",
												"id":"Upload-QEDK4PXX",
												"props":{
													"list-type":"${\"text\"}",
													":http-request":"${function handler(params){/*dm*/
\n                            const _file = params.file;/*dm*/
\n                            // 通过 FormData 对象上传文件/*dm*/
\n                            var formData = new FormData();/*dm*/
\n                            formData.append('file', _file);/*dm*/
\n                            formData.modelName = $.shareData.var_setting.modelName/*dm*/
\n                            $.shareData.var_fileListUploadAction([]);/*dm*/
\n                            $.shareData.var_importErrorDataAction([])/*dm*/
\n                            /*dm*/
\n                            $.shareData.api_importTemplateAction(formData).then(data => {/*dm*/
\n                              if (data.extra.importSuccess) {/*dm*/
\n                                $.shareData.var_fileRetAction('allSuccess')/*dm*/
\n                                this.$message.success('上传成功');/*dm*/
\n                                $.shareData.var_importDialogVisibleAction(false);/*dm*/
\n                                $.shareData.var_fileRetAction('')/*dm*/
\n                                $.shareData.var_pageParamsAction({ pageNum: 1 });/*dm*/
\n                                this.$refs.condition.search();/*dm*/
\n                              } else {/*dm*/
\n                                $.shareData.var_fileRetAction('fail')/*dm*/
\n                                $.shareData.var_importErrorDataAction(data.data);/*dm*/
\n                              }/*dm*/
\n                            }).catch(err => {/*dm*/
\n                              $.shareData.var_fileListUploadAction([]);/*dm*/
\n                            })/*dm*/
\n                          }}",
													":on-exceed":"${function handler(){/*dm*/
\nthis.$message.error(\"最多只能上传一个文件!\")/*dm*/
\n}}",
													":on-success":"${function handler(file){\r/*dm*/
\nconsole.log(\"成功：\", file)\r/*dm*/
\n\r/*dm*/
\n}}",
													":before-upload":"${function handler(file){let arr = file.name.split(\".\");\r/*dm*/
\nif (arr[arr.length - 1] != \"xls\" && arr[arr.length - 1] != \"xlsx\") {\r/*dm*/
\n  this.$message.error(\"上传文件格式有误，请上传excel\");\r/*dm*/
\n  return false\r/*dm*/
\n}\r/*dm*/
\nif (file.size / 1024 / 1024 > 20) {\r/*dm*/
\n  this.$message.error(\"文件大小超过20MB，请重新上传\");\r/*dm*/
\n  return false\r/*dm*/
\n} else {\r/*dm*/
\n  return true\r/*dm*/
\n}}}",
													"limit":"${1}",
													"name":"${'file'}",
													"action":"${`/api/v1/model/${$.shareData.var_setting?.modelName}/import`}",
													"file-list":"${$.shareData.var_fileListUpload}",
													"drag":true,
													"show-file-list":false,
													"accept":"${\"\"}"
												},
												"attrs":{
													"style":{
														"display":"inline-block",
														"width":"100%",
														"margin-right":"12px"
													},
													"class":"custom_upload"
												}
											}
										],
										"componentName":"DivWrapper",
										"id":"DivWrapper-J8jrEEFY",
										"title":"上传内容",
										"attrs":{
											"style":{
												"flex":"0",
												"width":"100%",
												"margin-bottom":"6px",
												"height":"126px"
											}
										},
										"props":{
											"name":"DivWrapper"
										}
									},
									{
										"children":[
											{
												"componentType":"custom",
												"children":[
													{
														"componentName":"TextWrapper",
														"id":"TextWrapper-zd7fknY4",
														"props":{
															"children":""
														},
														"attrs":{
															"style":{
																"color":"#F52F3E",
																"padding-right":"3px"
															},
															"class":"el-icon-error"
														}
													},
													{
														"componentName":"TextWrapper",
														"id":"TextWrapper-dEhinRKD",
														"props":{
															"children":"${`上传失败，本次上传错误数据${$.shareData.api_importTemplate?.extra?.errorCount}条${$.shareData.api_importTemplate?.extra?.uploadFileResult ? '，' : '。'}`}"
														},
														"attrs":{
															"style":{}
														}
													},
													{
														"children":[
															{
																"action":{
																	"fire":{
																		"click":{
																			"arguments":[],
																			"actions":[
																				{
																					"handler":"${function handler(){/*dm*/
\n                                            this.$message({ message: '正在下载，请稍等...', type: 'warning' });/*dm*/
\n                                            const a = document.createElement('a');/*dm*/
\n                                            a.href = $.shareData.api_importTemplate?.extra?.errorFilePath;/*dm*/
\n                                            document.body.appendChild(a);/*dm*/
\n                                            a.click();/*dm*/
\n                                            document.body.removeChild(a);/*dm*/
\n                                          }}",
																					"type":"function"
																				}
																			]
																		}
																	}
																},
																"componentName":"Button",
																"id":"Button-bEGr5paZ",
																"attrs":{
																	"style":{
																		"font-size":"12px"
																	}
																},
																"props":{
																	"children":"点击下载错误数据",
																	"type":"text"
																}
															}
														],
														"componentName":"VIf",
														"id":"VIf-HmZ4ycGZ",
														"props":{
															"conditions":"${[$.shareData.api_importTemplate?.extra?.uploadFileResult]}"
														}
													}
												],
												"componentName":"DivWrapper",
												"id":"DivWrapper-6ckFmwEP",
												"attrs":{
													"style":{
														"flex-direction":"row",
														"display":"flex",
														"font-size":"12px",
														"justify-content":"flex-start",
														"align-items":"center"
													}
												},
												"props":{
													"name":"DivWrapper"
												}
											}
										],
										"componentName":"VIf",
										"id":"VIf-Xj7sBjYe",
										"props":{
											"conditions":"${[$.shareData.var_fileRet === 'fail']}"
										},
										"attrs":{
											"style":{
												"flex":"1",
												"height":"54px"
											}
										}
									},
									{
										"children":[
											{
												"componentType":"custom",
												"children":[
													{
														"componentName":"TextWrapper",
														"id":"TextWrapper-x77yDX6z",
														"props":{
															"children":""
														},
														"attrs":{
															"style":{
																"color":"#0ED57D",
																"padding-right":"3px"
															},
															"class":"el-icon-success"
														}
													},
													{
														"componentName":"TextWrapper",
														"id":"TextWrapper-MysYCdj5",
														"props":{
															"children":"${`上传成功`}"
														},
														"attrs":{
															"style":{}
														}
													}
												],
												"componentName":"DivWrapper",
												"id":"DivWrapper-TxftTHpF",
												"attrs":{
													"style":{
														"flex-direction":"row",
														"display":"flex",
														"font-size":"12px",
														"justify-content":"flex-start",
														"align-items":"center"
													}
												},
												"props":{
													"name":"DivWrapper"
												}
											}
										],
										"componentName":"VIf",
										"id":"VIf-DDmTK6T7",
										"props":{
											"conditions":"${[$.shareData.var_fileRet==='allSuccess']}"
										},
										"attrs":{
											"style":{
												"flex":"1",
												"height":"54px"
											}
										}
									},
									{
										"children":[
											{
												"componentType":"custom",
												"children":[
													{
														"componentName":"TextWrapper",
														"id":"TextWrapper-TpcfJd7h",
														"props":{
															"children":""
														},
														"attrs":{
															"style":{
																"color":"#4871E4"
															},
															"class":"el-icon-loading"
														}
													},
													{
														"componentName":"TextWrapper",
														"id":"TextWrapper-aBJFWesA",
														"props":{
															"children":"${`上传中...`}"
														},
														"attrs":{
															"style":{
																"padding-left":"3px"
															}
														}
													}
												],
												"componentName":"DivWrapper",
												"id":"DivWrapper-DE5jNT47",
												"attrs":{
													"style":{
														"flex-direction":"row",
														"display":"flex",
														"font-size":"12px",
														"justify-content":"flex-start",
														"align-items":"center"
													}
												},
												"props":{
													"name":"DivWrapper"
												}
											}
										],
										"componentName":"VIf",
										"id":"VIf-x4aFcYfC",
										"props":{
											"conditions":"${[$.shareData.var_fileRet=='uploading']}"
										},
										"attrs":{
											"style":{
												"flex":"1",
												"height":"54px"
											}
										}
									},
									{
										"children":[
											{
												"componentType":"custom",
												"children":[
													{
														"componentName":"DivWrapper",
														"id":"DivWrapper-fkr446C4",
														"props":{
															"children":"错误详情"
														},
														"attrs":{
															"style":{
																"color":"#303133",
																"font-weight":"500",
																"margin-bottom":"4px"
															}
														}
													},
													{
														"componentType":"custom",
														"children":[
															{
																"children":[
																	{
																		"componentType":"custom",
																		"children":[
																			{
																				"componentName":"DivWrapper",
																				"id":"DivWrapper-FNrDdTFP",
																				"props":{
																					"children":"${`第${__item.rowIndex}条数据 ${__item.importFailedMsg}`}"
																				},
																				"attrs":{
																					"style":{
																						"line-height":"2em"
																					}
																				}
																			}
																		],
																		"componentName":"DivWrapper",
																		"id":"DivWrapper-YCxawfCb",
																		"attrs":{
																			"style":{}
																		},
																		"props":{
																			"name":"DivWrapper"
																		}
																	}
																],
																"componentName":"VFor",
																"id":"VFor52372",
																"props":{
																	"forEach":"${$.shareData.var_importErrorData.slice(0, 200)}",
																	"forEachKey":"__item",
																	"index":"$index"
																}
															},
															{
																"children":[
																	{
																		"componentName":"DivWrapper",
																		"id":"DivWrapper-eQYKTX8d",
																		"props":{
																			"children":"..."
																		},
																		"attrs":{
																			"style":{
																				"line-height":"2em"
																			}
																		}
																	}
																],
																"componentName":"VIf",
																"id":"VIf-4Axefjph",
																"props":{
																	"conditions":"${[$.shareData.var_importErrorData.length > 200]}"
																}
															}
														],
														"componentName":"DivWrapper",
														"id":"DivWrapper-Eyxcwb28",
														"attrs":{
															"style":{
																"border":"1px solid rgba(220,223,230,1)",
																"border-radius":"4px",
																"padding":"12px",
																"max-height":"180px",
																"background":"rgb(247, 248, 249)",
																"overflow-y":"scroll"
															}
														},
														"props":{
															"name":"DivWrapper"
														}
													}
												],
												"componentName":"DivWrapper",
												"id":"DivWrapper-i5AnwFjS",
												"attrs":{
													"style":{}
												},
												"props":{
													"name":"DivWrapper"
												}
											}
										],
										"componentName":"VIf",
										"id":"VIf47960",
										"props":{
											"conditions":"${[$.shareData.var_importErrorData.length > 0]}"
										}
									}
								],
								"componentName":"DivWrapper",
								"id":"DivWrapper-R4kER3wd",
								"attrs":{
									"style":{
										"flex-direction":"column",
										"display":"flex",
										"min-height":"190px"
									}
								},
								"props":{
									"name":"DivWrapper"
								}
							}
						],
						"componentName":"DivWrapper",
						"id":"DivWrapper-5bKBrjaC",
						"attrs":{
							"style":{}
						},
						"props":{
							"name":"DivWrapper"
						}
					},
					{
						"componentType":"custom",
						"children":[
							{
								"action":{
									"fire":{
										"click":{
											"arguments":[],
											"actions":[
												{
													"handler":"${function handler(params){$.shareData.var_importDialogVisibleAction(false)}}",
													"type":"function"
												}
											]
										}
									}
								},
								"componentName":"Button",
								"id":"Button-JhH2a4TY",
								"attrs":{
									"style":{}
								},
								"props":{
									"size":"small",
									"children":"关闭",
									"type":"${''}"
								}
							}
						],
						"componentName":"DivWrapper",
						"id":"DivWrapper-BJky4XrT",
						"attrs":{
							"style":{
								"display":"flex",
								"justify-content":"flex-end",
								"align-items":"center",
								"margin-bottom":"9px",
								"height":"42px"
							}
						},
						"props":{
							"name":"DivWrapper"
						}
					}
				],
				"action":{
					"fire":{
						"close":{
							"actions":[
								{
									"handler":"${function handler(){/*dm*/
\n                    $.shareData.var_importDialogVisibleAction(false);/*dm*/
\n                    $.shareData.var_fileRetAction('');/*dm*/
\n                    $.shareData.var_importErrorDataAction([]);/*dm*/
\n                  }}",
									"type":"function"
								}
							]
						}
					}
				},
				"componentName":"Dialog",
				"id":"Dialog-Qicnb4j8",
				"title":"导入",
				"props":{
					"modal-append-to-body":"${false}",
					"visible":"${$.shareData.var_importDialogVisible}",
					"width":"560px",
					"title":"导入目标",
					"close-on-press-escape":false,
					"close-on-click-modal":false
				}
			}
		],
		"functions":[
			{
				"default":"${function fun_loadData(queryData) {/*dm*/
\n  const data = queryData || $.shareData.var_searchType?.data || {}/*dm*/
\n  const modelName = $.shareData.var_setting.modelName;/*dm*/
\n  const { keywordQuery: keyword, attributes: scopeAttributes } = $.shareData.var_searchType?.searchData;/*dm*/
\n  let { keywordQuery, ...rest } = data;/*dm*/
\n  const fieldMap = $.global.shareData.modelsInfoData.fieldsMap[modelName];/*dm*/
\n  rest.queryConditions = rest?.queryConditions ? rest.queryConditions.map(item => {/*dm*/
\n    if (fieldMap.get(item.fieldName)?.businessType == 'department') {/*dm*/
\n      item.value = item.value?.organizationCode || item.value || '';/*dm*/
\n      item.filter = !!item.value;/*dm*/
\n    } else if (fieldMap.get(item.fieldName)?.businessType == 'multipleDepartment') {/*dm*/
\n      item.value = item.value?.map(v => v.organizationCode) || item.value || '';/*dm*/
\n      item.filter = !!item.value;/*dm*/
\n      if (item.values) {/*dm*/
\n        item.values = item.values?.map(v => v?.organizationCode || v) || item.values || '';/*dm*/
\n      }/*dm*/
\n    } else {/*dm*/
\n      item.filter = true/*dm*/
\n    }/*dm*/
\n    return item;/*dm*/
\n  }).filter(i => i.filter) : [];/*dm*/
\n  let params = {};/*dm*/
\n  for (let i in $.shareData.var_pageParams) {/*dm*/
\n    params[i] = $.shareData.var_pageParams[i];/*dm*/
\n  };/*dm*/
\n  await $.shareData.var_pageLoadingAction(true);/*dm*/
\n  let querySorts = $.shareData.var_querySorts/*dm*/
\n  const attributes = $.shareData.var_columnsShow.map(item => item.fieldName);/*dm*/
\n  const modelValueFormat = await $.global.functions.modelValueFormat($.shareData.var_setting.modelName)/*dm*/
\n  const caseQueryData = {/*dm*/
\n    \"keywordQuery\": {/*dm*/
\n      keyword,/*dm*/
\n      scopeAttributes/*dm*/
\n    },/*dm*/
\n    ...rest,/*dm*/
\n    attributes: attributes,/*dm*/
\n    ...$.shareData.var_tabsSearchData,/*dm*/
\n    querySorts: [/*dm*/
\n      ...querySorts,/*dm*/
\n      ...($.shareData.var_activePlanView?.viewSortList && $.shareData.var_activePlanView.viewSortList.map(item => {/*dm*/
\n        return {/*dm*/
\n          fieldName: item.fieldName,/*dm*/
\n          desc: item.sortOrder == 'desc'/*dm*/
\n        }/*dm*/
\n      }) || [])/*dm*/
\n    ],/*dm*/
\n    modelName: modelName,/*dm*/
\n  }/*dm*/
\n  $.shareData.var_searchTypeAction({/*dm*/
\n    cacheData: caseQueryData/*dm*/
\n  });/*dm*/
\n  let queryConditions = {};/*dm*/
\n  let qureydata = {/*dm*/
\n    ...caseQueryData,/*dm*/
\n    ...params/*dm*/
\n  }/*dm*/
\n  if ($.shareData.var_isQuery) {/*dm*/
\n    queryConditions = {/*dm*/
\n      fieldName: $.shareData.var_isQuery[0].fieldName,/*dm*/
\n      conditionType: $.shareData.var_isQuery[0].conditionType,/*dm*/
\n      value: $.shareData.var_isQuery[0]?.value?.id/*dm*/
\n    };/*dm*/
\n    qureydata.queryConditions.push({ ...queryConditions });/*dm*/
\n  }/*dm*/
\n/*dm*/
\n  if (qureydata.queryConditions.length) {/*dm*/
\n    qureydata.queryConditions = qureydata.queryConditions.map(item => {/*dm*/
\n/*dm*/
\n      if (typeof item.value == 'string') {/*dm*/
\n        return {/*dm*/
\n          ...item,/*dm*/
\n          value: item.value.trim()/*dm*/
\n        }/*dm*/
\n      }/*dm*/
\n/*dm*/
\n      return item;/*dm*/
\n    })/*dm*/
\n  }/*dm*/
\n/*dm*/
\n  try {/*dm*/
\n    if ($.include) {/*dm*/
\n/*dm*/
\n      qureydata.queryConditions = [...qureydata.queryConditions, ...JSON.parse($.include)]/*dm*/
\n    }/*dm*/
\n  } catch (e) { }/*dm*/
\n/*dm*/
\n/*dm*/
\n  $.shareData.api_getListAction(qureydata).then(() => {/*dm*/
\n    $.shareData.var_pageLoadingAction(false);/*dm*/
\n/*dm*/
\n    if ($.shareData.api_getList.code == 200) {/*dm*/
\n      for (let i in $.shareData.var_pageParams) {/*dm*/
\n        $.shareData.var_pageParams[i] = $.shareData.api_getList.data[i]/*dm*/
\n      }/*dm*/
\n/*dm*/
\n      let list = $.shareData.api_getList?.data?.records.map(item => {/*dm*/
\n        return modelValueFormat.input(item)/*dm*/
\n      });/*dm*/
\n      $.shareData.var_tableListAction(list)/*dm*/
\n/*dm*/
\n      // 处理查找关系弹窗回显选中/*dm*/
\n      $.functions.fun_handleFindRelation()/*dm*/
\n    }/*dm*/
\n  }).catch((e) => {/*dm*/
\n    $.shareData.var_pageLoadingAction(false);/*dm*/
\n  });/*dm*/
\n  $.functions.fun_getAdvancedFilter(true);/*dm*/
\n}}",
				"description":"初始化加载数据",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_loadData"
			},
			{
				"default":"${function draftLoadData() {/*dm*/
\n          const modelValueFormat = await $.global.functions.modelValueFormat($.shareData.var_setting.modelName)/*dm*/
\n          const modelName = $.shareData.var_setting.modelName;/*dm*/
\n          if ($.global.shareData.modelsInfoData?.draft[modelName]) {/*dm*/
\n            let draftData = {/*dm*/
\n              queryConditions: [/*dm*/
\n                {/*dm*/
\n                  fieldName: \"model_name\",/*dm*/
\n                  conditionType: \"EQ\",/*dm*/
\n                  value: modelName/*dm*/
\n                }/*dm*/
\n              ],/*dm*/
\n              pageSize: 10000/*dm*/
\n            }/*dm*/
\n            await $.shareData.api_getCommonDraftAction(draftData).then(()=>{/*dm*/
\n              if($.shareData.api_getCommonDraft?.code==200){/*dm*/
\n                let list = $.shareData.api_getCommonDraft?.data?.records.map(item => {/*dm*/
\n                  return modelValueFormat.input(item)/*dm*/
\n                });/*dm*/
\n                $.shareData.var_draftData = list;/*dm*/
\n              }/*dm*/
\n            }).catch((error)=>{/*dm*/
\n              console.log(error);/*dm*/
\n            })/*dm*/
\n            }/*dm*/
\n        }}",
				"description":"草稿箱加载",
				"type":"function",
				"title":"",
				"key":"fun_loadDraftData"
			},
			{
				"default":"${function getFieldsBusinessType(modelName, fieldName){/*dm*/
\n    const fields = $.shareData.var_columnsShow/*dm*/
\n    let businessType = 'text'/*dm*/
\n    let _sourse = null;/*dm*/
\n    const other = {/*dm*/
\n        relatedModelField: null/*dm*/
\n    }/*dm*/
\n    fields.map(item => {/*dm*/
\n        const modelFieldEntity = item.modelField;/*dm*/
\n        if (item.fieldName === fieldName) {/*dm*/
\n            _sourse = item/*dm*/
\n            if (modelFieldEntity) {/*dm*/
\n                switch (item.businessType) {/*dm*/
\n                    case 'formula': businessType = modelFieldEntity.formulaReturnType; return;/*dm*/
\n                    case 'findRelated': businessType = getRelatedModelBusinessType(modelFieldEntity); return;/*dm*/
\n                    case 'findRelatedMulti': businessType = getRelatedModelBusinessType(modelFieldEntity); return;/*dm*/
\n                    default: businessType = item.businessType;/*dm*/
\n                }/*dm*/
\n            } else {/*dm*/
\n                businessType = item.businessType;/*dm*/
\n                _sourse = item/*dm*/
\n            }/*dm*/
\n           /*dm*/
\n        }/*dm*/
\n    })/*dm*/
\n/*dm*/
\n    function getRelatedModelBusinessType(data) {/*dm*/
\n        let relatedModelField = data.relatedModel?.modelFields?.find(item => item.fieldName == data.calculationFieldName)/*dm*/
\n        other.relatedModelField = relatedModelField;/*dm*/
\n        return relatedModelField?.businessType || businessType/*dm*/
\n    }/*dm*/
\n    if (!businessType || !_sourse) {/*dm*/
\n        return {};/*dm*/
\n    }/*dm*/
\n    const minWidth = _sourse.fieldWidth || $.shareData.var_tableBusinessTypeWidth[businessType] || 120;/*dm*/
\n       return {/*dm*/
\n        fieldName,/*dm*/
\n        minWidth,/*dm*/
\n        fieldWidth: _sourse.fieldWidth,/*dm*/
\n        businessType,/*dm*/
\n        modelFieldEntity: _sourse.modelFieldEntity,/*dm*/
\n        _sourse,/*dm*/
\n        other/*dm*/
\n    }/*dm*/
\n}}",
				"description":"",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_getFieldsBusinessType"
			},
			{
				"default":"${function tablePlanLoadData() {/*dm*/
\n/*dm*/
\n            let { columnPlanId } = $.shareData.var_tableSetting/*dm*/
\n/*dm*/
\n            const modelName = $.shareData.var_setting?.modelName/*dm*/
\n            if (columnPlanId) {/*dm*/
\n/*dm*/
\n              if ($.shareData.api_getConditionsPlan?.data?.yeqian?.length > 0) {/*dm*/
\n                if (this.$refs['record-table']?.currentPlan?.system) {/*dm*/
\n                  this.$refs['record-table'].currentPlan = {}/*dm*/
\n                }/*dm*/
\n                let activeTags = $.shareData.api_getConditionsPlan?.data?.yeqian[0]/*dm*/
\n                if ($.shareData.var_searchRuleValue) {/*dm*/
\n                  activeTags = $.shareData.api_getConditionsPlan?.data?.yeqian.find(item => item.planCode === $.shareData.var_searchRuleValue)/*dm*/
\n                }/*dm*/
\n                if (activeTags?.planId) {/*dm*/
\n                  columnPlanId = activeTags.planId/*dm*/
\n                }/*dm*/
\n              }/*dm*/
\n              await $.shareData.api_tablePlanListAction({ modelName, planId: columnPlanId })/*dm*/
\n/*dm*/
\n              const list = $.shareData.api_tablePlanList?.filter(i => i.isDefault) || []/*dm*/
\n/*dm*/
\n              if (list.length > 0) {/*dm*/
\n/*dm*/
\n                const { system, id } = list[0]/*dm*/
\n/*dm*/
\n                await $.shareData.api_getTablePlanInfoAction({ modelName, planId: id, system })/*dm*/
\n/*dm*/
\n                const { planInfoDtoList,freeze, ...otherInfo } = $.shareData.api_getTablePlanInfo/*dm*/
\n/*dm*/
\n                $.shareData.var_tablePlanFieldsAction([...planInfoDtoList]);/*dm*/
\n/*dm*/
\n                let freezeNew = {action:true,head:true}/*dm*/
\n                if(freeze){/*dm*/
\n                  freezeNew = freeze/*dm*/
\n                }/*dm*/
\n                $.shareData.var_tablePlanOtherInfoAction({freeze:freezeNew, ...otherInfo })/*dm*/
\n/*dm*/
\n                const listInner = [...planInfoDtoList]/*dm*/
\n/*dm*/
\n                $.functions.fun_filterTableColumn(listInner)/*dm*/
\n/*dm*/
\n              }/*dm*/
\n/*dm*/
\n            }/*dm*/
\n}}",
				"description":"列表视图",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_loadTablePlanData"
			},
			{
				"default":"${function fun_genTableId(){/*dm*/
\n  const userData = $.bom.getHostData('userInfo')/*dm*/
\n  const genId = `${$.shareData.api_getTablePlanInfo.id}_${userData.personId || userData.userName || userData.pin}_${$.shareData.var_tableColumnWidth.id}`/*dm*/
\n  return genId/*dm*/
\n}}",
				"description":"获取表格唯一id",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_genTableId"
			},
			{
				"default":"${function columnFilter(cloumns) {/*dm*/
\n  const modelName = $.shareData.var_setting.modelName;/*dm*/
\n  const listInner = cloumns.reduce((prev, cur) => {/*dm*/
\n    const allFieldList = []/*dm*/
\n    const field = $.global.shareData.modelsInfoData.fieldsMap[modelName].get(cur.fieldName);/*dm*/
\n    if (cur.isEnable && (cur.relatedFieldList || []).filter(item => item.isEnable).length == 0) {/*dm*/
\n      // 先添加自己/*dm*/
\n      allFieldList.push({/*dm*/
\n        ...cur,/*dm*/
\n        fieldWidth: cur.displayWidth,/*dm*/
\n        fieldText: field.fieldText,/*dm*/
\n        label: field.fieldText,/*dm*/
\n        businessType: field.businessType,/*dm*/
\n        modelField: field,/*dm*/
\n        index: cur.showIndex,/*dm*/
\n      })/*dm*/
\n    }/*dm*/
\n/*dm*/
\n    // 处理查找关系逻辑/*dm*/
\n    if (cur.relatedFieldList?.length > 0) {/*dm*/
\n      cur.relatedFieldList.map(item => {/*dm*/
\n        const relatedField = field.relatedModel.modelFields.find(it1 => it1.fieldName === item.relatedFieldName)/*dm*/
\n/*dm*/
\n        // 再添加循环的数据/*dm*/
\n        if (item.isEnable) {/*dm*/
\n          allFieldList.push({/*dm*/
\n            ...item,/*dm*/
\n            name: item.fieldName,/*dm*/
\n            fieldWidth: item.displayWidth,/*dm*/
\n            fieldName: `${item.fieldName}.${item.relatedFieldName}`,/*dm*/
\n            fieldText: relatedField.fieldText,/*dm*/
\n            label: relatedField.fieldText,/*dm*/
\n            businessType: field.businessType,/*dm*/
\n            index: item.showIndex,/*dm*/
\n            modelField: {/*dm*/
\n              ...field,/*dm*/
\n              calculationFieldName: item.relatedFieldName/*dm*/
\n            }/*dm*/
\n          })/*dm*/
\n/*dm*/
\n        }/*dm*/
\n/*dm*/
\n      })/*dm*/
\n    }/*dm*/
\n    return [...prev, ...allFieldList]/*dm*/
\n  }, [])/*dm*/
\n  listInner.sort((i, j) => {/*dm*/
\n/*dm*/
\n    if (i.index > j.index) {/*dm*/
\n/*dm*/
\n      return 1;/*dm*/
\n/*dm*/
\n    } else if (i.index < j.index) {/*dm*/
\n/*dm*/
\n      return -1;/*dm*/
\n/*dm*/
\n    } else if (i.index == j.index) {/*dm*/
\n/*dm*/
\n      return 0;/*dm*/
\n/*dm*/
\n    }/*dm*/
\n  });/*dm*/
\n/*dm*/
\n  let str = localStorage.getItem($.functions.fun_genTableId())/*dm*/
\n  let widths = str ? JSON.parse(str) : {};/*dm*/
\n  $.shareData.var_tableColumnWidth.widths = widths/*dm*/
\n  const attributes = listInner.reduce((prev, cur) => [...prev, cur.name], [])/*dm*/
\n/*dm*/
\n  // $.shareData.var_searchAttributesAction(attributes)/*dm*/
\n/*dm*/
\n  $.shareData.var_tablePlanFieldsAction(listInner);/*dm*/
\n/*dm*/
\n  $.shareData.var_columnsShowAction(listInner);/*dm*/
\n}}",
				"description":"table列的过滤",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_filterTableColumn"
			},
			{
				"default":"${function queryConditionList() {/*dm*/
\n          const modelName = $.shareData.var_setting?.modelName/*dm*/
\n          await $.shareData.api_getConditionsPlanAction({ modelName: modelName });/*dm*/
\n        }}",
				"description":"获取查询条件方案列表",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_loadConditionPlanList"
			},
			{
				"default":"${function intSeach() {/*dm*/
\n          const modelName = $.shareData.var_setting?.modelName/*dm*/
\n          let map = $.global.shareData.modelsInfoData.fieldsMap[modelName];/*dm*/
\n/*dm*/
\n          let activeTag = $.shareData.var_activePlanView/*dm*/
\n          activeTag.conditionsJson = activeTag.conditionsJson || '[{\"standardQueryConditions\":[],\"standardCombinationRule\":\"\"}]'/*dm*/
\n          // 处理部门/*dm*/
\n          activeTag.conditionsValue.forEach(item => {/*dm*/
\n            if (map.get(item.fieldName)?.businessType == 'department' || map.get(item.fieldName)?.businessType == 'multipleDepartment') {/*dm*/
\n              // 表达式或默认值 内置返回 string/*dm*/
\n              if (typeof item.value == 'string') {/*dm*/
\n/*dm*/
\n              } else {/*dm*/
\n                if (item.conditionType == \"EQ\") {/*dm*/
\n                  item.value = { ...item.value, ...item?.selectObj} || {}/*dm*/
\n                }/*dm*/
\n              }/*dm*/
\n            }/*dm*/
\n          })/*dm*/
\n          if (activeTag.conditionsJson) {/*dm*/
\n            $.shareData.var_currentPlanAction({ ...activeTag });/*dm*/
\n            $.shareData.var_currentPlanValueAction(activeTag.planCode);/*dm*/
\n            let qvancedQuery = JSON.parse(activeTag.conditionsJson)[0]/*dm*/
\n            qvancedQuery.standardQueryConditions = $.functions.fun_formatQueryConditions(qvancedQuery.standardQueryConditions, map)/*dm*/
\n            let standardQueryConditions = qvancedQuery.standardQueryConditions.filter(item => item.value !== \"\" && item.value !== null && item.value !== undefined && item.value?.length !== 0);/*dm*/
\n            $.functions.fun_loadData({/*dm*/
\n              queryConditions: standardQueryConditions,/*dm*/
\n              combinationRule: qvancedQuery.standardCombinationRule/*dm*/
\n            })/*dm*/
\n            $.shareData.var_searchTypeAction({/*dm*/
\n              type: 'advanced', data: {/*dm*/
\n                queryConditions: standardQueryConditions,/*dm*/
\n                combinationRule: qvancedQuery.standardCombinationRule/*dm*/
\n              }/*dm*/
\n            })/*dm*/
\n          }/*dm*/
\n      }}",
				"description":"初始查询函数",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_intSeach"
			},
			{
				"default":"${function fun_loadCache(modelTableKey) {/*dm*/
\n          let pageParams = localStorage.getItem('pageParams_' + modelTableKey)/*dm*/
\n          if(pageParams) {/*dm*/
\n            pageParams = JSON.parse(pageParams);/*dm*/
\n            await $.shareData.var_pageParamsAction(pageParams);/*dm*/
\n          }/*dm*/
\n        }}",
				"description":"初始化缓存数据",
				"type":"function",
				"title":"",
				"key":"fun_loadPageParamsCache"
			},
			{
				"default":"${/*dm*/
\n          function queryConditionsFormat(queryConditions = [], modelFieldMaps){/*dm*/
\n            function format(fieldName, value, modelEntry) {/*dm*/
\n              if(!value) {/*dm*/
\n                return value;/*dm*/
\n              }/*dm*/
\n              let fieldNames = fieldName.split('.');/*dm*/
\n              const businessType = modelEntry.businessType;/*dm*/
\n              let newValue = value;/*dm*/
\n              // 普通字段/*dm*/
\n              if (fieldNames.length === 1) {/*dm*/
\n                switch (businessType) {/*dm*/
\n                  case 'user':/*dm*/
\n                    if (typeof value !== 'string' && value[0]) {/*dm*/
\n                      newValue = value[0].erp || value[0];/*dm*/
\n                    }/*dm*/
\n                    break;/*dm*/
\n                  case 'department':/*dm*/
\n                    if (value?.organizationCode) {/*dm*/
\n                      newValue = value?.organizationCode || '';/*dm*/
\n                    }/*dm*/
\n                    break;/*dm*/
\n                  case 'multipleDepartment':/*dm*/
\n                    if (value?.length) {/*dm*/
\n                      newValue = value?.map(v => v.organizationCode) || [];/*dm*/
\n                    }/*dm*/
\n                    break;/*dm*/
\n                  case 'multipleUser':/*dm*/
\n                    newValue = Array.isArray(value)/*dm*/
\n                      ? value?.map((v) => {/*dm*/
\n                        return v.erp;/*dm*/
\n                      })/*dm*/
\n                      : [value];/*dm*/
\n                    break;/*dm*/
\n                  case 'findRelated':/*dm*/
\n                    newValue = value?.id || value;/*dm*/
\n                    break;/*dm*/
\n                  case 'findRelatedMulti':/*dm*/
\n                    newValue = value.map((v) => {/*dm*/
\n                      return v.id;/*dm*/
\n                    });/*dm*/
\n                    break;/*dm*/
\n                  default:/*dm*/
\n                    null;/*dm*/
\n                }/*dm*/
\n                return newValue;/*dm*/
\n              } else {/*dm*/
\n                const field = modelEntry.relatedModel.modelFields.find(/*dm*/
\n                  (item) => item.fieldName === fieldNames[1]/*dm*/
\n                );/*dm*/
\n                return format(fieldNames[1], value, field);/*dm*/
\n              }/*dm*/
\n            }/*dm*/
\n            return queryConditions.map(item => {/*dm*/
\n              let fieldName = item.fieldName.split('.');/*dm*/
\n              const value = format(item.fieldName, item.value, modelFieldMaps.get(fieldName[0]));/*dm*/
\n              if (Array.isArray(value)) {/*dm*/
\n                return {/*dm*/
\n                  ...item,/*dm*/
\n                  values: value,/*dm*/
\n                };/*dm*/
\n              } else {/*dm*/
\n                return {/*dm*/
\n                  ...item,/*dm*/
\n                  value,/*dm*/
\n                };/*dm*/
\n              }/*dm*/
\n            })/*dm*/
\n          }/*dm*/
\n        }",
				"description":"",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_formatQueryConditions"
			},
			{
				"default":"${function getAdvancedFilter(isShow){/*dm*/
\n          if (isShow) {/*dm*/
\n            let index = $.shareData.var_searchType.data.queryConditions?.length;/*dm*/
\n            let num = null;/*dm*/
\n            while (index--) {/*dm*/
\n              if (($.shareData.var_searchType.data.queryConditions[index].value || $.shareData.var_searchType.data.queryConditions[index].value.length > 0) && /*dm*/
\n                $.shareData.var_searchType.data.queryConditions[index]?.isShow == undefined ? true : $.shareData.var_searchType.data.queryConditions[index]?.isShow) {/*dm*/
\n                num = num + 1/*dm*/
\n              }/*dm*/
\n            };/*dm*/
\n            $.shareData.var_badgeNumberAction(num);/*dm*/
\n            return num;/*dm*/
\n          } else {/*dm*/
\n            if ($.shareData.var_currentPlan.conditionsValue?.length > 0) {/*dm*/
\n              if ($.shareData.var_currentPlan.conditionsValue.some(item => item?.isShow == undefined ? true : item?.isShow)) {/*dm*/
\n                return false;/*dm*/
\n              } else {/*dm*/
\n                return true;/*dm*/
\n              }/*dm*/
\n            }/*dm*/
\n          }/*dm*/
\n        }}",
				"description":"高级筛选是否启用有多少个",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_getAdvancedFilter"
			},
			{
				"default":"${function getJumpUrl(type, query, modelName, fieldName) {/*dm*/
\n  const setting = $.functions.fun_getTableConfig()/*dm*/
\n  let path;/*dm*/
\n  switch (type) {/*dm*/
\n    case 'add': path = setting?.addUrl || $.shareData.var_setting.formPath;/*dm*/
\n      break;/*dm*/
\n    case 'edit': path = setting?.editUrl || $.shareData.var_setting.formPath;/*dm*/
\n      break;/*dm*/
\n    case 'copy': path = setting?.editUrl || $.shareData.var_setting.formPath;/*dm*/
\n      break;/*dm*/
\n    case 'detail': path = setting?.detailUrl || $.shareData.var_setting.detailPath;/*dm*/
\n      break;/*dm*/
\n    case 'nameField': path = setting?.fieldInfo?.name?.url || setting?.detailUrl || $.shareData.var_setting.detailPath;/*dm*/
\n      break;/*dm*/
\n    case 'fieldName': path = setting?.fieldInfo?.[fieldName]?.url || `/${modelName}/detail`/*dm*/
\n      break;/*dm*/
\n  }/*dm*/
\n  if (query && Object.keys(query).length > 0) {/*dm*/
\n    let arr = Object.keys(query);/*dm*/
\n    let queryStr = arr.map((item, index) => {/*dm*/
\n      return item + '=' + query[item]/*dm*/
\n    }).join('&');/*dm*/
\n    path += '?' + queryStr/*dm*/
\n  }/*dm*/
\n  return path/*dm*/
\n}/*dm*/
\n}",
				"description":"获取页面pageKey",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_getJumpUrl"
			},
			{
				"default":"${function fun_verifRole(code) {/*dm*/
\n    // if (!(code in $.shareData.var_pageRole)) {/*dm*/
\n    //     return true;/*dm*/
\n    // }/*dm*/
\n    return $.shareData.var_pageRole[code]/*dm*/
\n}}",
				"description":"高级筛选是否启用有多少个",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_verifRole"
			},
			{
				"default":"${function fun_initRecordView(activePlanCode){/*dm*/
\n    let list = await $.shareData.api_getRecordViewAction({/*dm*/
\n      modelName: $.shareData.var_setting.modelName,/*dm*/
\n      pageKey: $.shareData.var_setting.pageKey/*dm*/
\n    })  /*dm*/
\n/*dm*/
\n    list = [...list.filter(item => item.system).sort((a, b) => a.showIndex - b.showIndex), ...list.filter(item=> !item.system)].map(item => {/*dm*/
\n      return {/*dm*/
\n        ...item,/*dm*/
\n        systemPlan: item.system,/*dm*/
\n        id: item.editorViewCode/*dm*/
\n      }/*dm*/
\n    })/*dm*/
\n/*dm*/
\n    await $.shareData.var_pageViewListAction(list)/*dm*/
\n/*dm*/
\n    if (activePlanCode) {/*dm*/
\n       $.functions.fun_setActiveView(list.find(item => item.editorViewCode === activePlanCode))/*dm*/
\n    } else {/*dm*/
\n      const editorViewCode = $.bom.dev?.get?.('activePlan');/*dm*/
\n      if (editorViewCode) {/*dm*/
\n        let activePlan = list.find(item => item.editorViewCode === editorViewCode)/*dm*/
\n        if (activePlan) {/*dm*/
\n          $.functions.fun_setActiveView(activePlan)/*dm*/
\n          return;/*dm*/
\n        }/*dm*/
\n      }/*dm*/
\n      $.functions.fun_setActiveView(list.find(item => item.defaultState))/*dm*/
\n    }/*dm*/
\n}}",
				"description":"",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_initRecordView"
			},
			{
				"default":"${function fun_setActiveView(viewData){/*dm*/
\n  const modelName = $.shareData.var_setting.modelName;/*dm*/
\n/*dm*/
\n  // 查询/*dm*/
\n  const conditionsValue = viewData.viewPlanInfoList.sort((a, b) => a.showIndex - b.showIndex).map(item => {/*dm*/
\n    return JSON.parse(item.conditionsJson)/*dm*/
\n  })/*dm*/
\n/*dm*/
\n  // {/*dm*/
\n  //   \"name\": \"chaozhaoee\",/*dm*/
\n  //   \"label\": \"所属城市\",/*dm*/
\n  //      \"show\": true,/*dm*/
\n  //      \"index\": 16,/*dm*/
\n  //      \"modelFieldEntity\": { },/*dm*/
\n  //      \"fieldWidth\": null,/*dm*/
\n  //      \"union\": true,/*dm*/
\n  //      \"relatedDtoList\": []/*dm*/
\n  // }/*dm*/
\n/*dm*/
\n  // 列表需要的数据/*dm*/
\n  const planInfoDtoList = viewData.viewInfoList.map(item => {/*dm*/
\n    const field = $.global.shareData.modelsInfoData.fieldsMap[modelName].get(item.fieldName);/*dm*/
\n    const newItem = {/*dm*/
\n      \"name\": item.fieldName,/*dm*/
\n      \"label\": field.fieldText,/*dm*/
\n      \"show\": item.isEnable && (item.relatedFieldList || []).filter(item => item.isEnable).length == 0,/*dm*/
\n      \"index\": item.showIndex,/*dm*/
\n      \"fieldWidth\": item.displayWidth,/*dm*/
\n      \"union\": item.union,/*dm*/
\n      \"relatedDtoList\": item.relatedFieldList?.map(item => {/*dm*/
\n        let childField = field.relatedModel.modelFields.find(item1 => item.relatedFieldName == item1.fieldName)/*dm*/
\n/*dm*/
\n        return {/*dm*/
\n          ...item,/*dm*/
\n          \"name\": item.relatedFieldName,/*dm*/
\n          \"label\": childField.fieldText,/*dm*/
\n          \"show\": item.isEnable,/*dm*/
\n          \"index\": item.showIndex,/*dm*/
\n          \"fieldWidth\": item.displayWidth,/*dm*/
\n        }/*dm*/
\n      }) || []/*dm*/
\n    }/*dm*/
\n    return newItem;/*dm*/
\n  })/*dm*/
\n/*dm*/
\n/*dm*/
\n  /*dm*/
\n  const newViewData = {/*dm*/
\n    ...viewData,/*dm*/
\n    isDefault: viewData.defaultState,/*dm*/
\n/*dm*/
\n    planInfoDtoList,/*dm*/
\n/*dm*/
\n    tableWidth: viewData.displayDensity || \"small\",/*dm*/
\n/*dm*/
\n    freeze: {/*dm*/
\n      head: viewData.freezeFirstColumn,/*dm*/
\n      action: viewData.freezeOperateColumn,/*dm*/
\n    },/*dm*/
\n/*dm*/
\n    displayDensity: viewData.displayDensity || \"small\",/*dm*/
\n/*dm*/
\n    // 运行时数据/*dm*/
\n    conditionsJson: JSON.stringify([/*dm*/
\n      {/*dm*/
\n        standardQueryConditions: conditionsValue,/*dm*/
\n        standardCombinationRule: viewData.combinationRule/*dm*/
\n      }/*dm*/
\n    ]),/*dm*/
\n/*dm*/
\n    conditionsValue: conditionsValue,/*dm*/
\n  }/*dm*/
\n/*dm*/
\n  $.shareData.var_activePlanView = {};/*dm*/
\n  $.shareData.var_activePlanViewAction(newViewData);/*dm*/
\n/*dm*/
\n  $.functions.fun_filterTableColumn(newViewData.viewInfoList)/*dm*/
\n/*dm*/
\n  $.bom.dev?.set?.('activePlan', newViewData.editorViewCode)/*dm*/
\n/*dm*/
\n  // $.bom.page.replaceQuery({/*dm*/
\n  //   \"activePlan\": newViewData.editorViewCode/*dm*/
\n  // })/*dm*/
\n/*dm*/
\n}}",
				"description":"设置获取焦点视图",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_setActiveView"
			},
			{
				"default":"${function fun_savePlanView(view, isEdit = false) {/*dm*/
\n  const newData = {/*dm*/
\n    modelName: $.shareData.var_setting.modelName,/*dm*/
\n    pageKey: $.shareData.var_setting.pageKey,/*dm*/
\n    ...$.shareData.var_activePlanView,/*dm*/
\n    ...view/*dm*/
\n  }/*dm*/
\n/*dm*/
\n  // 有id修改 没id新建/*dm*/
\n  if (isEdit) {/*dm*/
\n    await $.shareData.api_setViewBodyAction({/*dm*/
\n      ...$.shareData.var_activePlanView,/*dm*/
\n      ...newData,/*dm*/
\n      isSystem: false,/*dm*/
\n      system: false,/*dm*/
\n      id: undefined/*dm*/
\n    })/*dm*/
\n/*dm*/
\n    let index = $.shareData.var_pageViewList.findIndex(item => item.editorViewCode === newData.editorViewCode);/*dm*/
\n/*dm*/
\n/*dm*/
\n    this.$set($.shareData.var_pageViewList, index, {/*dm*/
\n      ...$.shareData.var_activePlanView,/*dm*/
\n      ...newData/*dm*/
\n    })/*dm*/
\n/*dm*/
\n    return $.shareData.api_setViewBody/*dm*/
\n  } else {/*dm*/
\n    const info = await $.shareData.api_savePlanViewAction({/*dm*/
\n      ...$.shareData.var_activePlanView,/*dm*/
\n      ...newData,/*dm*/
\n      system: false,/*dm*/
\n      editorViewCode: undefined,/*dm*/
\n      id: undefined/*dm*/
\n    })/*dm*/
\n/*dm*/
\n    // 设为默认/*dm*/
\n    await $.shareData.api_setViewDefaultAction({/*dm*/
\n      modelName: $.shareData.var_setting?.modelName,/*dm*/
\n      viewCode: info.data,/*dm*/
\n      pageKey: $.shareData.var_setting.pageKey/*dm*/
\n    })/*dm*/
\n/*dm*/
\n    if (info.data) {/*dm*/
\n      $.shareData.var_pageViewList = [...$.shareData.var_pageViewList.map(item => {/*dm*/
\n        return {/*dm*/
\n          ...item,/*dm*/
\n          isDefault: false,/*dm*/
\n          defaultState: false,/*dm*/
\n        }/*dm*/
\n      }), {/*dm*/
\n        ...newData,/*dm*/
\n        editorViewCode: info.data,/*dm*/
\n        isDefault: true,/*dm*/
\n        defaultState: true,/*dm*/
\n        isSystem: false,/*dm*/
\n        system: false,/*dm*/
\n        systemPlan: false,/*dm*/
\n        id: info.data,/*dm*/
\n      }]/*dm*/
\n    }/*dm*/
\n/*dm*/
\n    $.functions.fun_setActiveView($.shareData.var_pageViewList[$.shareData.var_pageViewList.length - 1])/*dm*/
\n/*dm*/
\n    return $.shareData.api_savePlanView/*dm*/
\n  }/*dm*/
\n}}",
				"description":"保存视图",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_savePlanView"
			},
			{
				"default":"${function fun_getTableConfig(){/*dm*/
\n  return $.shareData.var_setting?.table?.[$.shareData.var_activePlanView.editorViewCode] || $.shareData.var_setting?.table?.default || {}/*dm*/
\n}}",
				"description":"",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_getTableConfig"
			},
			{
				"default":"${function fun_handleFindRelation() {/*dm*/
\n  // 用于查找关系弹窗数据回显/*dm*/
\n  // 如果是查找关系单选，数据为对象；如果是多选，数据为数组/*dm*/
\n  $.shareData.var_tableKeyAction('key' + (+new Date).toString())/*dm*/
\n  if ($.shareData.var_findRelationDialog?.businessType == 'findRelated') {/*dm*/
\n    // 强刷一次key值，保证table展示正常/*dm*/
\n/*dm*/
\n    $.shareData.var_singleSelectionAction($.shareData.var_findRelationSelectedData?.id)/*dm*/
\n  } else {/*dm*/
\n    const tableRef = this.$refs.jtable;/*dm*/
\n    if (!$.shareData.var_findRelationSelectedData || !tableRef) {/*dm*/
\n      return;/*dm*/
\n    }/*dm*/
\n/*dm*/
\n    // 初始化时必须全部初始化进去/*dm*/
\n    if ($.shareData.var_isInitMultiSelected) {/*dm*/
\n      $.shareData.var_isInitMultiSelectedAction(false)/*dm*/
\n      $.shareData.var_findRelationSelectedData.forEach(item => {/*dm*/
\n        tableRef.toggleRowSelection(item, true);/*dm*/
\n      });/*dm*/
\n    }/*dm*/
\n/*dm*/
\n    $.shareData.var_findRelationSelectedData.forEach(val => {/*dm*/
\n      Array.isArray($.shareData.tableList) && $.shareData.tableList.forEach(item => {/*dm*/
\n        if (val.id == item.id) {/*dm*/
\n          let isSelected = false;/*dm*/
\n/*dm*/
\n          tableRef.selection.forEach(last => {/*dm*/
\n            if (last.id == val.id) {/*dm*/
\n              isSelected = true;/*dm*/
\n            }/*dm*/
\n          })/*dm*/
\n          !isSelected && tableRef.toggleRowSelection(item, true);/*dm*/
\n        }/*dm*/
\n      })/*dm*/
\n    })/*dm*/
\n/*dm*/
\n/*dm*/
\n/*dm*/
\n  }/*dm*/
\n/*dm*/
\n}}",
				"description":"查找关系弹窗数据处理",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_handleFindRelation"
			}
		],
		"shareData":[
			{
				"default":"[]",
				"description":"表格数据",
				"key":"var_tableList"
			},
			{
				"default":"{\"pageNum\": 1,\"pageSize\": 10,\"totalPage\": 1,\"totalSize\": 1}",
				"description":"表格分页请求参数",
				"key":"var_pageParams"
			},
			{
				"default":true,
				"description":"table加载loading",
				"key":"var_pageLoading"
			},
			{
				"default":"[]",
				"description":"表格批量删除处理",
				"key":"var_multipleSelection"
			},
			{
				"default":"''",
				"description":"单选表格模式的选中值",
				"global":false,
				"key":"var_singleSelection"
			},
			{
				"default":"{/*dm*/
\n        \"columnPlanId\": \"792010462761443330\",/*dm*/
\n        \"showPagination\":true,/*dm*/
\n        \"paginationMode\":\"complete\",/*dm*/
\n        \"paginationAlign\":\"right\",/*dm*/
\n        \"pageSizes\":[10,20,30,40,50],/*dm*/
\n        \"showHeader\": true,/*dm*/
\n        \"stripe\": false,/*dm*/
\n        \"showNo\": false,/*dm*/
\n        \"selectRowType\": \"single\",/*dm*/
\n        \"border\": false,/*dm*/
\n        \"mode\": \"normal\",/*dm*/
\n        \"batchDelFlag\": true,/*dm*/
\n        \"addFlag\": true,/*dm*/
\n        \"exportDataFlag\": false,/*dm*/
\n        \"addPage\": \"\",/*dm*/
\n        \"editPage\": \"\",/*dm*/
\n        \"detailPage\": \"\",/*dm*/
\n        \"headerEllipsis\": true,/*dm*/
\n        \"cellEllipsis\": true,/*dm*/
\n        \"textAlign\":\"left\",/*dm*/
\n        \"showDetail\": true,/*dm*/
\n        \"showDel\": true,/*dm*/
\n        \"showEdit\": true,/*dm*/
\n        \"columnShowIcon\": false,/*dm*/
\n        \"columnShowText\": true,/*dm*/
\n        \"resizeColumn\": true/*dm*/
\n      }",
				"description":"setting属性",
				"key":"var_tableSetting"
			},
			{
				"default":"[]",
				"description":"表格展示列",
				"key":"var_columnsShow"
			},
			{
				"default":"{/*dm*/
\n        \"cacheData\": {},/*dm*/
\n        \"data\":{},/*dm*/
\n        \"searchData\": {},/*dm*/
\n        \"conditionData\": {},/*dm*/
\n      }",
				"description":"查询方式",
				"key":"var_searchType"
			},
			{
				"default":"{ \"id\": \"var_tableColumnWidth91862\",\r/*dm*/
\n  \"resize\": false, \"widths\": {\r/*dm*/
\n    \r/*dm*/
\n  }\r/*dm*/
\n}",
				"key":"var_tableColumnWidth"
			},
			{
				"default":"false",
				"description":"导入弹窗状态",
				"key":"var_importDialogVisible"
			},
			{
				"default":[],
				"description":"导入文件列表",
				"key":"var_fileListUpload"
			},
			{
				"default":false,
				"description":"上传失败弹窗状态",
				"key":"var_importErrorDialogVisible"
			},
			{
				"default":[],
				"description":"上传失败数据列表",
				"key":"var_importErrorData"
			},
			{
				"default":1,
				"description":"上传失败数据列表页码",
				"key":"var_importErrorDataPage"
			},
			{
				"default":"",
				"description":"上传状态",
				"key":"var_fileRet"
			},
			{
				"default":"[\r/*dm*/
\n\r/*dm*/
\n]",
				"description":"高级搜索querySorts参数",
				"global":false,
				"key":"var_querySorts"
			},
			{
				"default":"[]",
				"description":"列表展示视图字段数据",
				"key":"var_tablePlanFields"
			},
			{
				"default":"false",
				"description":"草稿箱弹窗",
				"key":"var_draftDrawer"
			},
			{
				"default":"{/*dm*/
\n  \"tableWidth\": \"\",/*dm*/
\n  \"freeze\": {}/*dm*/
\n}",
				"description":"列表的其他属性",
				"key":"var_tablePlanOtherInfo"
			},
			{
				"default":"",
				"description":"草稿箱数据",
				"key":"var_draftData"
			},
			{
				"default":"true",
				"key":"var_switchFlag"
			},
			{
				"default":"{\r/*dm*/
\n  \"conditionType\": \"\",\r/*dm*/
\n  \"conditionsJson\": '',\r/*dm*/
\n  \"defaultPlan\": \"\",\r/*dm*/
\n  \"modelName\": \"\",\r/*dm*/
\n  \"name\": \"\",\r/*dm*/
\n  \"planCode\": \"\",\r/*dm*/
\n  \"planSort\": \"\",\r/*dm*/
\n  \"systemPlan\": \"\",\r/*dm*/
\n  \"conditionsValue\": []\r/*dm*/
\n}",
				"global":false,
				"key":"var_currentPlan"
			},
			{
				"default":"\"\"",
				"key":"var_currentPlanValue"
			},
			{
				"default":"\"\"",
				"key":"var_searchRuleValue"
			},
			{
				"default":"{\r/*dm*/
\n  \r/*dm*/
\n}",
				"key":"var_tabsSearchData"
			},
			{
				"default":"{\r/*dm*/
\n  \"showPageTab\": true,\r/*dm*/
\n  \"showPlan\": true,\r/*dm*/
\n  \"showSearch\": true,\r/*dm*/
\n  \"showFlodButton\": true,\r/*dm*/
\n  \"showKeywordSearch\": true,\r/*dm*/
\n  \"mode\": \"simple\",\r/*dm*/
\n  \"searchMethod\": \"search\"\r/*dm*/
\n}",
				"description":"过滤器组件相关配置",
				"key":"var_settingData"
			},
			{
				"default":"{\r/*dm*/
\n  \"dateTime\": 180,\r/*dm*/
\n  \"date\": 120,\r/*dm*/
\n  \"user\": 110,\r/*dm*/
\n  \"multipleUser\": 300,\r/*dm*/
\n  \"text\": 120,\r/*dm*/
\n  \"number\": 120,\r/*dm*/
\n  \"phone\": 120,\r/*dm*/
\n  \"findRelated\": 120,\r/*dm*/
\n  \"findRelatedMulti\": 120,\r/*dm*/
\n  \"email\": 120,\r/*dm*/
\n  \"checkBox\": 120,\r/*dm*/
\n  \"select\": 120,\r/*dm*/
\n  \"multipleSelect\": 120,\r/*dm*/
\n  \"url\": 120,\r/*dm*/
\n  \"autoNumber\": 120,\r/*dm*/
\n  \"formula\": 120,\r/*dm*/
\n  \"summary\": 120,\r/*dm*/
\n  \"string\": 120\r/*dm*/
\n}",
				"global":false,
				"key":"var_tableBusinessTypeWidth"
			},
			{
				"default":"{\"searchPageConfig\":{\"enableTab\":false,\"enableAdvanced\":true,\"enableKeywordSearch\":true,\"enableImport\":true,\"enableExport\":true,\"enableBatchDelete\":true,\"enableAdd\":true}}",
				"description":"页面配置信息",
				"key":"var_settingPage"
			},
			{
				"default":"{\"name\":\"792010485771395074\",\"ceshimoxing\":\"792010485771395074\"}",
				"description":"查找关系字段对应详情页面ID",
				"key":"var_findRelatedPages"
			},
			{
				"default":"0",
				"description":"",
				"key":"var_badgeNumber"
			},
			{
				"default":"false",
				"description":"样式",
				"key":"var_styleFlag"
			},
			{
				"default":"\"\"",
				"global":false,
				"key":"var_isQuery"
			},
			{
				"default":"{}",
				"description":"页面权限",
				"key":"var_pageRole"
			},
			{
				"default":"{\"pageKey\":\"944567231992119298\",\"modelName\":\"template_library\",\"enableView\":true,\"enableKeyword\":true,\"enableQuery\":true,\"enableButtons\":true,\"enableImport\":false,\"enableExport\":false,\"enableBatchDelete\":true,\"enableAdd\":true,\"table\":{\"default\":{\"showPlanView\":true,\"edit\":true,\"detail\":false,\"delete\":true,\"copy\":false}},\"detailPath\":\"/template_library/detail\",\"formPath\":\"/template_library/form\",\"listPath\":\"/template_library/list\",\"pagePath\":\"/template_library/list\"}",
				"key":"var_setting"
			},
			{
				"default":"[]",
				"description":"页面视图列表",
				"global":false,
				"key":"var_pageViewList"
			},
			{
				"default":"{/*dm*/
\n  \"conditionsValue\": [],/*dm*/
\n  \"keyWordList\": []/*dm*/
\n}",
				"description":"当前焦点的视图",
				"global":false,
				"key":"var_activePlanView"
			},
			{
				"default":"{/*dm*/
\n  \"viewCode\": \"\",/*dm*/
\n  \"modelName\": \"\",/*dm*/
\n  \"modelText\": \"\",/*dm*/
\n  \"fieldName\": \"\",/*dm*/
\n  \"visible\": false,/*dm*/
\n  \"loading\": true,/*dm*/
\n}",
				"description":"查找关系多选",
				"global":false,
				"key":"var_findRelationMultiInfo"
			},
			{
				"default":"",
				"description":"用于查找关系弹窗展示",
				"global":false,
				"key":"var_findRelationDialog"
			},
			{
				"default":"[]",
				"description":"查找关系选中数据",
				"key":"var_findRelationSelectedData"
			},
			{
				"default":"",
				"key":"var_findRelatedForm"
			},
			{
				"default":"",
				"description":"页面渲染标识用于区分通信",
				"global":false,
				"key":"var_pageTag"
			},
			{
				"default":"",
				"key":"var_tableKey"
			},
			{
				"default":"true",
				"description":"查找关系弹窗多选初始化",
				"key":"var_isInitMultiSelected"
			}
		],
		"action":{
			"lifecycle":{
				"mounted":{
					"arguments":[],
					"actions":[
						{
							"handler":"${function handler(){/*dm*/
\n$.shareData.var_pageTagAction((+new Date()).toString());/*dm*/
\nawait $.shareData.api_getPageRoleAction({ pageKey: $.shareData.var_setting.pageKey })/*dm*/
\n$.shareData.var_pageRoleAction($.shareData.api_getPageRole)/*dm*/
\n}}",
							"type":"function"
						},
						{
							"handler":"${function Fun48572(){/*dm*/
\nconst res = await $.global.functions.getModelDictionaryInfo($.shareData?.var_setting?.modelName);/*dm*/
\n/*dm*/
\n$.shareData.var_setting.modelText = res.modelText/*dm*/
\n// 流程配置按钮开关/*dm*/
\n$.shareData.var_setting.wfOpen = res.wfOpen || false;/*dm*/
\n/*dm*/
\n// 查询条件视图列表/*dm*/
\nawait $.functions.fun_loadPageParamsCache(\"ModelTable36800\")/*dm*/
\n/*dm*/
\n/*dm*/
\nawait $.functions.fun_initRecordView();/*dm*/
\n/*dm*/
\n// await $.functions.fun_loadConditionPlanList();/*dm*/
\n// 查询列表数据展示信息/*dm*/
\n// await $.functions.fun_loadTablePlanData();/*dm*/
\n// 初始高级查询函数/*dm*/
\nawait $.functions.fun_intSeach();/*dm*/
\n/*dm*/
\n/*dm*/
\nif (!$.shareData.var_styleFlag) {/*dm*/
\n  $.bom.getHostData(\"setTitle\", res.modelText + \"-列表\");/*dm*/
\n}/*dm*/
\n}}",
							"type":"function"
						}
					]
				}
			},
			"on":{
				"findRelationCallback":{
					"arguments":[
						"data",
						" pageTag"
					],
					"actions":[
						{
							"handler":"${function Handler(){// 不处理当前页面通信/*dm*/
\n/*dm*/
\nif (pageTag == $.shareData.var_pageTag) {/*dm*/
\n  return;/*dm*/
\n}/*dm*/
\n/*dm*/
\n// 过滤器的查找多选回调数据/*dm*/
\n$.shareData.var_findRelatedFormAction(data)}}",
							"type":"function"
						}
					]
				},
				"findRelation":{
					"arguments":[
						"info",
						"data",
						"pageTag"
					],
					"actions":[
						{
							"handler":"${function Handler(){// 不处理自己的通信回调/*dm*/
\nif(pageTag == $.shareData.var_pageTag) {/*dm*/
\n  return;/*dm*/
\n}/*dm*/
\n/*dm*/
\n$.shareData.var_findRelationDialogAction(info)/*dm*/
\n// 查找关系 查找多选弹窗信息数据回显/*dm*/
\n$.shareData.var_findRelationSelectedDataAction(data)/*dm*/
\n$.functions.fun_handleFindRelation()/*dm*/
\n/*dm*/
\n/*dm*/
\n}}",
							"type":"function"
						}
					]
				},
				"queryList":{
					"arguments":[
						"query",
						"id"
					],
					"actions":[
						{
							"handler":"${function Fun55599(){// $.bom.open 接受的信息/*dm*/
\nif (query) {/*dm*/
\n  $.shareData.var_isQueryAction(query);/*dm*/
\n  $.shareData.var_styleFlagAction(true);/*dm*/
\n};/*dm*/
\nif (id) {/*dm*/
\n  this.$set($.shareData.var_isQuery[0], \"value\", id);/*dm*/
\n};/*dm*/
\n$.functions.fun_intSeach();/*dm*/
\n}}",
							"type":"function"
						}
					]
				}
			}
		},
		"id":"944567231992119298",
		"styleCode":".recordsPage {/*dm*/
\n  padding: 16px 24px;/*dm*/
\n  position: relative;/*dm*/
\n  box-sizing: border-box;/*dm*/
\n  height: 100%;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.recordsPage .headerEllipsis th.el-table__cell>.cell {/*dm*/
\n  overflow: hidden;/*dm*/
\n  text-overflow: ellipsis;/*dm*/
\n  white-space: nowrap;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.recordsPage .search-box {/*dm*/
\n  margin-bottom: 8px/*dm*/
\n}/*dm*/
\n/*dm*/
\n.recordsPage .records-joy-table {/*dm*/
\n  display: flex;/*dm*/
\n  flex-direction: column;/*dm*/
\n  min-height: 1px;/*dm*/
\n  height: 100%;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.recordsPage .records-joy-table>div:last-child {/*dm*/
\n  height: 100%;/*dm*/
\n  min-height: 0px;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.el-drawer__container .el-main {/*dm*/
\n  padding: 20px !important;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.el-tooltip__popper {/*dm*/
\n  max-width: 60%/*dm*/
\n}/*dm*/
\n/*dm*/
\n.erp-box .el-popover__reference-wrapper {/*dm*/
\n  height: 24px;/*dm*/
\n  line-height: 24px;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.recordsPage .el-table .cell .el-tooltip {/*dm*/
\n  line-height: 1.2;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.recordsPage .el-table .cell {/*dm*/
\n  white-space: nowrap;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.recordsPage .joy-search-page .joy-programme .query-top-line > .el-button {/*dm*/
\n  padding-left: 10px;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.recordsPage .base-erp.has-avatar.vertical {/*dm*/
\n  display: flex;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.recordsPage .records-joy-table-body {/*dm*/
\n  height: calc(100% - 40px);/*dm*/
\n}/*dm*/
\n/*dm*/
\n.records-joy-table .joy-more-button .more-button-heard .item {/*dm*/
\n  overflow: initial;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.custom_upload > div {/*dm*/
\n  width: 100%;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.custom_upload .el-upload-dragger {/*dm*/
\n  height: 126px;/*dm*/
\n  width: 100%;/*dm*/
\n  border: 1px rgb(72, 113, 228) dashed;/*dm*/
\n  border-radius: 3px;/*dm*/
\n  display: flex;/*dm*/
\n  align-items: center;/*dm*/
\n  justify-content: center;/*dm*/
\n  flex-direction: column;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.recordsPageDialog {/*dm*/
\n  padding: 0px;/*dm*/
\n  position: relative;/*dm*/
\n  height: calc(100vh - 116px);/*dm*/
\n}/*dm*/
\n/*dm*/
\n.recordsPageDialog .headerEllipsis th.el-table__cell>.cell {/*dm*/
\n  overflow: hidden;/*dm*/
\n  text-overflow: ellipsis;/*dm*/
\n  white-space: nowrap;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.recordsPageDialog .search-box {/*dm*/
\n  margin-bottom: 8px/*dm*/
\n}/*dm*/
\n/*dm*/
\n.recordsPageDialog .records-joy-table {/*dm*/
\n  display: flex;/*dm*/
\n  flex-direction: column;/*dm*/
\n  height: 100%;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.recordsPageDialog .records-joy-table>div:last-child {/*dm*/
\n  height: 100%;/*dm*/
\n  min-height: 0px;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.el-drawer__container .el-main {/*dm*/
\n  padding: 20px !important;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.el-tooltip__popper {/*dm*/
\n  max-width: 60%/*dm*/
\n}/*dm*/
\n/*dm*/
\n.erp-box .el-popover__reference-wrapper {/*dm*/
\n  height: 24px;/*dm*/
\n  line-height: 24px;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.recordsPageDialog .el-table .cell .el-tooltip {/*dm*/
\n  line-height: 1.2;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.recordsPageDialog .joy-search-page .joy-programme .query-top-line > .el-button {/*dm*/
\n  padding-left: 10px;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.recordsPageDialog .base-erp.has-avatar.vertical {/*dm*/
\n  display: flex;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.custom_upload > div {/*dm*/
\n  width: 100%;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.custom_upload .el-upload-dragger {/*dm*/
\n  height: 126px;/*dm*/
\n  width: 100%;/*dm*/
\n  border: 1px rgb(72, 113, 228) dashed;/*dm*/
\n  border-radius: 3px;/*dm*/
\n  display: flex;/*dm*/
\n  align-items: center;/*dm*/
\n  justify-content: center;/*dm*/
\n  flex-direction: column;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.recordsPage .records-joy-table .joy-more-button .more-button-heard .more-button-heard-item.el-dropdown {/*dm*/
\n  margin-left: -12px;/*dm*/
\n}/*dm*/
\n.find-relation-radio .el-radio__label { display: none;}",
		"apiConfigs":[
			{
				"default":"{code:200,msg:'',data:{totalSize:0,records: []}}",
				"transform":"function filter_aeZFJf(data) {return JSON.parse(JSON.stringify(data));}",
				"method":"POST",
				"isShareData":true,
				"name":"api_getList",
				"description":"获取表格数据",
				"url":"/api/v1/model/:modelName/list"
			},
			{
				"default":"{code:200,msg:'',data:''}",
				"transform":"function filter_aeZFJf(data) {return data;}",
				"method":"POST",
				"isShareData":true,
				"name":"api_saveData",
				"description":"新建行数据",
				"url":"/api/v1/model/:modelName"
			},
			{
				"transform":"function filter_aeZFJf(data) {return data.data;}",
				"method":"DELETE",
				"isShareData":true,
				"name":"api_deleteDataTrees",
				"description":"删除行数据",
				"url":"/api/v1/:modelName/trees/:id"
			},
			{
				"transform":"function filter_aeZFJf(data) {return data.data;}",
				"method":"DELETE",
				"isShareData":true,
				"name":"api_deleteData",
				"description":"删除行数据",
				"url":"/api/v1/model/:modelName/:id"
			},
			{
				"transform":"function filter_aeZFJf(data) {return data.data;}",
				"method":"DELETE",
				"isShareData":true,
				"name":"api_deleteInBatch",
				"description":"批量删除数据",
				"url":"/api/v1/model/:modelName"
			},
			{
				"method":"POST",
				"isShareData":true,
				"name":"api_derivedExcel",
				"description":"批量导出",
				"url":"/api/v1/model/:modelName/export"
			},
			{
				"method":"GET",
				"isShareData":true,
				"name":"api_downloadTemplate",
				"description":"模板下载",
				"url":"/api/v1/model/:modelName/export/downloadTemplate"
			},
			{
				"method":"POST",
				"isShareData":true,
				"name":"api_importTemplate",
				"description":"模板导入",
				"url":"/api/v1/model/:modelName/import?trigger=true"
			},
			{
				"default":"{\r/*dm*/
\n  \"tableWidth\": \"small\",\r/*dm*/
\n  \"freeze\": {\r/*dm*/
\n    \"head\": true,\r/*dm*/
\n    \"action\": true\r/*dm*/
\n  }\r/*dm*/
\n}",
				"transform":"function filter_YEcMz73F(data) {/*dm*/
\n  let planInfo = data/*dm*/
\n  if (data.code == 200) {/*dm*/
\n    planInfo = data.data;/*dm*/
\n  } else {/*dm*/
\n    planInfo = data/*dm*/
\n  }/*dm*/
\n  return {/*dm*/
\n    ...planInfo,/*dm*/
\n    \"tableWidth\": planInfo.tableWidth || 'small',/*dm*/
\n    \"freeze\": planInfo.freeze || {/*dm*/
\n      \"head\": true,/*dm*/
\n      \"action\": true/*dm*/
\n    },/*dm*/
\n    planInfoDtoList: planInfo?.planInfoDtoList?.map(item => {/*dm*/
\n       return {/*dm*/
\n         ...item,/*dm*/
\n         show: item.show && item.index !== -1,/*dm*/
\n         label: item?.modelFieldEntity?.fieldText || item.label,/*dm*/
\n         relatedDtoList: item.relatedDtoList?.map(item => {/*dm*/
\n           return {/*dm*/
\n             ...item,/*dm*/
\n             name: item.relatedFieldName/*dm*/
\n           }/*dm*/
\n         })/*dm*/
\n       }/*dm*/
\n    }) || []/*dm*/
\n  }/*dm*/
\n}",
				"method":"GET",
				"isShareData":true,
				"name":"api_getTablePlanInfo",
				"description":"列表视图-查询显示视图明细",
				"global":false,
				"url":"/api/v1/modelview/planInfo/:modelName/:planId/:system"
			},
			{
				"transform":"function filter_YEcMz73F(data) {/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果/*dm*/
\n /*dm*/
\n  return data.data;/*dm*/
\n}",
				"method":"GET",
				"isShareData":true,
				"name":"api_tablePlanList",
				"description":"列表视图-查询用户自定义视图列表",
				"url":"/api/v1/modelview/plan/:modelName/:planId"
			},
			{
				"transform":"function filter_YEcMz73F(data) {/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果/*dm*/
\n  return data;/*dm*/
\n}",
				"method":"DELETE",
				"isShareData":true,
				"name":"api_deleteTablePlan",
				"description":"列表视图-删除视图",
				"url":"/api/v1/modelview/planInfoDel/:modelName/:planId"
			},
			{
				"transform":"function filter_YEcMz73F(data) {/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果/*dm*/
\n  return data;/*dm*/
\n}",
				"method":"POST",
				"isShareData":true,
				"name":"api_addTablePlan",
				"description":"列表视图-新增",
				"url":"/api/v1/modelview/planInfoAdd"
			},
			{
				"transform":"function filter_YEcMz73F(data) {/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果/*dm*/
\n  return data;/*dm*/
\n}",
				"method":"PUT",
				"isShareData":true,
				"name":"api_saveTablePlan",
				"description":"列表视图-保存",
				"url":"/api/v1/modelview/planInfoEdit"
			},
			{
				"transform":"function filter_YEcMz73F(data) {/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果/*dm*/
\n  return data;/*dm*/
\n}",
				"method":"PUT",
				"isShareData":true,
				"name":"api_renameTablePlan",
				"description":"列表视图-重命名",
				"url":"/api/v1/modelview/reName"
			},
			{
				"transform":"function filter_8Q8hz8(data) {\r/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果\r/*dm*/
\n  return data.data;\r/*dm*/
\n}",
				"method":"DELETE",
				"isShareData":true,
				"name":"api_deleteDraft",
				"description":"删除一条草稿箱记录",
				"url":"/api/v1/model/common_draft/:id"
			},
			{
				"transform":"function filter_jstRNX(data) {\r/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果\r/*dm*/
\n  return data;\r/*dm*/
\n}",
				"method":"POST",
				"isShareData":true,
				"name":"api_getPreDownload",
				"description":"预下载接口",
				"url":"/api/v1/attachment/preDownload"
			},
			{
				"transform":"",
				"method":"POST",
				"isShareData":true,
				"name":"api_getCommonDraft",
				"description":"草稿箱数据",
				"url":"/api/v1/model/common_draft/list"
			},
			{
				"default":"{\r/*dm*/
\n  \"data\": {\r/*dm*/
\n    \"yeqian\": [],\r/*dm*/
\n    \"yuzhi\": []\r/*dm*/
\n  }\r/*dm*/
\n}",
				"transform":"function filter_KjHTHJyB(data) {\r/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果\r/*dm*/
\n  data.data.yeqian = (data.data.yeqian || []).map(item => {\r/*dm*/
\n    return {\r/*dm*/
\n      ...item,\r/*dm*/
\n      planName: item.name\r/*dm*/
\n    }\r/*dm*/
\n  }).sort((a,b) => a.planSort - b.planSort)\r/*dm*/
\n\r/*dm*/
\n  data.data.yuzhi = (data.data.yuzhi || []).map(item => {\r/*dm*/
\n    const conditionsValue = JSON.parse(item.conditionsJson)[0].standardQueryConditions\r/*dm*/
\n    return {\r/*dm*/
\n      ...item,\r/*dm*/
\n      conditionsValue\r/*dm*/
\n    }\r/*dm*/
\n  })\r/*dm*/
\n  return data\r/*dm*/
\n}",
				"method":"GET",
				"isShareData":true,
				"name":"api_getConditionsPlan",
				"description":"查询条件方案+页签-列表",
				"global":false,
				"url":"/api/v1/model/conditions/planAll/:modelName"
			},
			{
				"transform":"",
				"method":"POST",
				"isShareData":true,
				"name":"api_saveConditionsPlan",
				"description":"查询条件方案-保存或修改",
				"url":"/api/v1/model/conditions/plan/save"
			},
			{
				"transform":"",
				"method":"PUT",
				"isShareData":true,
				"name":"api_setConditionsPlanDefault",
				"description":"查询条件方案-设置默认",
				"url":"/api/v1/model/conditions/plan/default/:modelName/:planCode"
			},
			{
				"transform":"",
				"method":"DELETE",
				"isShareData":true,
				"name":"api_deleteConditionsPlan",
				"description":"查询条件方案-删除方案",
				"url":"/api/v1/model/conditions/plan/:modelName/:planCode"
			},
			{
				"transform":"",
				"method":"PUT",
				"isShareData":true,
				"name":"api_setTablePlanDefault",
				"description":"设为默认",
				"url":"/api/v1/modelview/setDefault/:modelName/:planId/:isSystem"
			},
			{
				"transform":"",
				"method":"PUT",
				"isShareData":true,
				"name":"api_editTree",
				"description":"修改树节点",
				"global":false,
				"url":"/api/v1/:modelName/trees/:id"
			},
			{
				"transform":"function filter_8YYSanTF(data) {/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果/*dm*/
\n return data.data.reduce((pre, cur) => {/*dm*/
\n    return {/*dm*/
\n  ...pre,    [cur.elementCode]: cur.permission === 1/*dm*/
\n    }/*dm*/
\n  }, {});/*dm*/
\n}",
				"method":"GET",
				"isShareData":true,
				"name":"api_getPageRole",
				"description":"获取页面元素权限",
				"global":false,
				"url":"/api/v1/permission/page/:pageKey/element"
			},
			{
				"transform":"function filter_ChzM6ZFd(data) {/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果/*dm*/
\n  return data.data;/*dm*/
\n}",
				"method":"GET",
				"isShareData":true,
				"name":"api_getRecordView",
				"description":"获取列表页视图",
				"global":false,
				"url":"/api/v2/views/:modelName/:pageKey/list_view"
			},
			{
				"transform":"function filter_pP7JxBbe(data) {/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果/*dm*/
\n  return data;/*dm*/
\n}",
				"method":"POST",
				"isShareData":true,
				"name":"api_savePlanView",
				"description":"保存当前视图",
				"global":false,
				"url":"/api/v2/views/:modelName/:pageKey/list_view"
			},
			{
				"transform":"function filter_pP7JxBbe(data) {/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果/*dm*/
\n  return data;/*dm*/
\n}",
				"method":"POST",
				"isShareData":true,
				"name":"api_setViewDefault",
				"description":"设置视图默认",
				"global":false,
				"url":"/api/v2/views/:modelName/:pageKey/list_view/:viewCode/_default"
			},
			{
				"transform":"",
				"method":"DELETE",
				"isShareData":true,
				"name":"api_deleteView",
				"description":"删除视图",
				"url":"/api/v2/views/:modelName/:pageKey/list_view/:viewCode"
			},
			{
				"transform":"",
				"method":"PUT",
				"isShareData":true,
				"name":"api_reViewName",
				"description":"重命名视图",
				"url":"/api/v2/views/:modelName/:pageKey/list_view/:editorViewCode/_rename"
			},
			{
				"transform":"function filter_jzTeDy3i(data) {/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果/*dm*/
\n  return data;/*dm*/
\n}",
				"method":"PATCH",
				"isShareData":true,
				"name":"api_setViewBody",
				"description":"修改视图内容",
				"global":false,
				"url":"/api/v2/views/:modelName/:pageKey/list_view/:editorViewCode"
			}
		],
		"attrs":{
			"style":{
				"position":"relative",
				"height":"100%"
			},
			"setting":"{\"name\": \"page\"}"
		}
	},
	"editorSettingDsl":"{\"detailPath\":\"/template_library/detail\",\"formPath\":\"/template_library/form\",\"listPath\":\"/template_library/list\",\"modelName\":\"template_library\",\"pageKey\":\"944567231992119298\",\"pagePath\":\"/template_library/list\",\"enableView\":true,\"enableKeyword\":true,\"enableQuery\":true,\"enableButtons\":true,\"enableImport\":false,\"enableExport\":false,\"enableBatchDelete\":true,\"enableAdd\":true,\"table\":{\"default\":{}}}",
	"pageInterceptor":"",
	"pageSource":"model",
	"frame":"vue"
}