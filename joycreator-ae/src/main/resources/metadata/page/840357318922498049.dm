{
	"usePlatform":"",
	"enableAdvanced":true,
	"componentCode":"ipms@1.0.0",
	"editable":false,
	"pageTemplate":"PC树模版",
	"edition":"v2",
	"pageKey":"840357318922498049",
	"pageSettingDsl":"{\"detailPath\":\"/ipms_office_organization/detail1\",\"formPath\":\"/ipms_office_organization/form1\",\"listPath\":\"/ipms_office_organization/list1\",\"modelName\":\"ipms_office_organization\",\"pageKey\":\"840357318922498049\",\"pagePath\":\"/ipms_office_organization/list1\"}",
	"pageName":"行政组织_树列表",
	"pageTemplateCode":"PCTree",
	"modelName":"ipms_office_organization",
	"pageType":"records_v5",
	"pageDsl":{
		"components":[
			{
				"componentType":"custom",
				"children":[
					{
						"componentType":"custom",
						"children":[
							{
								"componentType":"custom",
								"children":[
									{
										"componentName":"TextWrapper",
										"id":"TextWrapper39993",
										"props":{
											"children":"${$.shareData.var_setting.modelText}"
										},
										"attrs":{
											"style":{
												"color":"rgba(48,49,51,1)",
												"font-weight":"500",
												"font-size":"14px"
											}
										}
									},
									{
										"children":[
											{
												"action":{
													"fire":{
														"click":{
															"arguments":[],
															"actions":[
																{
																	"handler":"${function handler(){/*dm*/
\n$.bom.page.open($.shareData.var_setting.formPath, {/*dm*/
\n  defaultVarSaveForm: $.shareData.var_linkFormInfo/*dm*/
\n}, (data) => {/*dm*/
\n  if (data) {/*dm*/
\n    if (data.flag) {/*dm*/
\n      $.functions.fun_getTreeList(data.data);/*dm*/
\n    };/*dm*/
\n  }/*dm*/
\n}, {/*dm*/
\n  type: 'dialog',/*dm*/
\n  options: {/*dm*/
\n    \"title\": \"树模型\" + '-' + \"新建\",/*dm*/
\n    \"modal-append-to-body\": true,/*dm*/
\n    \"width\": \"648px\",/*dm*/
\n    \"close-on-press-escape\": true,/*dm*/
\n    \"close-on-click-modal\": false,/*dm*/
\n    \"class\": \"includeDialog\"/*dm*/
\n  },/*dm*/
\n});/*dm*/
\n}}",
																	"type":"function"
																}
															]
														}
													}
												},
												"componentName":"Button",
												"id":"Button9610",
												"attrs":{
													"style":{
														"height":"32px"
													}
												},
												"props":{
													"size":"small",
													"children":"新建",
													"icon":"joyIconFont joy-icon-add",
													"type":"primary"
												}
											}
										],
										"componentName":"VIf",
										"id":"VIf20438",
										"props":{
											"conditions":"${[$.functions.fun_verifRole(\"add\") && $.shareData.var_setting.enableAddRootNode]}"
										}
									}
								],
								"componentName":"DivWrapper",
								"id":"TextWrapper190",
								"title":"TreeBoxLeftTop",
								"attrs":{
									"style":{
										"padding-top":"0px",
										"display":"flex",
										"padding-left":"16px",
										"padding-bottom":"0px",
										"box-sizing":"border-box",
										"margin-right":"0px",
										"margin-left":"0px",
										"width":"100%",
										"justify-content":"space-between",
										"margin-top":"16px",
										"align-items":"center",
										"margin-bottom":"16px",
										"height":"40px",
										"padding-right":"16px"
									}
								}
							},
							{
								"children":[
									{
										"libraryName":"@jd/joyui",
										"componentType":"UILibrary",
										"children":[
											{
												"componentType":"custom",
												"children":[
													{
														"libraryName":"@jd/joyui",
														"componentType":"UILibrary",
														"componentName":"TooltipText",
														"id":"TooltipText-R72zMyhH",
														"version":"1.9.32",
														"props":{
															"openDelay":1000,
															"contentText":"${node.label}",
															"key":"${node.label}"
														},
														"attrs":{
															"style":{
																"max-width":"clac(100% - 20px)",
																"display":"block"
															}
														}
													},
													{
														"componentType":"custom",
														"children":[
															{
																"componentType":"custom",
																"children":[
																	{
																		"componentType":"custom",
																		"children":[
																			{
																				"componentType":"custom",
																				"children":[
																					{
																						"componentType":"custom",
																						"action":{
																							"fire":{
																								"click":{
																									"arguments":[],
																									"actions":[
																										{
																											"handler":"${function Fun_drPh37(){/*dm*/
\n// 阻止冒泡事件/*dm*/
\nevent.stopPropagation();/*dm*/
\n// 调用平台提供的dialog方法/*dm*/
\n$.bom.page.open($.shareData.var_setting.formPath, { /*dm*/
\n  disabledFormField: ['parent_id'],/*dm*/
\n  defaultVarSaveForm: {/*dm*/
\n  ...$.shareData.var_linkFormInfo,/*dm*/
\n  parentId: data, /*dm*/
\n  parent_id: data, /*dm*/
\n} }, (data) => {/*dm*/
\n  console.log(data, 'nnnnbbbbb')/*dm*/
\n  if (data) {/*dm*/
\n    if (data.flag) {/*dm*/
\n      $.functions.fun_getTreeList(data.data);/*dm*/
\n    };/*dm*/
\n  }/*dm*/
\n}, {/*dm*/
\n  type: 'dialog',/*dm*/
\n  options: {/*dm*/
\n    \"title\": \"树模型\" + '-' + \"新建\",/*dm*/
\n    \"modal-append-to-body\": true,/*dm*/
\n    \"width\": \"648px\",/*dm*/
\n    \"close-on-press-escape\": true,/*dm*/
\n    \"close-on-click-modal\": false,/*dm*/
\n    \"class\": \"includeDialog\"/*dm*/
\n  },/*dm*/
\n});/*dm*/
\n}}",
																											"type":"function"
																										}
																									]
																								}
																							}
																						},
																						"componentName":"Button",
																						"id":"Button4035516735",
																						"props":{
																							"round":false,
																							"children":"",
																							"icon":"joyIconFont joy-icon-add",
																							"type":"text",
																							"native-type":"button"
																						},
																						"attrs":{
																							"style":{
																								"border-radius":"6px !important",
																								"padding-top":"0px",
																								"display":"inline-block",
																								"padding-left":"0px !important",
																								"width":"26px",
																								"padding-bottom":"0px",
																								"height":"26px",
																								"padding-right":"0px !important"
																							},
																							"class":"treeIcon"
																						}
																					}
																				],
																				"componentName":"DivWrapper",
																				"id":"DivWrapper86917",
																				"attrs":{
																					"style":{
																						"display":"inline-block",
																						"box-sizing":"border-box",
																						"margin-right":"4px"
																					}
																				},
																				"props":{
																					"name":"DivWrapper"
																				}
																			}
																		],
																		"id":"VIf41547",
																		"componentName":"VIf",
																		"title":"逻辑容器",
																		"props":{
																			"conditions":"${[$.shareData.var_setting.enableAddChildNode]}"
																		}
																	},
																	{
																		"componentType":"custom",
																		"children":[
																			{
																				"componentType":"custom",
																				"children":[
																					{
																						"libraryName":"@jd/joyui",
																						"componentType":"UILibrary",
																						"action":{
																							"fire":{
																								"tab-click":{
																									"arguments":[
																										"tab"
																									],
																									"actions":[
																										{
																											"handler":"${function handler(){/*dm*/
\nconst modelName = $.shareData.var_setting.modelName;/*dm*/
\n$.shareData.var_findRelatedTagAction(tab);/*dm*/
\nif (tab == 'edit') {/*dm*/
\n  $.bom.page.open($.shareData.var_setting.formPath, { detailId: data?.id, disabledFormField: ['parent_id'] }, (data) => {/*dm*/
\n    if (data) {/*dm*/
\n      if (data.flag) {/*dm*/
\n        $.functions.fun_getTreeList(data.data);/*dm*/
\n      };/*dm*/
\n    }/*dm*/
\n  }, {/*dm*/
\n    type: 'dialog',/*dm*/
\n    options: {/*dm*/
\n      \"title\": \"树模型\" + '-' + \"编辑\",/*dm*/
\n      \"modal-append-to-body\": true,/*dm*/
\n      \"width\": \"648px\",/*dm*/
\n      \"close-on-press-escape\": true,/*dm*/
\n      \"close-on-click-modal\": false,/*dm*/
\n      \"class\": \"includeDialog\"/*dm*/
\n    },/*dm*/
\n  });/*dm*/
\n};/*dm*/
\nif (tab == 'detail') {/*dm*/
\n  $.bom.page.open($.shareData.var_setting.detailPath, { detailId: data?.id, pageType: 'dialog' }, (data) => {/*dm*/
\n    if (data) {/*dm*/
\n      if (data.flag) {/*dm*/
\n        $.functions.fun_getTreeList(data.data);/*dm*/
\n      };/*dm*/
\n    }/*dm*/
\n  }, {/*dm*/
\n    type: 'dialog',/*dm*/
\n    options: {/*dm*/
\n      \"title\": \"树模型\" + '-' + \"详情\",/*dm*/
\n      \"modal-append-to-body\": true,/*dm*/
\n      \"width\": \"648px\",/*dm*/
\n      \"close-on-press-escape\": true,/*dm*/
\n      \"close-on-click-modal\": false,/*dm*/
\n      \"class\": \"includeDialog\"/*dm*/
\n    },/*dm*/
\n  });/*dm*/
\n};/*dm*/
\nif (tab == 'delete') {/*dm*/
\n  $.functions.fun_closeAllPopover();/*dm*/
\n  this.$confirm('此操作将永久删除, 是否继续?', '提示', {/*dm*/
\n    confirmButtonText: '确定',/*dm*/
\n    cancelButtonText: '取消',/*dm*/
\n    type: 'warning'/*dm*/
\n  }).then(() => {/*dm*/
\n    $.shareData.api_deleteDataTreesAction({ offiOrg: data?.id }).then(() => {/*dm*/
\n      if ($.shareData.api_deleteDataTrees) {/*dm*/
\n        this.$message.success('删除成功!');/*dm*/
\n        if (data?.id == $.shareData.var_defaulTree?.id) {/*dm*/
\n          $.functions.fun_getTreeList();/*dm*/
\n        } else {/*dm*/
\n          $.functions.fun_getTreeList(data.id);/*dm*/
\n        }/*dm*/
\n      } else {/*dm*/
\n        // this.$message.error('删除失败')/*dm*/
\n      }/*dm*/
\n    }).catch((error) => {/*dm*/
\n      this.$message.error(error)/*dm*/
\n    })/*dm*/
\n  }).catch(() => { });/*dm*/
\n};/*dm*/
\n}}",
																											"type":"function"
																										}
																									]
																								}
																							}
																						},
																						"componentName":"MoreButton",
																						"id":"MoreButton26778",
																						"version":"1.8.42",
																						"props":{
																							"data":"${[/*dm*/
\n  { \"label\": \"编辑\", \"name\": \"edit\", \"size\": \"small\", isShow: $.shareData.var_setting.enableEdit },/*dm*/
\n  { \"label\": \"详情\", \"name\": \"detail\", \"size\": \"small\", disabled: $.shareData.var_setting.enableInfo, isShow: true },/*dm*/
\n  { \"label\": \"删除\", \"name\": \"delete\", \"size\": \"small\", isShow: $.shareData.var_setting.enableDetele }/*dm*/
\n].filter(item =>/*dm*/
\n { return !item.disabled /*dm*/
\n && $.functions.fun_verifRole(item.name) /*dm*/
\n && item.isShow/*dm*/
\n  }/*dm*/
\n )}",
																							"maxCount":"${0}"
																						},
																						"attrs":{
																							"style":{
																								"border-radius":"6px !important"
																							},
																							"class":"treeIcon"
																						}
																					}
																				],
																				"componentName":"DivWrapper",
																				"id":"DivWrapper5951",
																				"attrs":{
																					"style":{
																						"display":"inline-block",
																						"line-height":"26px",
																						"box-sizing":"border-box",
																						"margin-right":"4px"
																					}
																				},
																				"props":{
																					"name":"DivWrapper",
																					"nativeOn":"${{ click(event) { event.stopPropagation(); } }}",
																					"key":"${\"moreValue\"}"
																				}
																			}
																		],
																		"id":"VIf98026",
																		"componentName":"VIf",
																		"title":"逻辑容器",
																		"props":{
																			"conditions":"${[$.shareData.var_setting.enableEdit || $.shareData.var_setting.enableDetele]}",
																			"key":"${\"moreKey\"}"
																		}
																	}
																],
																"id":"DivWrapper16668",
																"componentName":"DivWrapper",
																"title":"Div",
																"attrs":{
																	"style":{
																		"display":"none",
																		"font-size":"14px"
																	},
																	"class":"modelTreeDiv"
																},
																"props":{
																	"name":"DivWrapper"
																}
															}
														],
														"id":"DivWrapper13725",
														"componentName":"DivWrapper",
														"title":"Div",
														"attrs":{
															"style":{
																"position":"absolute",
																"right":"10px"
															}
														},
														"props":{
															"name":"DivWrapper",
															"setting":"${}"
														}
													}
												],
												"componentName":"DivWrapper",
												"id":"DivWrapper6702",
												"attrs":{
													"style":{
														"display":"flex",
														"line-height":"40px",
														"position":"relative",
														"height":"40px"
													},
													"class":"custom-tree-hover"
												},
												"props":{
													"name":"DivWrapper"
												}
											}
										],
										"action":{
											"fire":{
												"node-drop":{
													"arguments":[
														"node"
													],
													"actions":[
														{
															"handler":"${function Fun_DkZrap(node){/*dm*/
\nif (node.dropType === 'inner') {/*dm*/
\n  // 删除本来内容的元素并重新排序/*dm*/
\n  let allChangeArr = node.dropNode.childNodes.map((item, index) => {/*dm*/
\n    return {/*dm*/
\n      id: item.data.id,/*dm*/
\n      order: index,/*dm*/
\n      parentId: node.dropNode.data.id/*dm*/
\n    }/*dm*/
\n  })/*dm*/
\n  await $.shareData.api_treeOrderAction({/*dm*/
\n    modelName: $.shareData.var_setting.modelName,/*dm*/
\n    data: allChangeArr,/*dm*/
\n  })/*dm*/
\n}/*dm*/
\n/*dm*/
\nif (node.dropType == \"before\" || node.dropType == \"after\") {/*dm*/
\n  let allChangeArr = node.dropNode.parent.childNodes.map((item, index) => {/*dm*/
\n    return {/*dm*/
\n      id: item.data.id,/*dm*/
\n      order: index,/*dm*/
\n      parentId: node.dropNode.parent?.data?.id || null/*dm*/
\n    }/*dm*/
\n  })/*dm*/
\n  await $.shareData.api_treeOrderAction({/*dm*/
\n    modelName: $.shareData.var_setting.modelName,/*dm*/
\n    data: allChangeArr,/*dm*/
\n  })/*dm*/
\n}/*dm*/
\nif ($.shareData.api_treeOrder.code !== 200) {/*dm*/
\n  this.$message.error($.shareData.api_treeOrder.msg);/*dm*/
\n}/*dm*/
\n}}",
															"type":"function"
														}
													]
												},
												"node-click":{
													"arguments":[
														"data",
														"node"
													],
													"actions":[
														{
															"handler":"${function handler(){\r/*dm*/
\n$.functions.fun_closeAllPopover();\r/*dm*/
\n$.shareData.var_defaulTree = { ...data.data };\r/*dm*/
\n$.$emit('changeItem', $.shareData.var_defaulTree)\r/*dm*/
\n$.functions.fun_getEmit(\"queryList\", \"\", data.data);\r/*dm*/
\n}}",
															"type":"function"
														}
													]
												}
											}
										},
										"componentName":"Tree",
										"id":"Tree1161",
										"version":"1.8.42",
										"props":{
											"currentNodeKey":"${$.shareData.var_defaulTree?.id}",
											"accordion":false,
											"indent":14,
											"lazy":false,
											"renderAfterExpand":true,
											":allowDrag":"${function handler(node){return true;}}",
											"props":"${{ \r/*dm*/
\n  children: 'children',\r/*dm*/
\n  label: 'name',\r/*dm*/
\n}}",
											"ref":"modelTree",
											"autoExpandParent":true,
											"showCheckbox":false,
											"draggable":true,
											"v-resizable":true,
											"defaultExpandedKeys":"${$.shareData.var_defaultExpanded}",
											":allowDrop":"${function handler(draggingNode, dropNode){return true}}",
											"defaultExpandAll":false,
											"joyData":"${$.shareData.var_tableList}",
											"highlightCurrent":false,
											"showInput":true,
											"nodeKey":"id",
											"checkStrictly":false,
											"filterList":"${[\"name\"]}",
											"v-slot":"{data, node}",
											"expandOnClickNode":true
										},
										"attrs":{
											"style":{
												"width":"264px",
												"min-width":"264px",
												"height":"calc(100% - 66px)"
											},
											"class":"modelTree"
										}
									}
								],
								"componentName":"VIf",
								"id":"VIf11192",
								"props":{
									"conditions":"${[$.shareData.var_tableList.length]}"
								}
							}
						],
						"componentName":"DivWrapper",
						"id":"DivWrapper66837",
						"title":"TreeBoxLeft",
						"attrs":{
							"style":{
								"box-shadow":"1px 0 0 0 rgba(235,238,245,1)",
								"flexDirection":"column",
								"display":"flex",
								"min-width":"264px",
								"height":"100%"
							},
							"setting":"${JSON.stringify({\"name\": \"tree\"})}"
						}
					},
					{
						"children":[
							{
								"componentType":"custom",
								"children":[
									{
										"children":[
											{
												"componentName":"Include",
												"id":"Include-ASrTbYdw",
												"attrs":{
													"style":{
														"padding-top":"16px",
														"overflow":"hidden",
														"padding-left":"16px",
														"padding-bottom":"16px",
														"height":"100%",
														"padding-right":"16px"
													},
													"class":"treeBoxRight"
												},
												"props":{
													":onLoad":"${function handler(){  const queryConditions = [\r/*dm*/
\n    {\r/*dm*/
\n      fieldName: $.shareData.var_setting.linkTabs[0]?.fieldName,\r/*dm*/
\n      conditionType: \"EQ\",\r/*dm*/
\n      value: $.shareData.var_defaulTree,\r/*dm*/
\n      modelName: $.shareData.var_setting.modelName\r/*dm*/
\n    }\r/*dm*/
\n  ];\r/*dm*/
\n  $.functions.fun_getEmit(\"queryList\", queryConditions);\r/*dm*/
\n}}",
													"pageId":"${$.shareData.var_setting.linkTabs[0]?.url}"
												}
											}
										],
										"componentName":"VIf",
										"id":"VIf-frSPZcBR",
										"props":{
											"conditions":"${[$.shareData.var_defaulTree.id]}"
										}
									}
								],
								"componentName":"DivWrapper",
								"id":"DivWrapper-7ytNXNHK",
								"title":"TreeBoxRight",
								"attrs":{
									"style":{
										"flex":"1 1 0%",
										"width":"100%",
										"height":"100%",
										"min-width":"1px"
									}
								}
							},
							{
								"children":[
									{
										"children":[
											{
												"children":[
													{
														"children":[
															{
																"componentType":"custom",
																"children":[
																	{
																		"componentName":"Include",
																		"id":"Include-wr4NTyST",
																		"attrs":{
																			"style":{
																				"padding":"0px 16px 0",
																				"overflow":"hidden",
																				"flex":"1"
																			},
																			"class":"treeBoxRight"
																		},
																		"props":{
																			":onLoad":"${function handler(){$.$emit('changeItem', $.shareData.var_defaulTree)\r/*dm*/
\n}}",
																			"pageId":"${$.shareData.var_setting.detailPath}",
																			"url":"${}"
																		}
																	}
																],
																"componentName":"DivWrapper",
																"id":"DivWrapper-KtQ6dS5A",
																"title":"TreeBoxRight"
															}
														],
														"componentName":"VIf",
														"id":"VIf-NNHTi7hK",
														"props":{
															"conditions":"${[\"base_info\" === $.shareData.var_activeDetailTab && !$.shareData.var_pageLoading && $.shareData.var_defaulTree]}"
														}
													}
												],
												"componentName":"TabPane",
												"id":"TabPane-xpc4BfX2",
												"props":{
													"name":"${\"base_info\"}",
													"label":"${\"基本信息\"}"
												}
											}
										],
										"componentName":"VIf",
										"id":"VIf-EwnbMwmC",
										"props":{
											"conditions":"${[$.shareData.var_setting.enableInfo]}"
										}
									},
									{
										"children":[
											{
												"children":[
													{
														"children":[
															{
																"componentType":"custom",
																"children":[
																	{
																		"componentName":"Include",
																		"id":"Include-PCTXy8NT",
																		"attrs":{
																			"style":{
																				"padding-top":"0px",
																				"overflow":"hidden",
																				"padding-left":"24px",
																				"padding-bottom":"10px",
																				"height":"100%",
																				"padding-right":"24px"
																			},
																			"class":"treeBoxRight"
																		},
																		"props":{
																			":onLoad":"${function handler(){  console.log('bababa', 'detail')\r/*dm*/
\n  const queryConditions = [\r/*dm*/
\n    {\r/*dm*/
\n      fieldName: __item.fieldName,\r/*dm*/
\n      conditionType: \"EQ\",\r/*dm*/
\n      value: $.shareData.var_defaulTree,\r/*dm*/
\n      modelName: $.shareData.var_setting.modelName\r/*dm*/
\n    }\r/*dm*/
\n  ];\r/*dm*/
\n  $.functions.fun_getEmit(\"queryList\", queryConditions);\r/*dm*/
\n}}",
																			"pageId":"${__item.url}"
																		}
																	}
																],
																"componentName":"DivWrapper",
																"id":"DivWrapper28953",
																"title":"TreeBoxRight",
																"attrs":{
																	"style":{
																		"height":"100%"
																	}
																}
															}
														],
														"componentName":"VIf",
														"id":"VIf-dG8QekJe",
														"props":{
															"conditions":"${[__item.name === $.shareData.var_activeDetailTab && !$.shareData.var_pageLoading]}"
														}
													}
												],
												"componentName":"TabPane",
												"id":"TabPane-JJjczHxR",
												"props":{
													"name":"${__item.name}",
													"label":"${__item.label}"
												},
												"attrs":{
													"style":{
														"height":"100%"
													},
													"setting":"${JSON.stringify({ \"name\": \"association-model\", \"data\": __item })}"
												}
											}
										],
										"componentName":"VFor",
										"id":"VFor-imJ2iscC",
										"props":{
											"forEach":"${$.shareData.var_defaulTree? $.shareData.var_setting.linkTabs : []}",
											"forEachKey":"__item",
											"index":"$index"
										}
									}
								],
								"action":{
									"fire":{
										"tab-click":{
											"arguments":[
												"value"
											],
											"actions":[
												{
													"handler":"${function Handler(value){console.log(value, 'var_activeDetailTabAction');/*dm*/
\n$.bom.dev?.set?.('activeDetailTab', value.name)/*dm*/
\n$.shareData.var_activeDetailTabAction(value.name)}}",
													"type":"function"
												}
											]
										}
									}
								},
								"componentName":"Tabs",
								"id":"Tabs-ndpwBjfG",
								"props":{
									"type":"top",
									"v-model":"${$.shareData.var_activeDetailTab}"
								},
								"attrs":{
									"style":{
										"overflow":"auto",
										"width":"calc(100% - 264px)",
										"height":"100%"
									},
									"class":"detail-tabs"
								}
							}
						],
						"componentName":"VIf",
						"id":"VIf-jE5XaNbK",
						"props":{
							"conditions":"${() => {\r/*dm*/
\n  if ($.shareData.var_setting.linkTabs.length == 1 && !$.shareData.var_setting.enableInfo) {\r/*dm*/
\n    return [true, false]\r/*dm*/
\n  }\r/*dm*/
\n  return [false, true]\r/*dm*/
\n}}"
						}
					}
				],
				"componentName":"DivWrapper",
				"id":"DivWrapper20714",
				"title":"TreeBox",
				"class":"recordsPage",
				"attrs":{
					"style":{
						"display":"flex",
						"height":"100%"
					}
				}
			}
		],
		"functions":[
			{
				"default":"${function fun_isInclude(){/*dm*/
\n  if (this.$el.parentNode.getAttribute(\"c_v_l\") === \"Include\") {/*dm*/
\n    return true;/*dm*/
\n  }/*dm*/
\n  return false;/*dm*/
\n}}",
				"description":"",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_isInclude"
			},
			{
				"default":"${function fun_verifRole(code) {/*dm*/
\n    if ($.shareData.var_pageRole.hasOwnProperty(code)) {/*dm*/
\n        return $.shareData.var_pageRole[code]/*dm*/
\n    }/*dm*/
\n    return false/*dm*/
\n}}",
				"description":"高级筛选是否启用有多少个",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_verifRole"
			},
			{
				"default":"${function advancedLoadData(queryData) {/*dm*/
\n          const data = queryData || $.shareData.var_searchType?.data || {}/*dm*/
\n          const modelName = $.shareData.var_setting.modelName;/*dm*/
\n          const { keywordQuery: keyword, attributes: scopeAttributes } = $.shareData.var_searchType?.searchData;/*dm*/
\n          let { keywordQuery, ...rest } = data;/*dm*/
\n          const fieldMap = $.global.shareData.modelsInfoData.fieldsMap[modelName];/*dm*/
\n          rest.queryConditions = rest.queryConditions?.map(item => {/*dm*/
\n            if (fieldMap.get(item.fieldName)?.businessType == 'department') {/*dm*/
\n              item.value = item.value?.organizationCode || item.value || '';/*dm*/
\n              item.filter = !!item.value;/*dm*/
\n            } else {/*dm*/
\n              item.filter = true/*dm*/
\n            }/*dm*/
\n            if(typeof item.value == 'string') {/*dm*/
\n              item.value = item.value.trim()/*dm*/
\n            }/*dm*/
\n            return item;/*dm*/
\n          }).filter(i => i.filter);/*dm*/
\n          let params = {};/*dm*/
\n          for (let i in $.shareData.var_pageParams) {/*dm*/
\n            params[i] = $.shareData.var_pageParams[i];/*dm*/
\n          };/*dm*/
\n          await $.shareData.var_pageLoadingAction(true);/*dm*/
\n          let querySorts = $.shareData.var_querySorts/*dm*/
\n          const attributes = $.shareData.var_columnsShow?.map(item => item.fieldName);/*dm*/
\n          const modelValueFormat = await $.global.functions.modelValueFormat($.shareData.var_setting.modelName)/*dm*/
\n          const caseQueryData = {/*dm*/
\n            \"keywordQuery\": {/*dm*/
\n              keyword,/*dm*/
\n              scopeAttributes/*dm*/
\n            },/*dm*/
\n            ...rest,/*dm*/
\n            attributes: attributes,/*dm*/
\n            ...$.shareData.var_tabsSearchData,/*dm*/
\n            querySorts,/*dm*/
\n            modelName: modelName,/*dm*/
\n          }/*dm*/
\n          $.shareData.var_searchTypeAction({/*dm*/
\n            cacheData: caseQueryData/*dm*/
\n          });/*dm*/
\n          let queryConditions = {};/*dm*/
\n          let qureydata = {/*dm*/
\n            ...caseQueryData,/*dm*/
\n            ...params/*dm*/
\n          }/*dm*/
\n          if ($.shareData.var_isQuery) {/*dm*/
\n            queryConditions = {/*dm*/
\n              fieldName: $.shareData.var_isQuery[0].fieldName,/*dm*/
\n              conditionType: $.shareData.var_isQuery[0].conditionType,/*dm*/
\n              value: $.shareData.var_isQuery[0]?.value?.id/*dm*/
\n            };/*dm*/
\n            qureydata.queryConditions.push({ ...queryConditions });/*dm*/
\n          }/*dm*/
\n          $.shareData.api_getListAction(qureydata).then(() => {/*dm*/
\n            $.shareData.var_pageLoadingAction(false);/*dm*/
\n            this.$refs.jtable?.doLayout();/*dm*/
\n            if ($.shareData.api_getList.code == 200) {/*dm*/
\n              for (let i in $.shareData.var_pageParams) {/*dm*/
\n                $.shareData.var_pageParams[i] = $.shareData.api_getList.data[i]/*dm*/
\n              }/*dm*/
\n            }/*dm*/
\n            let list = $.shareData.api_getList?.data?.records?.map(item => {/*dm*/
\n              return modelValueFormat.input(item)/*dm*/
\n            });/*dm*/
\n            $.shareData.var_tableListAction(list)/*dm*/
\n          }).catch((e) => {/*dm*/
\n            $.shareData.var_pageLoadingAction(false);/*dm*/
\n          });/*dm*/
\n          $.functions.fun_getAdvancedFilter(true);/*dm*/
\n        }}",
				"description":"初始化加载数据",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_loadData"
			},
			{
				"default":"${function draftLoadData() {/*dm*/
\n          const modelValueFormat = await $.global.functions.modelValueFormat($.shareData.var_setting.modelName)/*dm*/
\n          const modelName = $.shareData.var_setting.modelName;/*dm*/
\n          if ($.global.shareData.modelsInfoData?.draft[modelName]) {/*dm*/
\n            let draftData = {/*dm*/
\n              queryConditions: [/*dm*/
\n                {/*dm*/
\n                  fieldName: \"model_name\",/*dm*/
\n                  conditionType: \"EQ\",/*dm*/
\n                  value: modelName/*dm*/
\n                }/*dm*/
\n              ],/*dm*/
\n              pageSize: 10000/*dm*/
\n            }/*dm*/
\n            await $.shareData.api_getCommonDraftAction(draftData).then(()=>{/*dm*/
\n              if($.shareData.api_getCommonDraft?.code==200){/*dm*/
\n                let list = $.shareData.api_getCommonDraft?.data?.records.map(item => {/*dm*/
\n                  return modelValueFormat.input(item)/*dm*/
\n                });/*dm*/
\n                $.shareData.var_draftData = list;/*dm*/
\n              }/*dm*/
\n            }).catch((error)=>{/*dm*/
\n              console.log(error);/*dm*/
\n            })/*dm*/
\n            }/*dm*/
\n        }}",
				"description":"草稿箱加载",
				"type":"function",
				"title":"",
				"key":"fun_loadDraftData"
			},
			{
				"default":"${function getFieldsBusinessType(modelName, fieldName){/*dm*/
\n    const fields = $.shareData.var_columnsShow/*dm*/
\n    let businessType = 'text'/*dm*/
\n    let _sourse = null;/*dm*/
\n    const other = {/*dm*/
\n        relatedModelField: null/*dm*/
\n    }/*dm*/
\n    fields.map(item => {/*dm*/
\n        const modelFieldEntity = item.modelField;/*dm*/
\n        if (item.fieldName === fieldName) {/*dm*/
\n            _sourse = item/*dm*/
\n            if (modelFieldEntity) {/*dm*/
\n                switch (item.businessType) {/*dm*/
\n                    case 'formula': businessType = modelFieldEntity.formulaReturnType; return;/*dm*/
\n                    case 'findRelated': businessType = getRelatedModelBusinessType(modelFieldEntity); return;/*dm*/
\n                    case 'findRelatedMulti': businessType = getRelatedModelBusinessType(modelFieldEntity); return;/*dm*/
\n                    default: businessType = item.businessType;/*dm*/
\n                }/*dm*/
\n            } else {/*dm*/
\n                businessType = item.businessType;/*dm*/
\n                _sourse = item/*dm*/
\n            }/*dm*/
\n           /*dm*/
\n        }/*dm*/
\n    })/*dm*/
\n/*dm*/
\n    function getRelatedModelBusinessType(data) {/*dm*/
\n        let relatedModelField = data.relatedModel?.modelFields?.find(item => item.fieldName == data.calculationFieldName)/*dm*/
\n        other.relatedModelField = relatedModelField;/*dm*/
\n        return relatedModelField?.businessType || businessType/*dm*/
\n    }/*dm*/
\n    if (!businessType || !_sourse) {/*dm*/
\n        return {};/*dm*/
\n    }/*dm*/
\n    const minWidth = _sourse.fieldWidth || $.shareData.var_tableBusinessTypeWidth[businessType] || 120;/*dm*/
\n  /*dm*/
\n    return {/*dm*/
\n        fieldName,/*dm*/
\n        minWidth,/*dm*/
\n        fieldWidth: _sourse.fieldWidth,/*dm*/
\n        businessType,/*dm*/
\n        modelFieldEntity: _sourse.modelFieldEntity,/*dm*/
\n        _sourse,/*dm*/
\n        other/*dm*/
\n    }/*dm*/
\n}}",
				"description":"",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_getFieldsBusinessType"
			},
			{
				"default":"${function tablePlanLoadData() {/*dm*/
\n/*dm*/
\n            let { columnPlanId } = $.shareData.var_tableSetting/*dm*/
\n/*dm*/
\n            const modelName = $.shareData.var_setting?.modelName/*dm*/
\n            if (columnPlanId) {/*dm*/
\n/*dm*/
\n              if ($.shareData.api_getConditionsPlan?.data?.yeqian?.length > 0) {/*dm*/
\n                if (this.$refs['record-table']?.currentPlan?.system) {/*dm*/
\n                  this.$refs['record-table'].currentPlan = {}/*dm*/
\n                }/*dm*/
\n                let activeTags = $.shareData.api_getConditionsPlan?.data?.yeqian[0]/*dm*/
\n                if ($.shareData.var_searchRuleValue) {/*dm*/
\n                  activeTags = $.shareData.api_getConditionsPlan?.data?.yeqian.find(item => item.planCode === $.shareData.var_searchRuleValue)/*dm*/
\n                }/*dm*/
\n                if (activeTags?.planId) {/*dm*/
\n                  columnPlanId = activeTags.planId/*dm*/
\n                }/*dm*/
\n              }/*dm*/
\n              await $.shareData.api_tablePlanListAction({ modelName, planId: columnPlanId })/*dm*/
\n/*dm*/
\n              const list = $.shareData.api_tablePlanList?.filter(i => i.isDefault) || []/*dm*/
\n/*dm*/
\n              if (list.length > 0) {/*dm*/
\n/*dm*/
\n                const { system, id } = list[0]/*dm*/
\n/*dm*/
\n                await $.shareData.api_getTablePlanInfoAction({ modelName, planId: id, system })/*dm*/
\n/*dm*/
\n                const { planInfoDtoList,freeze, ...otherInfo } = $.shareData.api_getTablePlanInfo/*dm*/
\n/*dm*/
\n                $.shareData.var_tablePlanFieldsAction([...planInfoDtoList]);/*dm*/
\n/*dm*/
\n                let freezeNew = {action:true,head:true}/*dm*/
\n                if(freeze){/*dm*/
\n                  freezeNew = freeze/*dm*/
\n                }/*dm*/
\n                $.shareData.var_tablePlanOtherInfoAction({freeze:freezeNew, ...otherInfo })/*dm*/
\n/*dm*/
\n                const listInner = [...planInfoDtoList]/*dm*/
\n/*dm*/
\n                $.functions.fun_filterTableColumn(listInner)/*dm*/
\n/*dm*/
\n              }/*dm*/
\n/*dm*/
\n            }/*dm*/
\n}}",
				"description":"列表视图",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_loadTablePlanData"
			},
			{
				"default":"${function fun_genTableId(){/*dm*/
\n  const userData = $.bom.getHostData('userInfo')/*dm*/
\n  const genId = `${$.shareData.api_getTablePlanInfo.id}_${userData.personId || userData.userName || userData.pin}_${$.shareData.var_tableColumnWidth.id}`/*dm*/
\n  return genId/*dm*/
\n}}",
				"description":"获取表格唯一id",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_genTableId"
			},
			{
				"default":"${function columnFilter(cloumns) {/*dm*/
\n        const modelName = $.shareData.var_setting.modelName;/*dm*/
\n        const listInner = cloumns.reduce((prev, cur) => {/*dm*/
\n          const allFieldList = []/*dm*/
\n          if (cur.show) {/*dm*/
\n            const field = $.global.shareData.modelsInfoData.fieldsMap[modelName].get(cur.name);/*dm*/
\n            // 先添加自己/*dm*/
\n            allFieldList.push({/*dm*/
\n              ...cur,/*dm*/
\n              businessType: field.businessType,/*dm*/
\n              fieldName: cur.name,/*dm*/
\n              modelField: field/*dm*/
\n              })/*dm*/
\n          }/*dm*/
\n/*dm*/
\n          // 处理查找关系逻辑/*dm*/
\n          if (cur.relatedDtoList?.length > 0) {/*dm*/
\n            cur.relatedDtoList.map(item => {/*dm*/
\n              const relatedField = $.global.shareData.modelsInfoData.fieldsMap[modelName].get(cur.name);/*dm*/
\n              // 再添加循环的数据/*dm*/
\n              if (item.show) {/*dm*/
\n                allFieldList.push({/*dm*/
\n                  ...item,/*dm*/
\n                  name: cur.name,/*dm*/
\n                  fieldName: `${cur.name}.${item.name}`,/*dm*/
\n                  businessType: relatedField.businessType,/*dm*/
\n                  modelField: {/*dm*/
\n                    ...relatedField,/*dm*/
\n                    calculationFieldName: item.relatedFieldName/*dm*/
\n                  }/*dm*/
\n                })/*dm*/
\n              }/*dm*/
\n            })/*dm*/
\n          }/*dm*/
\n          return [...prev, ...allFieldList]/*dm*/
\n        }, [])/*dm*/
\n        listInner.sort((i, j) => {/*dm*/
\n/*dm*/
\n          if (i.index > j.index) {/*dm*/
\n/*dm*/
\n            return 1;/*dm*/
\n/*dm*/
\n          } else if (i.index < j.index) {/*dm*/
\n/*dm*/
\n            return -1;/*dm*/
\n/*dm*/
\n          } else if (i.index == j.index) {/*dm*/
\n/*dm*/
\n            return 0;/*dm*/
\n/*dm*/
\n          }/*dm*/
\n        });/*dm*/
\n/*dm*/
\n        let str = localStorage.getItem($.functions.fun_genTableId())/*dm*/
\n        let widths = str ? JSON.parse(str) : {};/*dm*/
\n        $.shareData.var_tableColumnWidth.widths = widths/*dm*/
\n        const attributes = listInner.reduce((prev, cur) => [...prev, cur.name], [])/*dm*/
\n/*dm*/
\n        $.shareData.var_searchAttributesAction(attributes)/*dm*/
\n/*dm*/
\n        $.shareData.var_columnsShowAction(listInner);/*dm*/
\n      }}",
				"description":"table列的过滤",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_filterTableColumn"
			},
			{
				"default":"${function queryConditionList() {/*dm*/
\n          const modelName = $.shareData.var_setting?.modelName/*dm*/
\n          await $.shareData.api_getConditionsPlanAction({ modelName: modelName });/*dm*/
\n        }}",
				"description":"获取查询条件方案列表",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_loadConditionPlanList"
			},
			{
				"default":"${function intSeach() {/*dm*/
\n          const modelName = $.shareData.var_setting?.modelName/*dm*/
\n          let map = $.global.shareData.modelsInfoData.fieldsMap[modelName];/*dm*/
\n          const yeqian = $.shareData.api_getConditionsPlan?.data?.yeqian/*dm*/
\n          if (yeqian && yeqian.length > 0) {/*dm*/
\n            const active = yeqian.find(item => item.defaultPlan)/*dm*/
\n            let sort = yeqian.sort((a, b) => a.planSort - b.planSort)/*dm*/
\n            let activeData = active || sort[0];/*dm*/
\n            $.shareData.var_searchRuleValueAction(activeData.planCode);/*dm*/
\n            let activeDatas = JSON.parse(activeData.conditionsJson)[0];/*dm*/
\n            activeDatas.standardQueryConditions = $.functions.fun_formatQueryConditions(activeDatas.standardQueryConditions, map).filter(i => i.value != \"\" && i.value != null );/*dm*/
\n            $.shareData.var_tabsSearchDataAction(activeDatas);/*dm*/
\n          }/*dm*/
\n          let activeTag = $.shareData.api_getConditionsPlan.data.yuzhi.find(item => item.defaultPlan);/*dm*/
\n          activeTag.conditionsJson = activeTag.conditionsJson || '[{\"standardQueryConditions\":[],\"standardCombinationRule\":\"\"}]'/*dm*/
\n          // 处理部门/*dm*/
\n          activeTag.conditionsValue.forEach(item => {/*dm*/
\n            if (map.get(item.fieldName)?.businessType == 'department') {/*dm*/
\n              // 表达式或默认值 内置返回 string/*dm*/
\n              if (typeof item.value == 'string') {/*dm*/
\n/*dm*/
\n              } else {/*dm*/
\n                if (item.conditionType == \"EQ\") {/*dm*/
\n                  item.value = { ...item.value, ...item.selectObj} || {}/*dm*/
\n                }/*dm*/
\n              }/*dm*/
\n            }/*dm*/
\n          })/*dm*/
\n          if (activeTag.conditionsJson) {/*dm*/
\n            $.shareData.var_currentPlanAction({ ...activeTag });/*dm*/
\n            $.shareData.var_currentPlanValueAction(activeTag.planCode);/*dm*/
\n            let qvancedQuery = JSON.parse(activeTag.conditionsJson)[0]/*dm*/
\n            qvancedQuery.standardQueryConditions = $.functions.fun_formatQueryConditions(qvancedQuery.standardQueryConditions, map)/*dm*/
\n            let standardQueryConditions = qvancedQuery.standardQueryConditions.filter(item => item.value !== \"\" && item.value !== null && item.value?.length !== 0);/*dm*/
\n            $.functions.fun_loadData({/*dm*/
\n              queryConditions: standardQueryConditions,/*dm*/
\n              combinationRule: qvancedQuery.standardCombinationRule/*dm*/
\n            })/*dm*/
\n            $.shareData.var_searchTypeAction({/*dm*/
\n              type: 'advanced', data: {/*dm*/
\n                queryConditions: standardQueryConditions,/*dm*/
\n                combinationRule: qvancedQuery.standardCombinationRule/*dm*/
\n              }/*dm*/
\n            })/*dm*/
\n          }/*dm*/
\n      }}",
				"description":"初始查询函数",
				"type":"function",
				"title":"",
				"key":"fun_intSeach"
			},
			{
				"default":"${function fun_loadCache(modelTableKey) {/*dm*/
\n          let pageParams = localStorage.getItem('pageParams_' + modelTableKey)/*dm*/
\n          if(pageParams) {/*dm*/
\n            pageParams = JSON.parse(pageParams);/*dm*/
\n            await $.shareData.var_pageParamsAction(pageParams);/*dm*/
\n          }/*dm*/
\n        }}",
				"description":"初始化缓存数据",
				"type":"function",
				"title":"",
				"key":"fun_loadPageParamsCache"
			},
			{
				"default":"${/*dm*/
\n          function queryConditionsFormat(queryConditions = [], modelFieldMaps){/*dm*/
\n            function format(fieldName, value, modelEntry) {/*dm*/
\n              if(!value) {/*dm*/
\n                return value;/*dm*/
\n              }/*dm*/
\n              let fieldNames = fieldName.split('.');/*dm*/
\n              const businessType = modelEntry.businessType;/*dm*/
\n              let newValue = value;/*dm*/
\n              // 普通字段/*dm*/
\n              if (fieldNames.length === 1) {/*dm*/
\n                switch (businessType) {/*dm*/
\n                  case 'user':/*dm*/
\n                    if (typeof value !== 'string' && value[0]) {/*dm*/
\n                      newValue = value[0].erp || value[0];/*dm*/
\n                    }/*dm*/
\n                    break;/*dm*/
\n                  case 'department':/*dm*/
\n                    if (value?.organizationCode) {/*dm*/
\n                      newValue = value?.organizationCode || '';/*dm*/
\n                    }/*dm*/
\n                    break;/*dm*/
\n                  case 'multipleUser':/*dm*/
\n                    newValue = Array.isArray(value)/*dm*/
\n                      ? value?.map((v) => {/*dm*/
\n                        return v.erp;/*dm*/
\n                      })/*dm*/
\n                      : [value];/*dm*/
\n                    break;/*dm*/
\n                  case 'findRelated':/*dm*/
\n                    newValue = value?.id || value;/*dm*/
\n                    break;/*dm*/
\n                  case 'findRelatedMulti':/*dm*/
\n                    newValue = value.map((v) => {/*dm*/
\n                      return v.id;/*dm*/
\n                    });/*dm*/
\n                    break;/*dm*/
\n                  default:/*dm*/
\n                    null;/*dm*/
\n                }/*dm*/
\n                return newValue;/*dm*/
\n              } else {/*dm*/
\n                const field = modelEntry.relatedModel.modelFields.find(/*dm*/
\n                  (item) => item.fieldName === fieldNames[1]/*dm*/
\n                );/*dm*/
\n                return format(fieldNames[1], value, field);/*dm*/
\n              }/*dm*/
\n            }/*dm*/
\n            return queryConditions.map(item => {/*dm*/
\n              let fieldName = item.fieldName.split('.');/*dm*/
\n              const value = format(item.fieldName, item.value, modelFieldMaps.get(fieldName[0]));/*dm*/
\n              if (Array.isArray(value)) {/*dm*/
\n                return {/*dm*/
\n                  ...item,/*dm*/
\n                  values: value,/*dm*/
\n                };/*dm*/
\n              } else {/*dm*/
\n                return {/*dm*/
\n                  ...item,/*dm*/
\n                  value,/*dm*/
\n                };/*dm*/
\n              }/*dm*/
\n            })/*dm*/
\n          }/*dm*/
\n        }",
				"description":"",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_formatQueryConditions"
			},
			{
				"default":"${function getTreeList(data) {/*dm*/
\n          const modelName = $.shareData.var_setting.modelName;/*dm*/
\n          const modelValueFormat = await $.global.functions.modelValueFormat($.shareData.var_setting.modelName);/*dm*/
\n          $.shareData.api_getListAction({modelName: modelName, pageSize: 9999, ...$.shareData.var_findQuery}).then(() => {/*dm*/
\n            $.shareData.var_pageLoadingAction(false);/*dm*/
\n            this.$refs.jtable?.doLayout();/*dm*/
\n            if ($.shareData.api_getList.code == 200) {/*dm*/
\n              for (let i in $.shareData.var_pageParams) {/*dm*/
\n                $.shareData.var_pageParams[i] = $.shareData.api_getList.data[i]/*dm*/
\n              }/*dm*/
\n            }/*dm*/
\n            let list = $.shareData.api_getList?.data?.records.map(item => {/*dm*/
\n              return modelValueFormat.input(item)/*dm*/
\n            });/*dm*/
\n            $.shareData.var_tableListAction($.functions.fun_treeHierarchy(list));/*dm*/
\n            if(data) {/*dm*/
\n              // 删除没找到默认选中当前/*dm*/
\n              this.$nextTick(() => {/*dm*/
\n                let obj = $.functions.fun_defaultDepartmentId([ ...$.shareData.var_tableList ], data);/*dm*/
\n                let copyObj = JSON.parse(JSON.stringify(obj));/*dm*/
\n                if (!obj) {/*dm*/
\n                  $.shareData.var_defaultExpandedAction([ JSON.parse(JSON.stringify($.shareData.var_defaulTree?.id)) ]);/*dm*/
\n                  this.$refs.modelTree.$refs.tree.setCurrentKey($.shareData.var_defaulTree);/*dm*/
\n                } else {/*dm*/
\n                  $.shareData.var_defaultExpandedAction([ copyObj?.id ]);/*dm*/
\n                  $.shareData.var_defaulTreeAction({ ...obj });/*dm*/
\n                  this.$refs.modelTree.$refs.tree.setCurrentKey($.shareData.var_defaulTree);/*dm*/
\n                  $.functions.fun_getEmit(\"queryList\", \"\", obj);/*dm*/
\n                  $.$emit('changeItem', $.shareData.var_defaulTree)/*dm*/
\n                }/*dm*/
\n              });/*dm*/
\n            } else {/*dm*/
\n              $.shareData.var_defaultExpandedAction([ JSON.parse(JSON.stringify($.shareData.var_tableList[0]?.id)) ]);/*dm*/
\n              $.shareData.var_defaulTreeAction({ ...$.shareData.var_tableList[0] });/*dm*/
\n            };/*dm*/
\n          }).catch(() => {/*dm*/
\n            $.shareData.var_pageLoadingAction(false);/*dm*/
\n          });/*dm*/
\n        }}",
				"description":"获取树的数据",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_getTreeList"
			},
			{
				"default":"${function treeHierarchy(data) {/*dm*/
\n            const hierarchy = {};/*dm*/
\n            data.forEach(item => {/*dm*/
\n              item.children = [];/*dm*/
\n              hierarchy[item.id] = item;/*dm*/
\n            });/*dm*/
\n            data.sort((a, b) => a.order - b.order).forEach(item => {/*dm*/
\n              let parentId = null;/*dm*/
\n              if (Object.prototype.toString.call(item.parent_id) === '[object Object]') {/*dm*/
\n                parentId = item.parent_id?.id;/*dm*/
\n              } else {/*dm*/
\n                parentId = item.parent_id;/*dm*/
\n              }/*dm*/
\n              if (parentId !== null && hierarchy[parentId]) {/*dm*/
\n                hierarchy[parentId].children.push(item);/*dm*/
\n              }/*dm*/
\n            });/*dm*/
\n            const result = Object.values(hierarchy).filter(item => {/*dm*/
\n              if (Object.prototype.toString.call(item.parent_id) === '[object Object]') {/*dm*/
\n                return !item.parent_id?.id/*dm*/
\n              } else {/*dm*/
\n                return !item.parent_id/*dm*/
\n              }/*dm*/
\n            });/*dm*/
\n            return result.sort((a, b) => a.order - b.order);/*dm*/
\n        }}",
				"description":"树层次结构",
				"type":"function",
				"title":"",
				"key":"fun_treeHierarchy"
			},
			{
				"default":"${function fun_getEmit(name, data, id){/*dm*/
\n            $.$emit(name, data, id);/*dm*/
\n}}",
				"description":"",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_getEmit"
			},
			{
				"default":"${function closeAll() {/*dm*/
\n          let btns = document.getElementsByClassName('treeIcon');/*dm*/
\n          [...btns].forEach(node => {/*dm*/
\n            node.__vue__.$parent.showPopper = false;/*dm*/
\n          });/*dm*/
\n        }}",
				"description":"关闭popover",
				"type":"function",
				"title":"",
				"key":"fun_closeAllPopover"
			},
			{
				"default":"${function defaultDepartmentId(departments, targetId) {/*dm*/
\n          for (let department of departments) {/*dm*/
\n            if (department.id === targetId) {/*dm*/
\n              return department;/*dm*/
\n            } else if (department.children && department.children.length > 0) {/*dm*/
\n              const result = $.functions.fun_defaultDepartmentId(department.children, targetId);/*dm*/
\n              if (result !== null) {/*dm*/
\n                return result;/*dm*/
\n              }/*dm*/
\n            }/*dm*/
\n          }/*dm*/
\n          return null;/*dm*/
\n        }}",
				"description":"默认当前选中项",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_defaultDepartmentId"
			}
		],
		"shareData":[
			{
				"default":"[]",
				"description":"表格数据",
				"key":"var_tableList"
			},
			{
				"default":"{\"pageNum\": 1,\"pageSize\": 10,\"totalPage\": 1,\"totalSize\": 1}",
				"description":"表格分页请求参数",
				"key":"var_pageParams"
			},
			{
				"default":true,
				"description":"table加载loading",
				"key":"var_pageLoading"
			},
			{
				"default":"[]",
				"description":"表格批量删除处理",
				"key":"var_multipleSelection"
			},
			{
				"default":"",
				"description":"单选表格模式的选中值",
				"key":"var_singleSelection"
			},
			{
				"default":"{/*dm*/
\n        \"columnPlanId\": false,/*dm*/
\n        \"showPagination\":true,/*dm*/
\n        \"paginationMode\":\"complete\",/*dm*/
\n        \"paginationAlign\":\"right\",/*dm*/
\n        \"pageSizes\":[10,20,30,40,50],/*dm*/
\n        \"showHeader\": true,/*dm*/
\n        \"stripe\": false,/*dm*/
\n        \"showNo\": false,/*dm*/
\n        \"selectRowType\": \"single\",/*dm*/
\n        \"border\": false,/*dm*/
\n        \"mode\": \"normal\",/*dm*/
\n        \"batchDelFlag\": true,/*dm*/
\n        \"addFlag\": true,/*dm*/
\n        \"exportDataFlag\": false,/*dm*/
\n        \"addPage\": \"\",/*dm*/
\n        \"editPage\": \"\",/*dm*/
\n        \"detailPage\": \"\",/*dm*/
\n        \"headerEllipsis\": true,/*dm*/
\n        \"cellEllipsis\": true,/*dm*/
\n        \"textAlign\":\"left\",/*dm*/
\n        \"showDetail\": true,/*dm*/
\n        \"showDel\": true,/*dm*/
\n        \"showEdit\": true,/*dm*/
\n        \"columnShowIcon\": false,/*dm*/
\n        \"columnShowText\": true,/*dm*/
\n        \"resizeColumn\": true/*dm*/
\n      }",
				"description":"setting属性",
				"key":"var_tableSetting"
			},
			{
				"default":"[]",
				"description":"表格展示列",
				"key":"var_columnsShow"
			},
			{
				"default":"{/*dm*/
\n        \"cacheData\": {},/*dm*/
\n        \"data\":{},/*dm*/
\n        \"searchData\": {},/*dm*/
\n        \"conditionData\": {},/*dm*/
\n      }",
				"description":"查询方式",
				"key":"var_searchType"
			},
			{
				"default":"{ \"id\": \"var_tableColumnWidth25145\",\r/*dm*/
\n  \"resize\": false, \"widths\": {\r/*dm*/
\n    \r/*dm*/
\n  }\r/*dm*/
\n}",
				"key":"var_tableColumnWidth"
			},
			{
				"default":"false",
				"description":"导入弹窗状态",
				"key":"var_importDialogVisible"
			},
			{
				"default":[],
				"description":"导入文件列表",
				"key":"var_fileListUpload"
			},
			{
				"default":false,
				"description":"上传失败弹窗状态",
				"key":"var_importErrorDialogVisible"
			},
			{
				"default":[],
				"description":"上传失败数据列表",
				"key":"var_importErrorData"
			},
			{
				"default":1,
				"description":"上传失败数据列表页码",
				"key":"var_importErrorDataPage"
			},
			{
				"default":"",
				"description":"上传状态",
				"key":"var_fileRet"
			},
			{
				"default":"[\r/*dm*/
\n{\r/*dm*/
\n\"fieldName\": \"update_time\",\r/*dm*/
\n\"desc\": true\r/*dm*/
\n  }\r/*dm*/
\n\r/*dm*/
\n]",
				"description":"高级搜索querySorts参数",
				"key":"var_querySorts"
			},
			{
				"default":"[]",
				"description":"列表展示视图字段数据",
				"key":"var_tablePlanFields"
			},
			{
				"default":"false",
				"description":"草稿箱弹窗",
				"key":"var_draftDrawer"
			},
			{
				"default":"{/*dm*/
\n  \"tableWidth\": \"\",/*dm*/
\n  \"freeze\": {}/*dm*/
\n}",
				"description":"列表的其他属性",
				"key":"var_tablePlanOtherInfo"
			},
			{
				"default":"",
				"description":"草稿箱数据",
				"key":"var_draftData"
			},
			{
				"default":"true",
				"key":"var_switchFlag"
			},
			{
				"default":"{\r/*dm*/
\n  \"conditionType\": \"\",\r/*dm*/
\n  \"conditionsJson\": '',\r/*dm*/
\n  \"defaultPlan\": \"\",\r/*dm*/
\n  \"modelName\": \"\",\r/*dm*/
\n  \"name\": \"\",\r/*dm*/
\n  \"planCode\": \"\",\r/*dm*/
\n  \"planSort\": \"\",\r/*dm*/
\n  \"systemPlan\": \"\",\r/*dm*/
\n  \"conditionsValue\": []\r/*dm*/
\n}",
				"global":false,
				"key":"var_currentPlan"
			},
			{
				"default":"\"\"",
				"key":"var_currentPlanValue"
			},
			{
				"default":"\"\"",
				"key":"var_searchRuleValue"
			},
			{
				"default":"{\r/*dm*/
\n  \r/*dm*/
\n}",
				"key":"var_tabsSearchData"
			},
			{
				"default":"{\r/*dm*/
\n  \"showPageTab\": true,\r/*dm*/
\n  \"showPlan\": true,\r/*dm*/
\n  \"showSearch\": true,\r/*dm*/
\n  \"showFlodButton\": true,\r/*dm*/
\n  \"showKeywordSearch\": true,\r/*dm*/
\n  \"mode\": \"simple\",\r/*dm*/
\n  \"searchMethod\": \"search\"\r/*dm*/
\n}",
				"description":"过滤器组件相关配置",
				"key":"var_settingData"
			},
			{
				"default":"{\r/*dm*/
\n  \"dateTime\": 180,\r/*dm*/
\n  \"date\": 120,\r/*dm*/
\n  \"user\": 110,\r/*dm*/
\n  \"multipleUser\": 300,\r/*dm*/
\n  \"text\": 120,\r/*dm*/
\n  \"number\": 120,\r/*dm*/
\n  \"phone\": 120,\r/*dm*/
\n  \"findRelated\": 120,\r/*dm*/
\n  \"findRelatedMulti\": 120,\r/*dm*/
\n  \"email\": 120,\r/*dm*/
\n  \"checkBox\": 120,\r/*dm*/
\n  \"select\": 120,\r/*dm*/
\n  \"multipleSelect\": 120,\r/*dm*/
\n  \"url\": 120,\r/*dm*/
\n  \"autoNumber\": 120,\r/*dm*/
\n  \"formula\": 120,\r/*dm*/
\n  \"summary\": 120,\r/*dm*/
\n  \"string\": 120\r/*dm*/
\n}",
				"global":false,
				"key":"var_tableBusinessTypeWidth"
			},
			{
				"default":"{}",
				"description":"权限数据",
				"key":"permissionData"
			},
			{
				"default":"{}",
				"key":"var_findQuery"
			},
			{
				"default":"{}",
				"key":"var_linkFormInfo"
			},
			{
				"default":"",
				"description":"树按钮",
				"key":"var_treeButton"
			},
			{
				"default":"{}",
				"description":"默认树",
				"global":false,
				"key":"var_defaulTree"
			},
			{
				"default":"",
				"description":"默认tag页",
				"key":"var_findRelatedTag"
			},
			{
				"default":"",
				"description":"默认tree展开",
				"key":"var_defaultExpanded"
			},
			{
				"default":"{}",
				"description":"页面权限",
				"key":"var_pageRole"
			},
			{
				"default":"[/*dm*/
\n  {/*dm*/
\n    \"label\": \"基本信息\",/*dm*/
\n    \"name\": \"base_info\",/*dm*/
\n    \"pageId\": \"804381450372812802\",/*dm*/
\n    \"url\": \"/api/v1/page/byKey/804381450372812802\",/*dm*/
\n    \"fieldName\": \"\"/*dm*/
\n  }/*dm*/
\n]",
				"global":false,
				"key":"var_linkTabs"
			},
			{
				"default":"\"base_info\"",
				"global":false,
				"key":"var_activeDetailTab"
			},
			{
				"default":"{\"formPath\":\"/ipms_office_organization/form1\",\"detailPath\":\"/ipms_office_organization/detail1\",\"enableInfo\":false,\"linkTabs\":[{\"label\":\"用户\",\"name\":\"ipms_user_office_organization\",\"url\":\"/ipms_user/list\",\"fieldName\":\"office_organization\",\"modelName\":\"ipms_user\"}],\"enableAddRootNode\":true,\"enableAddChildNode\":true,\"enableEdit\":true,\"enableDetele\":true,\"listPath\":\"/ipms_office_organization/list1\",\"modelName\":\"ipms_office_organization\",\"pageKey\":\"840357318922498049\",\"pagePath\":\"/ipms_office_organization/list1\",\"forceUpdate\":true}",
				"global":false,
				"key":"var_setting"
			}
		],
		"action":{
			"lifecycle":{
				"mounted":{
					"arguments":[],
					"actions":[
						{
							"handler":"${function handler(){/*dm*/
\n            await $.shareData.api_getPageRoleAction({ pageKey: $.shareData.var_setting.pageKey })/*dm*/
\n            $.shareData.var_pageRoleAction($.shareData.api_getPageRole)/*dm*/
\n      }}",
							"type":"function"
						},
						{
							"handler":"${function Fun13122(){/*dm*/
\nif ($.bom.dev?.get?.('activeDetailTab')) {/*dm*/
\n  $.shareData.var_activeDetailTabAction($.bom.dev?.get?.('activeDetailTab'))/*dm*/
\n}/*dm*/
\n/*dm*/
\nconst res = await $.global.functions.getModelDictionaryInfo($.shareData?.var_setting?.modelName);/*dm*/
\n/*dm*/
\n$.bom.getHostData(\"setTitle\", res.modelText + \"-列表\");/*dm*/
\n// 流程配置按钮开关/*dm*/
\n$.shareData.var_setting.wfOpen = res.wfOpen || false;/*dm*/
\n/*dm*/
\n$.shareData.var_setting.modelText = res.modelText;/*dm*/
\n/*dm*/
\n// 默认tag页/*dm*/
\n$.shareData.var_findRelatedTagAction(\"tree_model_detail\");/*dm*/
\n/*dm*/
\nif ($.shareData.var_setting.linkTabs.length > 0 && !$.shareData.var_setting.enableInfo) {/*dm*/
\n  $.shareData.var_activeDetailTabAction($.shareData.var_setting.linkTabs[0].name)/*dm*/
\n}/*dm*/
\n/*dm*/
\nif ($.functions.fun_isInclude()) {/*dm*/
\n  return;/*dm*/
\n}/*dm*/
\n// 树列表/*dm*/
\n$.functions.fun_getTreeList();}}",
							"type":"function"
						}
					]
				}
			},
			"on":{
				"treeQuery":{
					"arguments":[
						"queryData",
						"formInfo"
					],
					"actions":[
						{
							"handler":"${function Handler(queryData, formInfo){// 配置查询参数/*dm*/
\nconsole.log(11111, 'treetree')/*dm*/
\n$.shareData.var_findQuery = queryData;/*dm*/
\n$.shareData.var_linkFormInfo = formInfo;/*dm*/
\n$.functions.fun_getTreeList();}}",
							"type":"function"
						}
					]
				}
			}
		},
		"id":"840357318922498049",
		"styleCode":".container {/*dm*/
\n  height: 100%;/*dm*/
\n}/*dm*/
\n.recordsPage {/*dm*/
\n  padding: 0 24px;/*dm*/
\n  position:relative;/*dm*/
\n  height: 100%;/*dm*/
\n  box-sizing: border-box;/*dm*/
\n}/*dm*/
\n.treeBoxRight .recordsPageDialog {/*dm*/
\n  padding: 0px;/*dm*/
\n  position: relative;/*dm*/
\n  height: 100%;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.treeBoxRight .recordsPageDialog .records-joy-table-body {/*dm*/
\nheight: calc(100% - 42px);/*dm*/
\n}/*dm*/
\n/*dm*/
\n/*dm*/
\n.modelTree .el-tree-node__content {/*dm*/
\n  width: auto !important/*dm*/
\n}/*dm*/
\n/*dm*/
\n/*dm*/
\n.modelTree .joy-tree-box .el-tree-node__content:hover {/*dm*/
\n  background-color: #f7f8f9;/*dm*/
\n  border-radius: 6px;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.modelTree .joy-tree-box .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {/*dm*/
\n  background-color: #f0f2f5;/*dm*/
\n  border-radius: 6px;/*dm*/
\n  height: 38px;/*dm*/
\n  margin: 1px 0px;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.modelTree .custom-tree-node {/*dm*/
\n  overflow: hidden/*dm*/
\n}/*dm*/
\n/*dm*/
\n.modelTree .joy-tree-box {/*dm*/
\n  overflow-y: auto;/*dm*/
\n  height: calc(100% - 88px);/*dm*/
\n}/*dm*/
\n.treeBoxRight .el-tabs__header {/*dm*/
\n  margin: 0px;/*dm*/
\n}/*dm*/
\n.recordsPage .records-joy-table {/*dm*/
\n  height: 100%;/*dm*/
\n}/*dm*/
\n.recordsPage .records-joy-table>div:last-child {/*dm*/
\n  height: calc(100% - 92px);/*dm*/
\n  min-height: 0px;/*dm*/
\n}/*dm*/
\n.treeBoxRight {/*dm*/
\n  padding: 16px 24px 0px 24px;/*dm*/
\n  box-sizing: border-box;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.treeBoxRight .detailPageRoot{/*dm*/
\n  padding: 0;/*dm*/
\n}/*dm*/
\n.treeBoxRight .detailPageRoot .detailPageTop {/*dm*/
\n  display: none;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.detail-tabs > .el-tabs__content{/*dm*/
\n  height: calc(100% - 56px);/*dm*/
\n  overflow: auto;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.treeIcon:hover {/*dm*/
\n  background-color: rgba(23,32,69,0.08) !important;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.modelPopover {/*dm*/
\n  min-width: 0px !important;/*dm*/
\n  min-height: 0px !important;/*dm*/
\n  padding: 8px !important;/*dm*/
\n  height: 60px;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.el-popover .treeButton:hover {/*dm*/
\n  background-color: #F1F2F5;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.el-popover .del:hover {/*dm*/
\n  background-color: #F1F2F5;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.el-popper {/*dm*/
\n  margin-left: 10px/*dm*/
\n}/*dm*/
\n/*dm*/
\n.modelTree .treeIcon {/*dm*/
\n  color: #909399 !important/*dm*/
\n}/*dm*/
\n/*dm*/
\n.modelTree .treeIcon:hover {/*dm*/
\n  background: #DCDFE6;/*dm*/
\n  color: #909399 !important/*dm*/
\n}/*dm*/
\n/*dm*/
\n.modelTree .treeIcon:hover i{/*dm*/
\n  color: #909399 !important/*dm*/
\n}/*dm*/
\n/*dm*/
\n.modelTree .el-tree-node__content:hover .modelTreeDiv {/*dm*/
\n  display: inline-block !important/*dm*/
\n}/*dm*/
\n/*dm*/
\n.modelPopover {/*dm*/
\n  min-width: 0px !important;/*dm*/
\n  min-height: 0px !important;/*dm*/
\n  height: auto;/*dm*/
\n  border-radius: 6px !important;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.modelPopover .treeButton {/*dm*/
\n  display: block;/*dm*/
\n  color: #606266 !important;/*dm*/
\n  margin-left: 0px !important;/*dm*/
\n  text-align: center;/*dm*/
\n  border-radius: 6px;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.modelTree .iconModel {/*dm*/
\n  padding: 0/*dm*/
\n}/*dm*/
\n/*dm*/
\n.modelTree .el-tree-node__content {/*dm*/
\n  width: auto !important/*dm*/
\n}/*dm*/
\n/*dm*/
\n.modelTree .custom-tree-node {/*dm*/
\n  overflow: hidden/*dm*/
\n}/*dm*/
\n/*dm*/
\n.modelPopover .edit {/*dm*/
\n  margin: 0 !important;/*dm*/
\n  color: #606266 !important;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.modelPopover .del {/*dm*/
\n  width: 100% !important;/*dm*/
\n  text-align: left;/*dm*/
\n  padding-left: 10px;/*dm*/
\n  margin: 0px !important;/*dm*/
\n}/*dm*/
\n.includeDialog .el-dialog__body {/*dm*/
\n  height: 460px;/*dm*/
\n  max-height: 460px;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.includeDialog .editPage {/*dm*/
\n  padding: 0;/*dm*/
\n}/*dm*/
\n/*dm*/
\n/* .includeDialog .editPage .editPageFooter {/*dm*/
\n  border-top: none;/*dm*/
\n  padding: 0 !important;/*dm*/
\n} *//*dm*/
\n/*dm*/
\n.treeIcon .more-button-heard .more-button-heard-item.el-dropdown {/*dm*/
\n  margin-left: 0px !important;/*dm*/
\n}/*dm*/
\n.treeIcon .el-button--text {/*dm*/
\n  color: #909399 !important;/*dm*/
\n}/*dm*/
\n.custom_upload > div{width: 100%;}/*dm*/
\n.custom_upload .el-upload-dragger{/*dm*/
\n  height: 126px;/*dm*/
\n  width: 100%;/*dm*/
\n  border: 1px rgb(72, 113, 228) dashed;/*dm*/
\n  border-radius: 3px;/*dm*/
\n  display: flex;/*dm*/
\n  align-items: center;/*dm*/
\n  justify-content: center;/*dm*/
\n  flex-direction: column;/*dm*/
\n}/*dm*/
\n/*dm*/
\n/*dm*/
\n.el-tree-node__content:hover .custom-tree-hover .ellipsis{/*dm*/
\n    width: calc(100% - 66px) !important;/*dm*/
\n}",
		"apiConfigs":[
			{
				"transform":"function filter_8YYSanTF(data) {/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果/*dm*/
\n return data.data.reduce((pre, cur) => {/*dm*/
\n    return {/*dm*/
\n  ...pre,    [cur.elementCode]: cur.permission === 1/*dm*/
\n    }/*dm*/
\n  }, {});/*dm*/
\n}",
				"method":"GET",
				"isShareData":true,
				"name":"api_getPageRole",
				"description":"获取页面元素权限",
				"global":false,
				"url":"/api/v1/permission/page/:pageKey/element"
			},
			{
				"default":"{code:200,msg:'',data:{totalSize:0,records: []}}",
				"transform":"function filter_aeZFJf(data) {return JSON.parse(JSON.stringify(data));}",
				"method":"POST",
				"isShareData":true,
				"name":"api_getList",
				"description":"获取表格数据",
				"url":"/api/v1/model/:modelName/list"
			},
			{
				"default":"{code:200,msg:'',data:''}",
				"transform":"function filter_aeZFJf(data) {return data;}",
				"method":"POST",
				"isShareData":true,
				"name":"api_saveData",
				"description":"新建行数据",
				"url":"/api/v1/model/:modelName"
			},
			{
				"transform":"function filter_aeZFJf(data) {return data.data;}",
				"method":"DELETE",
				"isShareData":true,
				"name":"api_deleteDataTrees",
				"description":"删除行数据",
				"global":false,
				"url":"/api/v2/permission/offiOrg/:offiOrg"
			},
			{
				"transform":"function filter_aeZFJf(data) {return data.data;}",
				"method":"DELETE",
				"isShareData":true,
				"name":"api_deleteData",
				"description":"删除行数据",
				"url":"/api/v1/model/:modelName/:id"
			},
			{
				"transform":"function filter_aeZFJf(data) {return data.data;}",
				"method":"DELETE",
				"isShareData":true,
				"name":"api_deleteInBatch",
				"description":"批量删除数据",
				"url":"/api/v1/model/:modelName"
			},
			{
				"transform":"function filter_aeZFJf(data) {return data.data;}",
				"method":"GET",
				"isShareData":true,
				"name":"api_isApprover",
				"description":"当前用户是否为模型流程审批人",
				"url":"/api/v1/model/flow/:modelName/:id"
			},
			{
				"method":"POST",
				"isShareData":true,
				"name":"api_derivedExcel",
				"description":"批量导出",
				"url":"/api/v1/model/:modelName/export"
			},
			{
				"method":"GET",
				"isShareData":true,
				"name":"api_downloadTemplate",
				"description":"模板下载",
				"url":"/api/v1/model/:modelName/export/downloadTemplate"
			},
			{
				"method":"POST",
				"isShareData":true,
				"name":"api_importTemplate",
				"description":"模板导入",
				"url":"undefined/import?trigger=true"
			},
			{
				"default":"{\r/*dm*/
\n  \"tableWidth\": \"small\",\r/*dm*/
\n  \"freeze\": {\r/*dm*/
\n    \"head\": true,\r/*dm*/
\n    \"action\": true\r/*dm*/
\n  }\r/*dm*/
\n}",
				"transform":"function filter_YEcMz73F(data) {/*dm*/
\n  let planInfo = data/*dm*/
\n  if (data.code == 200) {/*dm*/
\n    planInfo = data.data;/*dm*/
\n  } else {/*dm*/
\n    planInfo = data/*dm*/
\n  }/*dm*/
\n  return {/*dm*/
\n    ...planInfo,/*dm*/
\n    \"tableWidth\": planInfo.tableWidth || 'small',/*dm*/
\n    \"freeze\": planInfo.freeze || {/*dm*/
\n      \"head\": true,/*dm*/
\n      \"action\": true/*dm*/
\n    },/*dm*/
\n    planInfoDtoList: planInfo?.planInfoDtoList?.map(item => {/*dm*/
\n       return {/*dm*/
\n         ...item,/*dm*/
\n         show: item.show && item.index !== -1,/*dm*/
\n         label: item?.modelFieldEntity?.fieldText || item.label,/*dm*/
\n         relatedDtoList: item.relatedDtoList?.map(item => {/*dm*/
\n           return {/*dm*/
\n             ...item,/*dm*/
\n             name: item.relatedFieldName/*dm*/
\n           }/*dm*/
\n         })/*dm*/
\n       }/*dm*/
\n    }) || []/*dm*/
\n  }/*dm*/
\n}",
				"method":"GET",
				"isShareData":true,
				"name":"api_getTablePlanInfo",
				"description":"列表视图-查询显示视图明细",
				"global":false,
				"url":"/api/v1/modelview/planInfo/:modelName/:planId/:system"
			},
			{
				"transform":"function filter_YEcMz73F(data) {/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果/*dm*/
\n /*dm*/
\n  return data.data;/*dm*/
\n}",
				"method":"GET",
				"isShareData":true,
				"name":"api_tablePlanList",
				"description":"列表视图-查询用户自定义视图列表",
				"url":"/api/v1/modelview/plan/:modelName/:planId"
			},
			{
				"transform":"function filter_YEcMz73F(data) {/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果/*dm*/
\n  return data;/*dm*/
\n}",
				"method":"DELETE",
				"isShareData":true,
				"name":"api_deleteTablePlan",
				"description":"列表视图-删除视图",
				"url":"/api/v1/modelview/planInfoDel/:modelName/:planId"
			},
			{
				"transform":"function filter_YEcMz73F(data) {/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果/*dm*/
\n  return data;/*dm*/
\n}",
				"method":"POST",
				"isShareData":true,
				"name":"api_addTablePlan",
				"description":"列表视图-新增",
				"url":"/api/v1/modelview/planInfoAdd"
			},
			{
				"transform":"function filter_YEcMz73F(data) {/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果/*dm*/
\n  return data;/*dm*/
\n}",
				"method":"PUT",
				"isShareData":true,
				"name":"api_saveTablePlan",
				"description":"列表视图-保存",
				"url":"/api/v1/modelview/planInfoEdit"
			},
			{
				"transform":"function filter_YEcMz73F(data) {/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果/*dm*/
\n  return data;/*dm*/
\n}",
				"method":"PUT",
				"isShareData":true,
				"name":"api_renameTablePlan",
				"description":"列表视图-重命名",
				"url":"/api/v1/modelview/reName"
			},
			{
				"transform":"function filter_8Q8hz8(data) {\r/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果\r/*dm*/
\n  return data.data;\r/*dm*/
\n}",
				"method":"DELETE",
				"isShareData":true,
				"name":"api_deleteDraft",
				"description":"删除一条草稿箱记录",
				"url":"/api/v1/model/common_draft/:id"
			},
			{
				"transform":"function filter_jstRNX(data) {\r/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果\r/*dm*/
\n  return data;\r/*dm*/
\n}",
				"method":"POST",
				"isShareData":true,
				"name":"api_getPreDownload",
				"description":"预下载接口",
				"url":"/api/v1/attachment/preDownload"
			},
			{
				"transform":"",
				"method":"POST",
				"isShareData":true,
				"name":"api_getCommonDraft",
				"description":"草稿箱数据",
				"url":"/api/v1/model/common_draft/list"
			},
			{
				"default":"{\r/*dm*/
\n  \"data\": {\r/*dm*/
\n    \"yeqian\": [],\r/*dm*/
\n    \"yuzhi\": []\r/*dm*/
\n  }\r/*dm*/
\n}",
				"transform":"function filter_KjHTHJyB(data) {\r/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果\r/*dm*/
\n  data.data.yeqian = (data.data.yeqian || []).map(item => {\r/*dm*/
\n    return {\r/*dm*/
\n      ...item,\r/*dm*/
\n      planName: item.name\r/*dm*/
\n    }\r/*dm*/
\n  }).sort((a,b) => a.planSort - b.planSort)\r/*dm*/
\n\r/*dm*/
\n  data.data.yuzhi = (data.data.yuzhi || []).map(item => {\r/*dm*/
\n    const conditionsValue = JSON.parse(item.conditionsJson)[0].standardQueryConditions\r/*dm*/
\n    return {\r/*dm*/
\n      ...item,\r/*dm*/
\n      conditionsValue\r/*dm*/
\n    }\r/*dm*/
\n  })\r/*dm*/
\n  return data\r/*dm*/
\n}",
				"method":"GET",
				"isShareData":true,
				"name":"api_getConditionsPlan",
				"description":"查询条件方案+页签-列表",
				"global":false,
				"url":"/api/v1/model/conditions/planAll/:modelName"
			},
			{
				"transform":"",
				"method":"POST",
				"isShareData":true,
				"name":"api_saveConditionsPlan",
				"description":"查询条件方案-保存或修改",
				"url":"/api/v1/model/conditions/plan/save"
			},
			{
				"transform":"",
				"method":"PUT",
				"isShareData":true,
				"name":"api_setConditionsPlanDefault",
				"description":"查询条件方案-设置默认",
				"url":"/api/v1/model/conditions/plan/default/:modelName/:planCode"
			},
			{
				"transform":"",
				"method":"DELETE",
				"isShareData":true,
				"name":"api_deleteConditionsPlan",
				"description":"查询条件方案-删除方案",
				"url":"/api/v1/model/conditions/plan/:modelName/:planCode"
			},
			{
				"transform":"",
				"method":"PUT",
				"isShareData":true,
				"name":"api_setTablePlanDefault",
				"description":"设为默认",
				"url":"/api/v1/modelview/setDefault/:modelName/:planId/:isSystem"
			},
			{
				"transform":"",
				"method":"PUT",
				"isShareData":true,
				"name":"api_editTree",
				"description":"修改树节点",
				"global":false,
				"url":"/api/v1/:modelName/trees/:id"
			},
			{
				"transform":"",
				"method":"POST",
				"isShareData":true,
				"name":"api_treeOrder",
				"description":"树节点排序",
				"global":false,
				"url":"/api/v2/models/:modelName/tree/_order"
			}
		],
		"attrs":{
			"style":{
				"position":"relative",
				"height":"100%"
			},
			"class":".tree-page",
			"setting":"${JSON.stringify({ \"name\": \"page\" })}"
		}
	},
	"editorSettingDsl":"{\"enableInfo\":true,\"linkTabs\":[{\"label\":\"用户(所属组织机构 )\",\"name\":\"ipms_user_office_organization\",\"url\":\"/ipms_user/list\",\"fieldName\":\"office_organization\",\"modelName\":\"ipms_user\"}],\"enableAddRootNode\":true,\"enableAddChildNode\":true,\"enableEdit\":false,\"enableDetele\":false,\"detailPath\":\"/ipms_office_organization/detail1\",\"formPath\":\"/ipms_office_organization/form1\",\"listPath\":\"/ipms_office_organization/list1\",\"modelName\":\"ipms_office_organization\",\"pageKey\":\"840357318922498049\",\"pagePath\":\"/ipms_office_organization/list1\",\"forceUpdate\":true}",
	"pageInterceptor":"",
	"pageSource":"model",
	"frame":"vue"
}