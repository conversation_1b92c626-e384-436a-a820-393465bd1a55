{
	"usePlatform":"",
	"enableAdvanced":true,
	"editable":true,
	"pageTemplate":"PC标准增删改查模板",
	"edition":"v2",
	"pageKey":"956167632830300161",
	"pageSettingDsl":"{\"detailPath\":\"/prompt_words_config/detail\",\"formPath\":\"/prompt_words_config/form\",\"listPath\":\"/prompt_words_config/list\",\"modelName\":\"prompt_words_config\",\"pageKey\":\"956167632830300161\",\"pagePath\":\"/prompt_words_config/form\"}",
	"pageName":"提示词管理_新建/编辑",
	"pageTemplateCode":"PCDefault",
	"modelName":"prompt_words_config",
	"pageType":"form_v4",
	"pageDsl":{
		"components":[
			{
				"children":[
					{
						"componentType":"custom",
						"children":[
							{
								"libraryName":"@jd/joyui",
								"componentType":"UILibrary",
								"children":[
									{
										"componentType":"custom",
										"children":[
											{
												"componentType":"custom",
												"children":[
													{
														"componentType":"custom",
														"children":[
															{
																"componentType":"custom",
																"componentName":"TextWrapper",
																"id":"TextWrapper-zSGkZzrm",
																"attrs":{
																	"style":{
																		"color":"#909399",
																		"font-weight":"normal",
																		"display":"block",
																		"width":"100%",
																		"font-size":"14px",
																		"line-height":"66px"
																	}
																},
																"props":{
																	"children":" 暂无数据"
																},
																"chosen":false
															}
														],
														"componentName":"DivWrapper",
														"id":"DivWrapper-HEjzmJwa",
														"title":"暂无数据",
														"attrs":{
															"style":{
																"background-color":"#ffffff",
																"width":"100%",
																"height":"100%"
															}
														},
														"props":{
															"name":"DivWrapper",
															"slot":"empty"
														},
														"chosen":false
													},
													{
														"componentType":"custom",
														"children":[
															{
																"componentType":"custom",
																"componentName":"TableColumn",
																"id":"TableColumn-ZGZD3J8j",
																"props":{
																	"show-overflow-tooltip":"",
																	"reserve-selection":true,
																	":selectable":"${function s(scope){ return scope['$extra']?.dataPermission ==2 }}",
																	"width":"48",
																	"fixed":"left",
																	"type":"selection",
																	"align":"center",
																	"v-slot":"scope"
																}
															}
														],
														"componentName":"VIf",
														"id":"VIf-323dK7tJ",
														"props":{
															"conditions":"${[ $.shareData.var_tables[viewCode]?.setting?.selectRowType === 'many' || $.shareData.var_tables[viewCode]?.setting?.batchDelFlag ]}"
														}
													},
													{
														"componentType":"custom",
														"children":[
															{
																"componentType":"custom",
																"componentName":"TableColumn",
																"id":"TableColumn-bwfRCfzE",
																"props":{
																	"resizable":false,
																	"width":"60",
																	"fixed":"left",
																	"label":"序号",
																	"type":"index",
																	"align":"left",
																	"class-name":"color-text-secondary"
																}
															}
														],
														"componentName":"VIf",
														"id":"VIf-YfSGanHh",
														"props":{
															"conditions":"${[$.shareData.var_tables[viewCode]?.setting?.showNo]}"
														}
													},
													{
														"children":[
															{
																"children":[
																	{
																		"children":[
																			{
																				"componentType":"custom",
																				"id":"DivWrapper-mh4dkQDH",
																				"componentName":"DivWrapper",
																				"title":"Div",
																				"attrs":{
																					"style":{}
																				},
																				"props":{
																					"name":"DivWrapper"
																				}
																			}
																		],
																		"componentName":"Radio",
																		"id":"Radio-TNfyB8sJ",
																		"props":{
																			"label":"${scope.row.id}",
																			"v-model":"${$.shareData.var_tables[viewCode]?.singleSelection}"
																		}
																	}
																],
																"componentName":"TableColumn",
																"id":"TableColumn-cHSB2xhD",
																"props":{
																	"width":"${'80px'}",
																	"v-slot":"scope"
																}
															}
														],
														"componentName":"VIf",
														"id":"VIf-jDs8SpZk",
														"props":{
															"conditions":"${[$.shareData.var_tables[viewCode]?.setting?.showRadio]}"
														}
													},
													{
														"componentType":"custom",
														"children":[
															{
																"componentType":"custom",
																"children":[
																	{
																		"libraryName":"@jd/joyui",
																		"componentType":"UILibrary",
																		"action":{
																			"fire":{
																				"navtoDetail":{
																					"arguments":[
																						"field",
																						"modelName"
																					],
																					"actions":[
																						{
																							"handler":"${function Fun27696(field, modelName){if (true) {/*dm*/
\n  // console.error('子模型新建或编辑时， 不支持跳转啊');/*dm*/
\n  return;/*dm*/
\n}}}",
																							"type":"function"
																						}
																					]
																				},
																				"handleDownload":{
																					"arguments":[
																						"file"
																					],
																					"actions":[
																						{
																							"handler":"${function handler(file){/*dm*/
\nawait $.global.shareData.preDownloadAction({ originFileName: file.originFileName })/*dm*/
\n/*dm*/
\nfetch($.global.shareData.preDownload.presignedObjectUrl, {/*dm*/
\n  method: 'GET',/*dm*/
\n  responseType: 'blob',/*dm*/
\n}).then(/*dm*/
\n  res => {/*dm*/
\n    return res.blob();/*dm*/
\n  })/*dm*/
\n  .then(blob => {/*dm*/
\n/*dm*/
\n    let bl = new Blob([blob]);/*dm*/
\n/*dm*/
\n    var link = document.createElement('a');/*dm*/
\n/*dm*/
\n    link.href = window.URL.createObjectURL(bl);/*dm*/
\n/*dm*/
\n    link.download = file.name;/*dm*/
\n/*dm*/
\n    link.click();/*dm*/
\n/*dm*/
\n    window.URL.revokeObjectURL(link.href);/*dm*/
\n/*dm*/
\n  }).catch(err => {/*dm*/
\n    console.log('error', error)/*dm*/
\n  });/*dm*/
\n}}",
																							"type":"function"
																						}
																					]
																				}
																			}
																		},
																		"componentName":"ModelField",
																		"id":"ModelField-JSkiM4zA",
																		"title":"模型字段",
																		"version":"1.8.0",
																		"props":{
																			"originProps":{},
																			"formType":"table",
																			"elementProps":"${() => {/*dm*/
\n  const handleEdit = (item) => {/*dm*/
\n    const searchParams = new URLSearchParams();/*dm*/
\n    searchParams.set('mode', 'edit');/*dm*/
\n    searchParams.set('url', `https://joybuilder.s3.cn-north-1.jdcloud-oss.com/${item.originFileName}`)/*dm*/
\n    searchParams.set('filename', item.name);/*dm*/
\n    $.bom.route('open', `/previewFile?${searchParams.toString()}`)/*dm*/
\n/*dm*/
\n  }/*dm*/
\n  // 附件开启编辑/*dm*/
\n  if (item.modelField.businessType === 'attachment') {/*dm*/
\n    return {/*dm*/
\n      enableEdit: true,/*dm*/
\n      handleEdit/*dm*/
\n    }/*dm*/
\n/*dm*/
\n  }/*dm*/
\n/*dm*/
\n  return {};/*dm*/
\n}}",
																			"fieldProps":"${{/*dm*/
\n  ...(item.modelField || {}),/*dm*/
\n    jumpOrNot: false/*dm*/
\n}}",
																			"businessType":"${item.modelField.businessType}",
																			"value":"${scope.row[item.name]}"
																		}
																	}
																],
																"componentName":"TableColumn",
																"id":"TableColumn-eP67ABHC",
																"props":{
																	"show-overflow-tooltip":"${$.shareData.var_tables[viewCode]?.setting?.cellEllipsis}",
																	"prop":"${item.name}",
																	"fixed":false,
																	"label":"${$.shareData.var_setting.table?.[viewCode]?.fieldInfo?.[item.name]?.fieldName || item.label}",
																	"sortable":false,
																	"align":"${$.shareData.var_tables[viewCode]?.setting?.textAlign}",
																	"v-slot":"scope",
																	"min-width":"${() => {\r/*dm*/
\n  if (item?.modelField.businessType === 'dateTime') {\r/*dm*/
\n    return item?.displayWidth || 180\r/*dm*/
\n  }\r/*dm*/
\n  return item?.displayWidth || 140\r/*dm*/
\n}}"
																},
																"attrs":{
																	"setting":"${JSON.stringify({/*dm*/
\n  name: 'field',/*dm*/
\n  editorViewCode: viewCode,/*dm*/
\n  type: 'table',/*dm*/
\n  fieldInfo: {/*dm*/
\n    fieldText: item.label,/*dm*/
\n    fieldName: item.name,/*dm*/
\n    modelName: item.modelField?.relatedModel?.modelName || item.modelField.modelName,/*dm*/
\n    businessType: item.modelField.businessType/*dm*/
\n  }/*dm*/
\n})}"
																}
															}
														],
														"componentName":"VFor",
														"id":"VFor-8cADRYzk",
														"title":"table列循环容器",
														"props":{
															"forEach":"${$.shareData.var_tables[viewCode]?.columns}",
															"forEachKey":"item",
															"index":"$index"
														}
													},
													{
														"componentType":"custom",
														"children":[
															{
																"componentType":"custom",
																"children":[
																	{
																		"libraryName":"@jd/joyui",
																		"componentType":"UILibrary",
																		"action":{
																			"fire":{
																				"tab-click":{
																					"arguments":[
																						"tab"
																					],
																					"actions":[
																						{
																							"handler":"${function handler(tab){const detailId = scope.row?.id;/*dm*/
\nconst setting = $.shareData.var_setting.table?.[viewCode];/*dm*/
\nconst addPage = setting?.editUrl || $.functions.fun_getOperatePath(modelName, 'form');/*dm*/
\nconst detailPage = setting?.detailUrl || $.functions.fun_getOperatePath(modelName, 'detail')/*dm*/
\nconst tableKey = viewCode;/*dm*/
\nconst subModel = {/*dm*/
\n  modelName,/*dm*/
\n  modelText,/*dm*/
\n  fieldName,/*dm*/
\n};/*dm*/
\nif (tab == 'detail' && detailId) {/*dm*/
\n  $.bom.page?.open(detailPage, {/*dm*/
\n    detailId,/*dm*/
\n    pageType: 'subModel',/*dm*/
\n    subModel/*dm*/
\n  }, async (data) => {/*dm*/
\n  }, {/*dm*/
\n    options: {/*dm*/
\n      width: '960px',/*dm*/
\n      title: '详情',/*dm*/
\n      closeOnClickModal: false,/*dm*/
\n      customClass: 'sub-model-dialog',/*dm*/
\n    },/*dm*/
\n    type: 'dialog'/*dm*/
\n  });/*dm*/
\n}/*dm*/
\nif (tab == 'edit') {/*dm*/
\n  if (detailId) {/*dm*/
\n    $.bom.page?.open(addPage, {/*dm*/
\n      detailId,/*dm*/
\n      pageType: 'subModel',/*dm*/
\n      subModel,/*dm*/
\n      formData: scope.row/*dm*/
\n    }, async (data) => {/*dm*/
\n      if (!data) return;/*dm*/
\n      // 编辑/*dm*/
\n      const oldRow = scope.row;/*dm*/
\n      const rowIndex = scope.$index;/*dm*/
\n      let transform = await $.global.functions.modelValueFormat(modelName)/*dm*/
\n/*dm*/
\n      /*dm*/
\n      // 显示列表/*dm*/
\n      this.$set($.shareData.var_tables[viewCode]?.tableList, rowIndex, {/*dm*/
\n        ...oldRow,/*dm*/
\n        ...data/*dm*/
\n      });/*dm*/
\n      if (data.id.includes($.shareData.var_consts.subModelIDPrefix)) {/*dm*/
\n        const [name, addIndex] = data.id.split('_');/*dm*/
\n        const newRow = {/*dm*/
\n          ...oldRow,/*dm*/
\n          ...data/*dm*/
\n        }/*dm*/
\n        delete newRow.id;/*dm*/
\n        $.shareData.var_tables[viewCode].tableData.addDatas[addIndex] = transform.output(newRow);/*dm*/
\n      } else {/*dm*/
\n        // 单独记录修改行/*dm*/
\n        const updateRowKey = $.shareData.var_tables[viewCode]?.tableData?.updateDatas.findIndex(i => i.id == data.id);/*dm*/
\n        if (updateRowKey == -1) {/*dm*/
\n          $.shareData.var_tables[viewCode]?.tableData?.updateDatas.push(transform.output(data));/*dm*/
\n        } else {/*dm*/
\n          $.shareData.var_tables[viewCode].tableData.updateDatas[updateRowKey] = transform.output(data);/*dm*/
\n        }/*dm*/
\n      }/*dm*/
\n    }, {/*dm*/
\n      options: {/*dm*/
\n        width: '960px',/*dm*/
\n        title: '编辑',/*dm*/
\n        closeOnClickModal: false,/*dm*/
\n        customClass: 'sub-model-dialog',/*dm*/
\n      },/*dm*/
\n      type: 'dialog'/*dm*/
\n    });/*dm*/
\n  };/*dm*/
\n}/*dm*/
\nif (tab == 'delete') {/*dm*/
\n  this.$confirm('此操作将永久删除, 是否继续?', '提示', {/*dm*/
\n    confirmButtonText: '确定',/*dm*/
\n    cancelButtonText: '取消',/*dm*/
\n    type: 'warning'/*dm*/
\n  }).then(() => {/*dm*/
\n    // 删除本地缓存数据/*dm*/
\n    const rowKey = scope.$index;/*dm*/
\n/*dm*/
\n    $.shareData.var_tables[viewCode]?.tableList.splice(rowKey, 1);/*dm*/
\n    if (scope.row?.id.includes($.shareData.var_consts.subModelIDPrefix)) {/*dm*/
\n      const [, addRowKey] = scope.row?.id.split('_');/*dm*/
\n      $.shareData.var_tables[viewCode]?.tableData?.addDatas.splice(addRowKey, 1);/*dm*/
\n    } else {/*dm*/
\n      // 添加子模型表单数据/*dm*/
\n      $.shareData.var_tables[viewCode]?.tableData?.deleteIds.push(detailId)/*dm*/
\n    }/*dm*/
\n/*dm*/
\n  });/*dm*/
\n}/*dm*/
\n}}",
																							"type":"function"
																						}
																					]
																				}
																			}
																		},
																		"componentName":"MoreButton",
																		"id":"MoreButton-D6C8pMnr",
																		"version":"1.8.0",
																		"props":{
																			"styleState":"${[]}",
																			"padding":"0px 16px 0px 0px",
																			"data":"${() => {\r/*dm*/
\n  let list = [];\r/*dm*/
\n  const setting = $.shareData.var_setting.table;\r/*dm*/
\n  let isEdit = setting['default'].edit; // false ? $.shareData.permissionData?.edit : true;\r/*dm*/
\n  // let detail =   false ? $.shareData.permissionData?.detail : true;\r/*dm*/
\n  let isDelete = setting['default'].delete;// false ? $.shareData.permissionData?.delete : true;\r/*dm*/
\n  if (setting[viewCode]){\r/*dm*/
\n    isEdit = setting[viewCode].edit;\r/*dm*/
\n    isDelete = setting[viewCode].delete;\r/*dm*/
\n  }\r/*dm*/
\n  if (isEdit) {\r/*dm*/
\n    list.push({ \"label\": \"编辑\", \"name\": \"edit\", \"type\": \"text\", \"size\": \"small\" })\r/*dm*/
\n  };\r/*dm*/
\n  // if ( detail) {\r/*dm*/
\n  //   list.push({ \"label\": \"详情\", \"name\": \"detail\", \"type\": \"text\", \"size\": \"small\" })\r/*dm*/
\n  // };\r/*dm*/
\n  if ( isDelete) {\r/*dm*/
\n    list.push({ \"label\": \"删除\", \"name\": \"delete\", \"type\": \"text\", \"size\": \"small\" });\r/*dm*/
\n  };\r/*dm*/
\n\r/*dm*/
\n  const arr = setting[viewCode]?.columnCustomButtons || []\r/*dm*/
\n  const customButtons = arr?.filter(v => v.enable)\r/*dm*/
\n  if (customButtons.length) {\r/*dm*/
\n    for (let i = 0; i < customButtons.length; i++) {\r/*dm*/
\n      list.push({ \"label\": customButtons[i].label, \"name\": customButtons[i].label, \"type\": \"text\", \"size\": \"small\" });\r/*dm*/
\n    }\r/*dm*/
\n  }\r/*dm*/
\n  return list;\r/*dm*/
\n}}",
																			"moreRound":"${false}",
																			"max-count":"${3}",
																			"textAlign":"left",
																			"marginLeft":"${0 * 1}"
																		}
																	}
																],
																"componentName":"TableColumn",
																"id":"TableColumn-dk6z8CDy",
																"props":{
																	"prop":"action",
																	"fixed":"right",
																	"label":"操作",
																	"v-slot":"scope",
																	"min-width":"172px"
																}
															}
														],
														"componentName":"VIf",
														"id":"VIf-sB8YrzTG",
														"props":{
															"conditions":"${[$.shareData.var_tables[viewCode]?.setting?.mode==='normal' && enableOperateColumnu]}"
														}
													}
												],
												"action":{
													"fire":{
														"header-dragend":{
															"arguments":[
																"newWidth",
																"oldWidth",
																"column",
																"event"
															],
															"actions":[
																{
																	"handler":"${function handler(newWidth,oldWidth,column,event){/*dm*/
\nconst genId = $.functions.fun_genTableId(viewCode, modelName);/*dm*/
\n/*dm*/
\nconst { property } = column/*dm*/
\n/*dm*/
\n/*dm*/
\nconst { businessType, fieldWidth, minWidth } = $.functions.getFieldsBusinessType(viewCode, property);/*dm*/
\n/*dm*/
\n/*dm*/
\nconst minNewWidth = Math.max(minWidth, newWidth)/*dm*/
\n/*dm*/
\n/*dm*/
\nconst newColumnWidth = {/*dm*/
\n  ...$.shareData.tableColumnWidth.widths,/*dm*/
\n  [property]: minNewWidth/*dm*/
\n}/*dm*/
\n/*dm*/
\n$.shareData.tableColumnWidth.widths = newColumnWidth;/*dm*/
\nconst refName = viewCode;/*dm*/
\n/*dm*/
\n// 修复数据、重置表格宽度/*dm*/
\n/*dm*/
\nthis.$refs[refName].store.states._columns = this.$refs[refName]?.store.states._columns.map(item => {/*dm*/
\n/*dm*/
\n  if (item.id === column.id && item.minWidth && item.width) {/*dm*/
\n/*dm*/
\n    return {/*dm*/
\n      ...item,/*dm*/
\n      realWidth: minNewWidth,/*dm*/
\n      width: minNewWidth/*dm*/
\n    }/*dm*/
\n/*dm*/
\n  }/*dm*/
\n  return item/*dm*/
\n})/*dm*/
\n/*dm*/
\nthis.$refs[refName]?.store?.scheduleLayout(true);/*dm*/
\n/*dm*/
\nthis.$nextTick(() => {/*dm*/
\n  // 更新列的宽度/*dm*/
\n/*dm*/
\n  this.$refs[refName].layout.updateColumnsWidth();/*dm*/
\n/*dm*/
\n})/*dm*/
\n/*dm*/
\nlocalStorage.setItem(genId, JSON.stringify(newColumnWidth))}}",
																	"type":"function"
																}
															]
														},
														"current-change":{
															"arguments":[
																"value"
															],
															"actions":[
																{
																	"handler":"${function Fun84098(val){ if ($.shareData.var_tables[viewCode]?.setting?.selectRowType === 'single') {/*dm*/
\n  $.shareData.var_tablesAction({/*dm*/
\n    [viewCode]: {/*dm*/
\n      singleSelection: value.id/*dm*/
\n    }/*dm*/
\n  })/*dm*/
\n}/*dm*/
\n}}",
																	"type":"function"
																}
															]
														},
														"sort-change":{
															"arguments":[
																"column",
																"prop"
															],
															"actions":[
																{
																	"handler":"${function Fun_47sXNY(column, prop){/*dm*/
\nif ($.shareData.var_tables[viewCode]?.searchQuery?.querySorts.length > 1) {/*dm*/
\n/*dm*/
\n  $.shareData.var_tables[viewCode]?.searchQuery?.querySorts.splice(0, 1);/*dm*/
\n/*dm*/
\n}/*dm*/
\n/*dm*/
\n/*dm*/
\n/*dm*/
\nif (column.order) {/*dm*/
\n/*dm*/
\n  $.shareData.var_tables[viewCode]?.searchQuery?.querySorts.unshift({/*dm*/
\n/*dm*/
\n    fieldName: column.prop,/*dm*/
\n/*dm*/
\n    desc: column.order == \"descending\"/*dm*/
\n/*dm*/
\n  })/*dm*/
\n/*dm*/
\n}/*dm*/
\n/*dm*/
\n/*dm*/
\n/*dm*/
\nawait $.functions.fun_loadTableData(modelName, viewCode);/*dm*/
\n/*dm*/
\n}}",
																	"type":"function"
																}
															]
														},
														"selection-change":{
															"arguments":[
																"selection"
															],
															"actions":[
																{
																	"handler":"${function Fun37467(selection){$.shareData.var_tablesAction({/*dm*/
\n  [viewCode]: {/*dm*/
\n    multipleSelection: selection/*dm*/
\n  }/*dm*/
\n})}}",
																	"type":"function"
																}
															]
														}
													}
												},
												"componentName":"Table",
												"id":"Table-ErX7sPeY",
												"title":"表格区域",
												"attrs":{
													"class":"${$.shareData.var_tables[viewCode]?.setting?.headerEllipsis?'headerEllipsis':''}"
												},
												"props":{
													"border":"${false}",
													"max-height":"280px",
													"headerEllipsis":"${$.shareData.var_tables[viewCode]?.setting?.headerEllipsis}",
													"data":"${$.shareData.var_tables[viewCode]?.tableList}",
													"show-header":"${$.shareData.var_tables[viewCode]?.setting?.showHeader}",
													"ref":"${viewCode}",
													"size":"small",
													"v-loading":"${$.shareData.var_tables[viewCode]?.loading}",
													"tooltip-effect":"dark",
													"tree-props":"${{ hasChildren: \"hasChildren\", children: \"children\" }}",
													"stripe":"${$.shareData.var_tables[viewCode]?.setting?.stripe}",
													"style":"width: 100%",
													"height":"${$.shareData.var_tables[viewCode]?.tableList?.length ? ''  : tableStyle?.height}",
													"row-key":"id",
													"highlight-current-row":"${$.shareData.var_tables[viewCode]?.setting?.selectRowType === 'single'}"
												}
											}
										],
										"componentName":"DivWrapper",
										"id":"DivWrapper-H2erfa8Y",
										"title":"表格+操作按钮",
										"props":{
											"name":"DivWrapper"
										},
										"attrs":{
											"class":"records-joy-table-body"
										}
									}
								],
								"action":{
									"fire":{
										"handleSizeChange":{
											"arguments":[
												"value"
											],
											"actions":[
												{
													"handler":"${function Fun63971(val){$.shareData.var_tables[viewCode].params.pageSize = value;/*dm*/
\nlocalStorage.setItem(`var_basePageParams_${viewCode}`, value)/*dm*/
\n$.functions.fun_loadTableData(modelName, viewCode)/*dm*/
\n}}",
													"type":"function"
												}
											]
										},
										"handleCurrentChange":{
											"arguments":[
												"value"
											],
											"actions":[
												{
													"handler":"${function Fun29265(value){$.shareData.var_tables[viewCode].params.pageNum = value;/*dm*/
\n$.functions.fun_loadTableData(modelName, viewCode);/*dm*/
\n}}",
													"type":"function"
												}
											]
										}
									}
								},
								"componentName":"ModelTable",
								"id":"ModelTable-rKxQ5XmQ",
								"title":"列表组件",
								"version":"1.8.0",
								"props":{
									"pageSizes":"${$.shareData.var_tables[viewCode]?.setting?.pageSizes}",
									"editPage":"undefined",
									"buttonShowIcon":false,
									"showPagination":"${$.shareData.var_tables[viewCode]?.setting?.showPagination}",
									"showRefresh":false,
									"batchDelFlag":false,
									"buttonShowText":true,
									"addFlag":true,
									"mode":"${$.shareData.var_tables[viewCode]?.setting?.mode}",
									"columnShowIcon":false,
									"modelName":"modelName",
									"exportDataFlag":false,
									"ref":"${viewCode}",
									"showPlanView":true,
									"pageParams":"${$.shareData.var_tables[viewCode]?.params}",
									"addPage":"undefined",
									"paginationMode":"${$.shareData.var_tables[viewCode]?.setting?.paginationMode}",
									"detailPage":"undefined",
									"model":"multiple",
									"draftFlag":false,
									"multipleSelection":"${$.shareData.var_tables[viewCode]?.multipleSelection}"
								},
								"attrs":{
									"style":{
										"height":"100%"
									},
									"class":"tableScolle sub-model-table"
								}
							}
						],
						"componentName":"DivWrapper",
						"id":"DivWrapper-xt4RGZ3B"
					}
				],
				"componentName":"Export",
				"id":"Export-QtxKchrM",
				"attrs":{
					"name":"table",
					"style":{},
					"run":false
				},
				"props":{
					"modelName":"",
					"modelText":"${}",
					"fieldName":"${}",
					"enableOperateColumnu":"${}",
					"tableStyle":"${{'height': '106px'}}",
					"viewCode":""
				}
			},
			{
				"componentType":"custom",
				"children":[
					{
						"componentType":"custom",
						"children":[
							{
								"componentType":"custom",
								"children":[
									{
										"componentType":"custom",
										"children":[
											{
												"libraryName":"@jd/joyui",
												"componentType":"UILibrary",
												"__setting":{},
												"children":[
													{
														"libraryName":"@jd/joyui",
														"componentType":"UILibrary",
														"action":{
															"fire":{
																"handleReturn":{
																	"arguments":[
																		""
																	],
																	"actions":[
																		{
																			"handler":"${function handler(){if (history.state) {/*dm*/
\n  $.bom.page.close() || $.bom.route('go', -1);/*dm*/
\n} else {/*dm*/
\n  $.bom.route('push', { path: '/' })/*dm*/
\n}}}",
																			"type":"function"
																		}
																	]
																}
															}
														},
														"componentName":"TitleButtonBreadCrumb",
														"id":"TitleButtonBreadCrumb-iAXKdWpi",
														"title":"按钮面包屑",
														"version":"1.8.0",
														"props":{
															"modelName":"${() => { if($.shareData.var_pageInfo.isEdit) { return `${$.shareData.var_pageInfo.modelText}: ${$.shareData.var_saveForm.name || ''}` } else { return $.shareData.var_pageInfo.title } }}",
															"btnText":"",
															"size":"medium",
															"round":"${false}",
															"plain":"${false}",
															"nativeType":"button",
															"icon":"joy-icon-left",
															"disabled":"${false}",
															"loading":"${false}",
															"autofocus":"${false}",
															"btnType":"text",
															"circle":"${false}"
														}
													}
												],
												"componentName":"TitlePage",
												"id":"TitlePage-Ds2ZSzjt",
												"title":"标题",
												"version":"1.8.0",
												"props":{}
											}
										],
										"componentName":"VIf",
										"id":"VIf-kTtGNmsw",
										"props":{
											"conditions":"${[$.shareData.var_pageInfo.showHeader && $.shareData.var_setting.enableTitle]}"
										}
									}
								],
								"componentName":"DivWrapper",
								"id":"DivWrapper-WHBandCh",
								"title":"Header"
							},
							{
								"libraryName":"@jd/joyui",
								"componentType":"UILibrary",
								"children":[
									{
										"componentType":"custom",
										"children":[
											{
												"libraryName":"@jd/joyui",
												"componentType":"UILibrary",
												"children":[
													{
														"componentType":"custom",
														"children":[
															{
																"componentType":"custom",
																"children":[
																	{
																		"children":[
																			{
																				"children":[
																					{
																						"componentType":"custom",
																						"children":[
																							{
																								"libraryName":"@jd/joyui",
																								"componentType":"UILibrary",
																								"action":{
																									"fire":{
																										"change":{
																											"arguments":[
																												"value"
																											],
																											"actions":[
																												{
																													"handler":"${function Fun_PxhJyx(value){this.$set($.shareData.var_saveForm, item.fieldName, value);/*dm*/
\n}}",
																													"type":"function"
																												}
																											]
																										},
																										"findRelatedOpen":{
																											"arguments":[
																												"field"
																											],
																											"actions":[
																												{
																													"handler":"${function handler(){const { modelName, modelText } = field.relatedModel;/*dm*/
\nconst { fieldName, businessType, queryConditions   } = field;/*dm*/
\n/*dm*/
\n// const viewCode = field.viewCode; /*dm*/
\n// const queryConditions = JSON.parse(item.field.queryConditions) || [];/*dm*/
\n/*dm*/
\n// $.shareData.var_tablesAction({/*dm*/
\n//   [viewCode]: {/*dm*/
\n//     singleSelection: $.shareData.var_saveForm[fieldName]?.id,/*dm*/
\n//     searchQuery: {/*dm*/
\n//       queryConditions,/*dm*/
\n//       combinationRule: item.field.combinationRule/*dm*/
\n//     }/*dm*/
\n//   }/*dm*/
\n// })/*dm*/
\n/*dm*/
\n// $.functions.fun_loadTableData(modelName, viewCode)/*dm*/
\n$.shareData.var_findRelationInfoAction({/*dm*/
\n  modelName,/*dm*/
\n  modelText,/*dm*/
\n  fieldName,/*dm*/
\n  businessType,/*dm*/
\n  queryConditions,/*dm*/
\n  // viewCode,/*dm*/
\n  visible: true/*dm*/
\n})/*dm*/
\n// console.log(JSON.stringify($.shareData.var_findRelationInfo, null, 2), JSON.stringify($.shareData.var_tables[viewCode], null, 2),'xxxxx');}}",
																													"type":"function"
																												}
																											]
																										}
																									}
																								},
																								"componentName":"ModelField",
																								"id":"ModelField-MamC5r7e",
																								"title":"ModelField表单子项",
																								"version":"1.8.0",
																								"props":{
																									"originProps":"${{/*dm*/
\n  column: $.shareData.var_setting?.group?.[items.groupCode]?.columnTotal || $.shareData.var_setting.columnTotal/*dm*/
\n}}",
																									"formType":"save",
																									"fieldName":"${item.field.fieldName}",
																									"elementProps":"${() => {/*dm*/
\n  let disabled = false;/*dm*/
\n  // 如果权限里面设置了编辑权限，在编辑的时候disabled/*dm*/
\n  if ($.shareData.var_pageInfo.isEdit) {/*dm*/
\n    if (item.modelFieldPermission !== 2) {/*dm*/
\n      disabled = true;/*dm*/
\n    }/*dm*/
\n  } else {/*dm*/
\n    disabled = item.field.fieldName == $.shareData.var_subModelInfo.fieldName || $.shareData.var_disabledFormField.includes(item.field.fieldName)/*dm*/
\n  }/*dm*/
\n  const { officeConfig } = $.bom.getHostData('appInfo') || {};/*dm*/
\n  const handleEdit = (item) => {/*dm*/
\n    $.global.functions.handleOfficeFile(item, 'edit')/*dm*/
\n/*dm*/
\n  }/*dm*/
\n  // 附件开启编辑/*dm*/
\n  if (item.field?.businessType === 'attachment' && officeConfig?.enable) {/*dm*/
\n    return {/*dm*/
\n      enableEdit: true,/*dm*/
\n      handleEdit,/*dm*/
\n      disabled/*dm*/
\n    }/*dm*/
\n/*dm*/
\n  }/*dm*/
\n/*dm*/
\n  return {/*dm*/
\n    disabled/*dm*/
\n  };/*dm*/
\n}}",
																									"isShowField":"${item.field.fieldName == $.shareData.var_subModelInfo.fieldName}",
																									"fieldProps":"${() => {/*dm*/
\n  const res = {/*dm*/
\n    ...item.field/*dm*/
\n  }/*dm*/
\n  if ($.shareData.var_setting?.fieldInfo?.[item.fieldName]?.fieldName) {/*dm*/
\n    res.fieldText = $.shareData.var_setting.fieldInfo[item.fieldName]?.fieldName/*dm*/
\n  }/*dm*/
\n  if(item.field?.businessType == 'findRelatedMulti') {/*dm*/
\n    res.isPopup = true;/*dm*/
\n  }/*dm*/
\n  return res;/*dm*/
\n}}",
																									"businessType":"${item.field?.businessType}",
																									"fieldType":"${item.field.fieldType}",
																									"value":"${$.shareData.var_saveForm[item.fieldName]}",
																									"fieldText":""
																								},
																								"attrs":{
																									"setting":"${JSON.stringify({/*dm*/
\n  name: 'field',/*dm*/
\n  fieldInfo: {/*dm*/
\n    fieldName: item.field.fieldName,/*dm*/
\n    businessType: item.field.businessType,/*dm*/
\n    modelName: item.field?.relatedModel?.modelName || item.field.modelName,/*dm*/
\n    fieldText: item.field.fieldText/*dm*/
\n  }/*dm*/
\n})}"
																								}
																							}
																						],
																						"componentName":"VFor",
																						"id":"VFor-kM5jsGhj",
																						"title":"Model循环容器",
																						"props":{
																							"forEach":"${() => {/*dm*/
\n  const businessTypes = items.fields.reduce((res, item) => {/*dm*/
\n    res[item.fieldName] = item.field.businessType;/*dm*/
\n    return res;/*dm*/
\n  },/*dm*/
\n    {});/*dm*/
\n   return items.fields.filter((item) => {/*dm*/
\n    if (this.ruleManager[item.fieldName]) {/*dm*/
\n      const res = this.ruleManager[item.fieldName]($.shareData.var_saveForm, businessTypes)/*dm*/
\n      return res;/*dm*/
\n    }/*dm*/
\n    return true;/*dm*/
\n  })/*dm*/
\n}}",
																							"forEachKey":"item",
																							"index":"$index"
																						}
																					},
																					{
																						"componentType":"custom",
																						"children":[
																							{
																								"componentType":"custom",
																								"children":[
																									{
																										"componentType":"custom",
																										"children":[
																											{
																												"componentType":"custom",
																												"componentName":"TextWrapper",
																												"id":"TextWrapper-wsm3Q6h7",
																												"props":{
																													"children":"${__subModel.modelText}"
																												},
																												"attrs":{
																													"style":{
																														"border-left":"3px solid #4871E4",
																														"display":"inline-block",
																														"padding-left":"4px",
																														"line-height":1,
																														"height":"14px"
																													},
																													"class":""
																												}
																											},
																											{
																												"componentType":"custom",
																												"children":[
																													{
																														"children":[
																															{
																																"children":[
																																	{
																																		"action":{
																																			"fire":{
																																				"click":{
																																					"arguments":[],
																																					"actions":[
																																						{
																																							"handler":"${function(){ console.log(\"button click\");}}",
																																							"type":"function"
																																						}
																																					]
																																				}
																																			}
																																		},
																																		"componentName":"Button",
																																		"id":"Button-PYnMW6QZ",
																																		"attrs":{},
																																		"props":{
																																			"size":"small",
																																			"children":"${__item.label}",
																																			"icon":"${`joyIconFont ${__item.icon}`}",
																																			"type":"text"
																																		}
																																	}
																																],
																																"componentName":"VFor",
																																"id":"VFor-ejAfHAF6",
																																"props":{
																																	"forEach":"${() => {\r/*dm*/
\n  const viewCode = __subModel.relatedListViewCode\r/*dm*/
\n\r/*dm*/
\n  return $.functions.fun_getCustomColumn(viewCode)\r/*dm*/
\n}}",
																																	"forEachKey":"__item",
																																	"index":"$index"
																																}
																															}
																														],
																														"componentName":"VIf",
																														"id":"VIf-j2DRbwYS",
																														"props":{
																															"conditions":"${() => {\r/*dm*/
\n  const viewCode = __subModel.relatedListViewCode\r/*dm*/
\n\r/*dm*/
\n  return [$.functions.fun_getCustomColumn(viewCode).length]\r/*dm*/
\n}}"
																														}
																													},
																													{
																														"componentType":"custom",
																														"action":{
																															"fire":{
																																"click":{
																																	"arguments":[],
																																	"actions":[
																																		{
																																			"handler":"${function handler(){/*dm*/
\n/*dm*/
\nconst { modelName, modelText, fieldName, relatedListViewCode } = __subModel;/*dm*/
\nconst addPath = $.shareData.var_setting.table?.[relatedListViewCode]?.addUrl || $.functions.fun_getOperatePath(modelName, 'form')/*dm*/
\n$.bom.page?.open(addPath, {/*dm*/
\n  pageType: 'subModel',/*dm*/
\n  subModel: {/*dm*/
\n    modelName,/*dm*/
\n    modelText,/*dm*/
\n    fieldName/*dm*/
\n  }/*dm*/
\n}, async (data) => {/*dm*/
\n  if (data) {/*dm*/
\n    const tableKey = relatedListViewCode;/*dm*/
\n    // 模型列表数据/*dm*/
\n    $.shareData.var_tables[tableKey].tableList.push({/*dm*/
\n      ...data,/*dm*/
\n      id: $.shareData.var_consts.subModelIDPrefix + $.shareData.var_tables[tableKey].tableData.addDatas.length/*dm*/
\n    });/*dm*/
\n    const res = await $.functions.fun_tableDataTransform('output', modelName, data)/*dm*/
\n/*dm*/
\n    $.shareData.var_tables[tableKey].tableData.addDatas.push(res);/*dm*/
\n  }/*dm*/
\n}, {/*dm*/
\n  options: {/*dm*/
\n    width: '960px',/*dm*/
\n    title: '新建',/*dm*/
\n    closeOnClickModal: false,/*dm*/
\n    customClass: 'sub-model-dialog',/*dm*/
\n  },/*dm*/
\n  type: 'dialog'/*dm*/
\n});/*dm*/
\n/*dm*/
\n/*dm*/
\n/*dm*/
\n}}",
																																			"type":"function"
																																		}
																																	]
																																}
																															}
																														},
																														"componentName":"Button",
																														"id":"Button-b4QQpEaj",
																														"props":{
																															"size":"small",
																															"children":"新建",
																															"type":"text"
																														},
																														"attrs":{}
																													}
																												],
																												"componentName":"DivWrapper",
																												"id":"DivWrapper-X6Pw7Akz",
																												"attrs":{
																													"style":{
																														"padding-top":"4px",
																														"padding-left":"4px",
																														"padding-bottom":"4px",
																														"padding-right":"4px"
																													},
																													"setting":"${JSON.stringify({\r/*dm*/
\n  name: 'buttons', operationButtonNotShow: true,\r/*dm*/
\n  modelName: __subModel.modelName,\r/*dm*/
\n  editorViewCode: __subModel.relatedListViewCode\r/*dm*/
\n})\r/*dm*/
\n}"
																												},
																												"props":{
																													"name":"DivWrapper"
																												}
																											}
																										],
																										"componentName":"DivWrapper",
																										"id":"DivWrapper-QxHxESS3",
																										"attrs":{
																											"style":{
																												"display":"flex",
																												"justify-content":"space-between",
																												"align-items":"center",
																												"margin-bottom":"8px"
																											}
																										}
																									},
																									{
																										"children":[
																											{
																												"componentName":"Import",
																												"id":"Import-MQtREPfh",
																												"attrs":{
																													"name":"table",
																													"style":{}
																												},
																												"props":{
																													"modelName":"${__subModel.modelName}",
																													"modelText":"${__subModel.modelText}",
																													"fieldName":"${__subModel.fieldName}",
																													"enableOperateColumnu":"${true}",
																													"tableStyle":"${{'height': '106px'}}",
																													"viewCode":"${__subModel.relatedListViewCode}"
																												}
																											}
																										],
																										"componentName":"div",
																										"id":"div-ZDhAKZkM",
																										"attrs":{
																											"setting":"${JSON.stringify({/*dm*/
\n  name: 'table', id: __subModel.id, /*dm*/
\n  editorViewCode: __subModel.relatedListViewCode, /*dm*/
\n  modelName: __subModel.modelName,/*dm*/
\n  \"views\": { \"copy\": false, detail: false, keyword: false, advanced:false }/*dm*/
\n})}"
																										}
																									}
																								],
																								"componentName":"DivWrapper",
																								"id":"DivWrapper-rxMP8txG",
																								"attrs":{
																									"style":{
																										"padding":"0 8px",
																										"clear":"both",
																										"margin-bottom":"16px"
																									},
																									"class":"sub-model-root"
																								}
																							}
																						],
																						"componentName":"VFor",
																						"id":"VFor-EdfeNKS5",
																						"title":"Model循环容器",
																						"props":{
																							"forEach":"${items.subModelList}",
																							"forEachKey":"__subModel",
																							"index":"$index"
																						}
																					}
																				],
																				"componentName":"CollapseItem",
																				"id":"CollapseItem-PZ3mDFEA",
																				"props":{
																					"name":"${'isUnfold'}",
																					"title":"${items.groupName}"
																				},
																				"attrs":{
																					"style":{}
																				}
																			}
																		],
																		"componentName":"Collapse",
																		"id":"Collapse-jFZMkjPw",
																		"props":{
																			"value":"${$.shareData.var_setting.group?.[items.groupCode]?.isUnfold === false ? '' : 'isUnfold'}"
																		},
																		"attrs":{
																			"style":{
																				"flex":"1",
																				"margin-bottom":"16px"
																			},
																			"class":"${}"
																		}
																	}
																],
																"componentName":"Col",
																"id":"Col-E5TPiC3Q",
																"attrs":{
																	"style":{
																		"padding":"0px",
																		"width":"100%"
																	},
																	"class":"${$.shareData.var_setting?.group?.[items.groupCode]?.enableTitle === false ? 'system-collapse-hidden-wrapper' : 'xxxxx'}",
																	"setting":"${JSON.stringify({name: 'group', id: items.id, groupCode: items.groupCode,  groupName: items.groupName })}"
																},
																"props":{
																	"span":24
																}
															}
														],
														"componentName":"VFor",
														"id":"VFor-6NtcZpxK",
														"title":"Collapse循环容器",
														"props":{
															"forEach":"${$.shareData.var_viewDetailList}",
															"forEachKey":"items",
															"index":"$index"
														}
													}
												],
												"componentName":"ModelForm",
												"id":"ModelForm-K8aRky8k",
												"title":"表单组件",
												"version":"1.8.0",
												"props":{
													"ref":"savesForm",
													"size":"small",
													"labelPosition":"top",
													"formData":"${$.shareData.var_saveForm}"
												},
												"attrs":{
													"class":"editPageForm"
												}
											}
										],
										"componentName":"DivWrapper",
										"id":"DivWrapper-Z5M6fPyh",
										"title":"Main",
										"attrs":{
											"style":{
												"padding":"0"
											}
										}
									}
								],
								"componentName":"ModelDrivenMarkup",
								"id":"ModelDrivenMarkup-Ry7Ks8iH",
								"title":"模型辅助配置",
								"version":"1.8.0",
								"props":{
									"modelName":"ss",
									"modelText":"全字段",
									"formType":"save",
									"findRelatedTableKey":"findRelated"
								}
							}
						],
						"componentName":"DivWrapper",
						"id":"DivWrapper-iaSrF3di",
						"title":"Header+Main",
						"attrs":{
							"style":{
								"overflow":"auto",
								"flex":"1",
								"box-sizing":"border-box"
							}
						}
					},
					{
						"toolbar":true,
						"componentType":"custom",
						"children":[
							{
								"componentType":"custom",
								"action":{
									"fire":{
										"click":{
											"actions":[
												{
													"handler":"${function undefined(){/*dm*/
\n                          $.bom.page.close() || $.bom.route('go', -1);/*dm*/
\n                        }}",
													"type":"function"
												}
											]
										}
									}
								},
								"componentName":"Button",
								"id":"Button-fDP85bC2",
								"attrs":{
									"class":"bottom-btn"
								},
								"props":{
									"size":"small",
									"children":"取消"
								},
								"chosen":false
							},
							{
								"componentType":"custom",
								"children":[
									{
										"componentType":"custom",
										"action":{
											"fire":{
												"click":{
													"actions":[
														{
															"handler":"${function Fun34975(){/*dm*/
\nlet format = await $.functions.fun_formFormat($.shareData.var_setting.modelName);/*dm*/
\nconst saveForm = { ...format.output($.shareData.var_saveForm) }/*dm*/
\nconst draftId = $.functions.fun_getQueryValue('draftId');/*dm*/
\nconst views = $.shareData.var_viewDetailList;/*dm*/
\nconst subModel = {};/*dm*/
\n/*dm*/
\nviews.forEach(group => {/*dm*/
\n  group.subModelList.forEach(model => {/*dm*/
\n    const tableKey = model.relatedListViewCode;/*dm*/
\n    const tableData = $.shareData.var_tables[tableKey].tableData;/*dm*/
\n    subModel['$subModel$' + model.modelName + '$' + model.fieldName] = tableData/*dm*/
\n  })/*dm*/
\n})/*dm*/
\nconst formBody = {/*dm*/
\n  \"name\": $.shareData.var_pageInfo.modelText,/*dm*/
\n  \"vn\": 0,/*dm*/
\n  \"data_info\": JSON.stringify({/*dm*/
\n    ...saveForm,/*dm*/
\n    $subModel:subModel,/*dm*/
\n  }),/*dm*/
\n  \"model_name\": $.shareData.var_setting.modelName,/*dm*/
\n  '$__draftId': draftId/*dm*/
\n};/*dm*/
\nawait $.shareData.api_postCommonDraftAction(formBody);/*dm*/
\nif ($.shareData.api_postCommonDraft.code == 200) {/*dm*/
\n  this.$message.success('暂存成功!');/*dm*/
\n  $.bom.page.close() || $.bom.route('go', -1);/*dm*/
\n} else {/*dm*/
\n  this.$message.error($.shareData.api_postCommonDraft.msg);/*dm*/
\n}/*dm*/
\n}}",
															"type":"function"
														}
													]
												}
											}
										},
										"componentName":"Button",
										"id":"Button-2dsQ6djk",
										"props":{
											"size":"small",
											"children":"暂存"
										},
										"chosen":false
									}
								],
								"id":"VIf-fx6EKpXM",
								"componentName":"VIf",
								"title":"逻辑容器",
								"props":{
									"conditions":"${[$.shareData.var_pageInfo.openDraft && !$.shareData.var_pageInfo.isSubModel && !$.shareData.var_pageInfo?.isEdit ]}"
								}
							},
							{
								"componentType":"custom",
								"action":{
									"fire":{
										"click":{
											"actions":[
												{
													"handler":"${function Fun98425(){const modelName = $.shareData.var_setting.modelName;/*dm*/
\nthis.$refs['savesForm'].form.validate(async validate => {/*dm*/
\n  if (validate) {/*dm*/
\n    let format = await $.global.functions.modelValueFormat(modelName, $.shareData.var_pageInfo.isEdit ? 'add' : 'edit');/*dm*/
\n    // 如果是子模型，仅提供数据/*dm*/
\n    const pageParams = $.bom.page.params()?.data;/*dm*/
\n    if (pageParams?.pageType == 'subModel') {/*dm*/
\n      const {/*dm*/
\n        fieldName/*dm*/
\n      } = pageParams.subModel;/*dm*/
\n      $.shareData.var_saveForm[fieldName] = undefined;/*dm*/
\n      $.bom.page.close($.shareData.var_saveForm);/*dm*/
\n      // $.bom.page.close(format.output($.shareData.var_saveForm));/*dm*/
\n      return;/*dm*/
\n    }/*dm*/
\n    let { id, ...saveForm } = format.output($.shareData.var_saveForm);/*dm*/
\n    // 父级模型：如果包含子模型，则处理子模型数据/*dm*/
\n    const views = $.shareData.var_viewDetailList;/*dm*/
\n    const subModel = {};/*dm*/
\n/*dm*/
\n    views.forEach(group => {/*dm*/
\n      group.subModelList.forEach(model => {/*dm*/
\n        const tableKey = model.relatedListViewCode;/*dm*/
\n        const tableData = $.shareData.var_tables[tableKey].tableData;/*dm*/
\n        subModel['$subModel$' + model.modelName + '$' + model.fieldName] = {/*dm*/
\n          addDatas: tableData?.addDatas || [],/*dm*/
\n          updateDatas: tableData?.updateDatas || [],/*dm*/
\n          deleteIds: tableData?.deleteIds || [],/*dm*/
\n        }/*dm*/
\n      })/*dm*/
\n    })/*dm*/
\n    const formData = {/*dm*/
\n      modelName,/*dm*/
\n      ...saveForm,/*dm*/
\n      ...subModel/*dm*/
\n    }/*dm*/
\n    if ($.shareData.var_pageInfo.isEdit) {/*dm*/
\n      formData.id = id;/*dm*/
\n    } else {/*dm*/
\n      delete formData.id;/*dm*/
\n    }/*dm*/
\n    $.shareData.var_saveLoadingAction(true);/*dm*/
\n    // console.log(formData)/*dm*/
\n    // return;/*dm*/
\n    await $.shareData.api_saveDataAction(formData);/*dm*/
\n    if ($.shareData.api_saveData.code == 200) {/*dm*/
\n      this.$message.success($.shareData.var_pageInfo.typeTitle + '成功!');/*dm*/
\n      $.bom.page.close({ flag: true, data: $.shareData.api_saveData.data }) || $.bom.route('push', $.shareData.var_setting.listPath);/*dm*/
\n      $.shareData.var_saveLoadingAction(false);/*dm*/
\n    } else {/*dm*/
\n      $.shareData.var_saveLoadingAction(false);/*dm*/
\n    }/*dm*/
\n  } else {/*dm*/
\n    this.$nextTick(() => {/*dm*/
\n      var elements = document.querySelectorAll('.el-form-item__error');/*dm*/
\n      elements.length && elements[0].closest('.el-form-item').scrollIntoView({ behavior: \"smooth\", block: \"start\" });/*dm*/
\n    })/*dm*/
\n  }/*dm*/
\n});/*dm*/
\n}}",
													"type":"function"
												}
											]
										}
									}
								},
								"componentName":"Button",
								"id":"Button-5nbQDX3h",
								"props":{
									"size":"small",
									"children":"保存",
									"type":"primary",
									"loading":"${$.shareData.var_saveLoading}"
								},
								"chosen":false
							},
							{
								"componentType":"custom",
								"children":[
									{
										"componentType":"custom",
										"action":{
											"fire":{
												"click":{
													"actions":[
														{
															"handler":"${function Fun75449(){/*dm*/
\nthis.$refs['savesForm'].form.validate(async validate => {/*dm*/
\n  if (validate) {/*dm*/
\n    let format = await $.functions.fun_formFormat($.shareData.var_setting.modelName);/*dm*/
\n    let { id, ...formData } = format.output($.shareData.var_saveForm);/*dm*/
\n    if ($.shareData.var_pageInfo.wfOpen) {/*dm*/
\n      // 流程标识/*dm*/
\n      formData.$__saveAndSubmitWf = true;/*dm*/
\n    }/*dm*/
\n    // 父级模型：如果包含子模型，则处理子模型数据/*dm*/
\n    const views = $.shareData.var_viewDetailList;/*dm*/
\n    const subModel = {};/*dm*/
\n    views.forEach(group => {/*dm*/
\n      group.subModelList.forEach(model => {/*dm*/
\n        const tableKey = model.relatedListViewCode;/*dm*/
\n        const tableData = $.shareData.var_tables[tableKey].tableData;/*dm*/
\n        subModel['$subModel$' + model.modelName + '$' + model.fieldName] = {/*dm*/
\n          addDatas: tableData?.addDatas || [],/*dm*/
\n          updateDatas: tableData?.updateDatas || [],/*dm*/
\n          deleteIds: tableData?.deleteIds || [],/*dm*/
\n        }/*dm*/
\n      })/*dm*/
\n    })/*dm*/
\n/*dm*/
\n    if ($.shareData.var_pageInfo.isEdit) {/*dm*/
\n      formData.id = id;/*dm*/
\n    }/*dm*/
\n/*dm*/
\n    await $.shareData.api_saveDataAction({/*dm*/
\n      ...formData,/*dm*/
\n      ...subModel,/*dm*/
\n      modelName: $.shareData.var_setting.modelName/*dm*/
\n    });/*dm*/
\n    if ($.shareData.api_saveData.code == 200) {/*dm*/
\n      this.$message.success(formData?.id ? '编辑成功!' : '新增成功');/*dm*/
\n      $.bom.page.close() || $.bom.route('go', -1);/*dm*/
\n      await $.shareData.var_saveLoadingAction(false);/*dm*/
\n    } else {/*dm*/
\n      this.$message.error($.shareData.api_saveData.msg);/*dm*/
\n      await $.shareData.var_saveLoadingAction(false);/*dm*/
\n    }/*dm*/
\n  }/*dm*/
\n});/*dm*/
\n}}",
															"type":"function"
														}
													]
												}
											}
										},
										"componentName":"Button",
										"id":"Button-RhXC62y2",
										"props":{
											"size":"small",
											"children":"保存并提交",
											"type":"primary"
										},
										"chosen":false
									}
								],
								"id":"VIf-TYKrjNYM",
								"componentName":"VIf",
								"title":"逻辑容器",
								"props":{
									"conditions":"${[$.shareData.var_pageInfo.wfOpen && $.shareData.var_pageInfo.wfStatus]}"
								}
							}
						],
						"componentName":"Col",
						"id":"Col-tyfeKiEA",
						"title":"Footer",
						"props":{
							"style":{
								"flex-shrink":"0"
							},
							"span":24
						},
						"attrs":{
							"class":"editPageFooter"
						}
					}
				],
				"componentName":"DivWrapper",
				"id":"DivWrapper-KTZdSnQ2",
				"title":"全字段新建页面",
				"attrs":{
					"style":{
						"flex-direction":"column",
						"display":"flex"
					},
					"class":"editPage"
				}
			},
			{
				"componentType":"custom",
				"children":[
					{
						"componentName":"Include",
						"id":"Include-8BZxGM5x",
						"props":{
							"data":"${$.shareData.var_findRelationInfo?.queryConditions}",
							"v-loading":"${$.shareData.var_findRelationInfo.loading}",
							":onLoad":"${function handler(){/*dm*/
\n$.$emit('findRelation',/*dm*/
\n  $.shareData.var_findRelationInfo,/*dm*/
\n  $.shareData.var_saveForm[$.shareData.var_findRelationInfo.fieldName]/*dm*/
\n)/*dm*/
\n$.shareData.var_findRelationInfoAction({/*dm*/
\n  loading: false/*dm*/
\n})}}",
							"run":"${$.shareData.var_findRelationInfo.visible}",
							"pageId":"${() => {/*dm*/
\n  const { modelName, fieldName}= $.shareData.var_findRelationInfo;/*dm*/
\n  const custom = $.shareData.var_setting?.fieldInfo?.[fieldName]?.url/*dm*/
\n  return custom || `/${modelName}/list`;/*dm*/
\n}}",
							"key":"${$.shareData.var_findRelationInfo.modelName + $.shareData.var_findRelationInfo.fieldName}"
						},
						"attrs":{
							"style":{
								"height":"50vh"
							}
						}
					},
					{
						"componentType":"custom",
						"children":[
							{
								"componentType":"custom",
								"action":{
									"fire":{
										"click":{
											"actions":[
												{
													"handler":"${function handler(){$.shareData.var_findRelationInfoAction({/*dm*/
\n  visible: false/*dm*/
\n})/*dm*/
\n$.shareData.var_findRelatedFormAction(null);}}",
													"type":"function"
												}
											]
										}
									}
								},
								"componentName":"Button",
								"id":"Button-HfXQmEcW",
								"attrs":{},
								"props":{
									"size":"small",
									"children":"取消"
								}
							},
							{
								"componentType":"custom",
								"action":{
									"fire":{
										"click":{
											"actions":[
												{
													"handler":"${function Fun24728(){const { modelName, fieldName, viewCode } = $.shareData.var_findRelationInfo;/*dm*/
\n/*dm*/
\nconst newActionValue = $.shareData.var_findRelatedForm/*dm*/
\n/*dm*/
\nif (newActionValue) {/*dm*/
\n  // 当前字段关联规则内容请求/*dm*/
\n  let res = newActionValue;/*dm*/
\n  if ($.shareData.var_findRelationInfo.businessType == 'findRelated') {/*dm*/
\n    let obj = newActionValue;/*dm*/
\n    await $.shareData.api_getReferencedInfoAction({/*dm*/
\n      modelName: $.shareData.var_setting.modelName,/*dm*/
\n      fieldName: fieldName,/*dm*/
\n      id: obj.id/*dm*/
\n    })/*dm*/
\n/*dm*/
\n    let referenced = $.shareData.api_getReferencedInfo;/*dm*/
\n/*dm*/
\n    if (referenced?.data) {/*dm*/
\n      for (let key in referenced.data) {/*dm*/
\n        this.$set($.shareData.var_saveForm, key, referenced?.data[key])/*dm*/
\n      }/*dm*/
\n    }/*dm*/
\n    res = { ...obj }/*dm*/
\n  }/*dm*/
\n/*dm*/
\n/*dm*/
\n  // 使用$set追加响应式/*dm*/
\n  this.$set($.shareData.var_saveForm, fieldName, res)/*dm*/
\n/*dm*/
\n  $.shareData.var_findRelationInfoAction({/*dm*/
\n    visible: false/*dm*/
\n  })/*dm*/
\n  $.shareData.var_findRelatedFormAction(null);/*dm*/
\n/*dm*/
\n/*dm*/
\n} else if (!newActionValue && !$.shareData.var_saveForm[fieldName]) {/*dm*/
\n  this.$message.error('请点击表格行选择数据!');/*dm*/
\n} else {/*dm*/
\n  $.shareData.var_findRelationInfoAction({/*dm*/
\n    visible: false/*dm*/
\n  })/*dm*/
\n}/*dm*/
\n}}",
													"type":"function"
												}
											]
										}
									}
								},
								"componentName":"Button",
								"id":"Button-hCmC8hAJ",
								"attrs":{},
								"props":{
									"size":"small",
									"children":"确定",
									"type":"primary"
								},
								"chosen":false
							}
						],
						"componentName":"DivWrapper",
						"id":"DivWrapper-j7ERbCn5",
						"props":{
							"name":"footer",
							"slot":"footer"
						}
					}
				],
				"action":{
					"fire":{
						"close":{
							"actions":[
								{
									"handler":"${function Fun50778(){$.$emit('findRelation', null)/*dm*/
\n$.shareData.var_findRelationInfoAction({/*dm*/
\n  visible: false/*dm*/
\n})}}",
									"type":"function"
								}
							]
						}
					}
				},
				"componentName":"Dialog",
				"id":"Dialog-DdF4ZSEn",
				"title":"被关联模型",
				"key":"beiguanlianmoxing71321save",
				"props":{
					"modal-append-to-body":"${false}",
					"append-to-body":"${true}",
					"destroy-on-close":"${true}",
					"visible":"${$.shareData.var_findRelationInfo.visible}",
					"width":"${\"60%\"}",
					"title":"${() => {/*dm*/
\n/*dm*/
\n  return `请选择【${$.shareData.var_findRelationInfo.modelText}】`/*dm*/
\n}}",
					"close-on-click-modal":"",
					"close-on-press-escape":false,
					"custom-class":"recordsDialogBox"
				}
			}
		],
		"functions":[
			{
				"default":"${function preUpload(file) {/*dm*/
\n        return $.shareData.api_getPreUploadUrlAction({/*dm*/
\n          name: file.name/*dm*/
\n        }).catch(e=>{/*dm*/
\n          console.log('e', e)/*dm*/
\n        })/*dm*/
\n      }}",
				"description":"获取页面id",
				"key":"fun_preUpload"
			},
			{
				"default":"${function getQueryValue(queryName) {/*dm*/
\n  const query = $.bom.page?.query?.() || {};/*dm*/
\n  return query[queryName] || null;/*dm*/
\n}}",
				"description":"获取页面id",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_getQueryValue"
			},
			{
				"default":"${function fun_initFormView(modelName) {/*dm*/
\n/*dm*/
\n  await $.shareData.api_getFormPageInfoAction({/*dm*/
\n    modelName: $.shareData.var_setting.modelName,/*dm*/
\n    pageKey: $.shareData.var_setting.pageKey/*dm*/
\n  })/*dm*/
\n/*dm*/
\n  const initListColumns = (relatedModelName, viewInfoList) => {/*dm*/
\n    let allFieldList = []/*dm*/
\n/*dm*/
\n    viewInfoList.map((value) => {/*dm*/
\n/*dm*/
\n      const fieldInfo = $.global.shareData.modelsInfoData.fieldsMap[relatedModelName]?.get(value.fieldName)/*dm*/
\n/*dm*/
\n      const newData = {/*dm*/
\n        ...value,/*dm*/
\n        name: value.fieldName,/*dm*/
\n        label: fieldInfo.fieldText,/*dm*/
\n        defaultSelectValue: value.defaultSelect,/*dm*/
\n        modelField: fieldInfo,/*dm*/
\n        index: value.showIndex,/*dm*/
\n        fieldWidth: value.displayWidth/*dm*/
\n      }/*dm*/
\n/*dm*/
\n      if (value.isEnable && !value.relatedFieldList?.length) {/*dm*/
\n        allFieldList.push(newData);/*dm*/
\n      }/*dm*/
\n/*dm*/
\n      if (value.relatedFieldList?.length > 0) {/*dm*/
\n        const field = fieldInfo;/*dm*/
\n        value.relatedFieldList.map(item => {/*dm*/
\n          const relatedField = field.relatedModel.modelFields.find(it1 => it1.fieldName === item.relatedFieldName)/*dm*/
\n          // 再添加循环的数据/*dm*/
\n          if (item.isEnable) {/*dm*/
\n            allFieldList.push({/*dm*/
\n              ...item,/*dm*/
\n              name: item.fieldName,/*dm*/
\n              fieldWidth: item.displayWidth,/*dm*/
\n              fieldName: `${item.fieldName}.${item.relatedFieldName}`,/*dm*/
\n              fieldText: relatedField.fieldText,/*dm*/
\n              label: relatedField.fieldText,/*dm*/
\n              businessType: field.businessType,/*dm*/
\n              index: item.showIndex,/*dm*/
\n              modelField: {/*dm*/
\n                ...field,/*dm*/
\n                calculationFieldName: item.relatedFieldName/*dm*/
\n              }/*dm*/
\n            })/*dm*/
\n          }/*dm*/
\n        })/*dm*/
\n      }/*dm*/
\n/*dm*/
\n      return/*dm*/
\n/*dm*/
\n    })/*dm*/
\n    allFieldList = allFieldList.sort((a, b) => a.showIndex - b.showIndex);/*dm*/
\n/*dm*/
\n    return allFieldList;/*dm*/
\n  }/*dm*/
\n/*dm*/
\n  const models = new Set();/*dm*/
\n  // 处理分组中的子模型table视图/*dm*/
\n  $.shareData.api_getFormPageInfo.viewGroupList.forEach(({ groupName, groupCode, groupInfoList, viewRelatedList }) => {/*dm*/
\n    if (viewRelatedList.length) {/*dm*/
\n      viewRelatedList.forEach(({ relatedModelName, relatedListViewCode }) => {/*dm*/
\n        models.add(relatedModelName)/*dm*/
\n      })/*dm*/
\n    }/*dm*/
\n  });/*dm*/
\n  // 处理表单中关联关系的table视图/*dm*/
\n  let hasFindRelation = false, findRelationViewCode = {};/*dm*/
\n  $.shareData.api_getFormPageInfo.viewFindRelationships.forEach(({ relatedListViewCode, fieldName, relatedModelName }) => {/*dm*/
\n    models.add(relatedModelName)/*dm*/
\n    hasFindRelation = true;/*dm*/
\n  });/*dm*/
\n  // 统一调用模型接口/*dm*/
\n  if (models.size) {/*dm*/
\n    await Promise.all([...models].map(modelName => $.global.functions.getModelDictionaryInfo(modelName)));/*dm*/
\n  }/*dm*/
\n  // 处理关联字段/*dm*/
\n  if (hasFindRelation) {/*dm*/
\n    $.shareData.api_getFormPageInfo.viewFindRelationships.forEach(({ listViews: { viewInfoList, viewSortList }, relatedListViewCode, fieldName, relatedModelName, editorViewCode }) => {/*dm*/
\n      const viewCode = relatedListViewCode + fieldName;/*dm*/
\n      findRelationViewCode[fieldName] = viewCode/*dm*/
\n      $.functions.fun_initTableView({/*dm*/
\n        modelText: '',/*dm*/
\n        modelName: relatedModelName,/*dm*/
\n        viewCode,/*dm*/
\n        setting: {/*dm*/
\n          showPagination: true,/*dm*/
\n          showRadio: true,/*dm*/
\n          showNo: false/*dm*/
\n        },/*dm*/
\n        querySorts: viewSortList.map(({ fieldName, sortOrder }) => ({ fieldName, desc: sortOrder == 'asc' ? false : true }))/*dm*/
\n/*dm*/
\n      })/*dm*/
\n      this.$set($.shareData.var_tables[viewCode], 'columns', initListColumns(relatedModelName, viewInfoList))/*dm*/
\n    });/*dm*/
\n  }/*dm*/
\n/*dm*/
\n/*dm*/
\n  // 处理分组数据/*dm*/
\n  const groupData = $.shareData.api_getFormPageInfo.viewGroupList.map(({ id, groupName, groupCode, groupInfoList, viewRelatedList }) => {/*dm*/
\n    return {/*dm*/
\n      groupName,/*dm*/
\n      groupCode,/*dm*/
\n      id,/*dm*/
\n      showDrawer: true || $.shareData.var_setting.group?.[item.groupCode]?.enableTitle,/*dm*/
\n      subModelList: viewRelatedList.map(({ relatedShowName, relatedModelName, relatedListViewCode, id, relatedFieldName, listViews: { viewInfoList, viewSortList } }) => {/*dm*/
\n        $.functions.fun_initTableView({/*dm*/
\n          modelText: relatedShowName,/*dm*/
\n          modelName: relatedModelName,/*dm*/
\n          viewCode: relatedListViewCode,/*dm*/
\n          querySorts: viewSortList.map(({ fieldName, sortOrder }) => ({ fieldName, desc: sortOrder == 'asc' ? false : true }))/*dm*/
\n        })/*dm*/
\n        this.$set($.shareData.var_tables[relatedListViewCode], 'columns', initListColumns(relatedModelName, viewInfoList))/*dm*/
\n        return {/*dm*/
\n          modelText: relatedShowName,/*dm*/
\n          modelName: relatedModelName,/*dm*/
\n          fieldName: relatedFieldName,/*dm*/
\n          relatedListViewCode,/*dm*/
\n          id/*dm*/
\n/*dm*/
\n        }/*dm*/
\n      }),/*dm*/
\n      fields: !groupInfoList ? [] : groupInfoList.reduce((arr, { fieldName, modelFieldPermission }) => {/*dm*/
\n        const field = $.global.shareData.modelsInfoData.fieldsMap[modelName]?.get(fieldName);/*dm*/
\n/*dm*/
\n        field.preUpload = $.functions.fun_preUpload;/*dm*/
\n        if (field.syncFieldData) {/*dm*/
\n          field.disabled = !!field?.syncFieldData/*dm*/
\n        }/*dm*/
\n        // 自动编号、引用字段、公式、汇总字段类型 不展示/*dm*/
\n        const hiddenFields = ['autoNumber', 'formula', 'summary', 'referenced']/*dm*/
\n/*dm*/
\n        if (!hiddenFields.includes(field.businessType) && !field.isReferencedField) {/*dm*/
\n/*dm*/
\n          arr.push({/*dm*/
\n            fieldName,/*dm*/
\n            modelFieldPermission: modelFieldPermission,/*dm*/
\n            field: {/*dm*/
\n              ...(findRelationViewCode[fieldName] ? { viewCode: findRelationViewCode[fieldName] } : {}),/*dm*/
\n              ...field,/*dm*/
\n            }/*dm*/
\n          })/*dm*/
\n/*dm*/
\n          // 新建页过滤掉modelFieldPermission不为2(编辑权限)的/*dm*/
\n          if (!$.shareData.var_pageInfo.isEdit) {/*dm*/
\n            arr = arr.filter(v => v.modelFieldPermission == 2)/*dm*/
\n          }/*dm*/
\n        }/*dm*/
\n        return arr;/*dm*/
\n/*dm*/
\n      }, [])/*dm*/
\n    };/*dm*/
\n  })/*dm*/
\n  $.shareData.var_viewDetailListAction(groupData);/*dm*/
\n}}",
				"description":"初始化视图",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_initFormView"
			},
			{
				"default":"${function getFormEdit(detailId, formData = {}) {/*dm*/
\n  if (detailId) {/*dm*/
\n    let format = await $.functions.fun_formFormat($.shareData.var_setting.modelName);/*dm*/
\n    if (detailId.includes($.shareData.var_consts.subModelIDPrefix)) {/*dm*/
\n      // 回显子模型新增的数据/*dm*/
\n      const pageParams = $.bom.page.params()?.data;/*dm*/
\n      const obj = pageParams.formData;/*dm*/
\n      for (let key in obj) {/*dm*/
\n        this.$set($.shareData.var_saveForm, key, obj[key])/*dm*/
\n      }/*dm*/
\n      return;/*dm*/
\n    }/*dm*/
\n    await $.shareData.api_getInfoAction({ id: detailId, modelName: $.shareData.var_setting.modelName });/*dm*/
\n    let res = $.shareData.api_getInfo.data;/*dm*/
\n    const obj = format.input(res);/*dm*/
\n    const mergeData = {/*dm*/
\n      ...obj,/*dm*/
\n      ...formData/*dm*/
\n    }/*dm*/
\n    for (let key in obj) {/*dm*/
\n      this.$set($.shareData.var_saveForm, key, mergeData[key])/*dm*/
\n    }/*dm*/
\n    // 流程状态处理/*dm*/
\n    if ($.shareData.var_pageInfo.wfOpen) {/*dm*/
\n      // 待提交 审批不通过显示按钮/*dm*/
\n      $.shareData.var_pageInfo.wfStatus = ['1', '4'].includes($.shareData.var_saveForm.approval_status);/*dm*/
\n    }/*dm*/
\n  } else {/*dm*/
\n    await $.shareData.api_getDefaultDataAction({ modelName: $.shareData.var_setting.modelName }).then(async res => {/*dm*/
\n      let format = await $.functions.fun_formFormat($.shareData.var_setting.modelName);/*dm*/
\n      const obj = format.input(res.data);/*dm*/
\n      for (let key in obj) {/*dm*/
\n        this.$set($.shareData.var_saveForm, key, obj[key])/*dm*/
\n      }/*dm*/
\n      // 流程状态处理/*dm*/
\n      if ($.shareData.var_pageInfo.wfOpen) {/*dm*/
\n        // 待提交 审批不通过显示按钮/*dm*/
\n        $.shareData.var_pageInfo.wfStatus = ['1', '4'].includes($.shareData.var_saveForm.approval_status);/*dm*/
\n      }/*dm*/
\n    });/*dm*/
\n  }/*dm*/
\n}}",
				"description":"获取页面信息",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_getFormEdit"
			},
			{
				"default":"${function fun_initSubModelPage() {/*dm*/
\n  // 当在子模型下页面处理/*dm*/
\n  const addPageParams = $.bom.page.params()?.data;/*dm*/
\n  if (addPageParams?.pageType == 'subModel') {/*dm*/
\n    $.shareData.var_pageInfoAction({/*dm*/
\n      showHeader: false,/*dm*/
\n    })/*dm*/
\n/*dm*/
\n    const { fieldName, modelText, modelName } = addPageParams.subModel;/*dm*/
\n    $.shareData.var_subModelInfoAction({/*dm*/
\n      fieldName,/*dm*/
\n      modelText,/*dm*/
\n      modelName,/*dm*/
\n      fieldId: `${modelName}|{fieldName}`/*dm*/
\n    });/*dm*/
\n    // 如果是子模型表单， 则补充默认数据/*dm*/
\n/*dm*/
\n    $.shareData.var_saveForm[fieldName] = modelText;/*dm*/
\n  }/*dm*/
\n}}",
				"description":"",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_initSubModelPage"
			},
			{
				"default":"${function fun_initDraftInfo() {/*dm*/
\n  const draftId =  $.functions.fun_getQueryValue('draftId');/*dm*/
\n  if (draftId) {/*dm*/
\n    // 获草稿信息/*dm*/
\n    await $.shareData.api_getCommonDraftInfoAction({ id: draftId });/*dm*/
\n    const draftInfo = $.shareData.api_getCommonDraftInfo;/*dm*/
\n    if (draftInfo?.code == 200 && draftInfo?.data) {/*dm*/
\n      const formInfo = draftInfo?.data;/*dm*/
\n     /*dm*/
\n      // 主子表回显/*dm*/
\n      if (formInfo?.$subModel) {/*dm*/
\n        for (let info in formInfo.$subModel) {/*dm*/
\n          const [,,modelName] = info.split('$'); // $subModle$a$hh/*dm*/
\n          const { viewCode, addDatas } = formInfo.$subModel[info];/*dm*/
\n          if ($.shareData.var_tables[viewCode]) {/*dm*/
\n            // 存储子表数据/*dm*/
\n            this.$set($.shareData.var_tables[viewCode], 'tableData', {/*dm*/
\n              ...formInfo.$subModel[info]/*dm*/
\n            })/*dm*/
\n            // 回显子表/*dm*/
\n            const res = await $.functions.fun_tableDataTransform('input', modelName, [...addDatas]);/*dm*/
\n/*dm*/
\n            this.$set($.shareData.var_tables[viewCode], 'tableList', res.map((item,i) => {/*dm*/
\n              item.id = $.shareData.var_consts.subModelIDPrefix + i;/*dm*/
\n              return item;/*dm*/
\n            }))/*dm*/
\n          }/*dm*/
\n        }/*dm*/
\n        // 删掉该字段，form表单不能展示该数据/*dm*/
\n        delete formInfo.$subModel/*dm*/
\n      }/*dm*/
\n      // 回显form表单/*dm*/
\n      let format = await $.functions.fun_formFormat($.shareData.var_setting.modelName);/*dm*/
\n      this.$set($.shareData, 'var_saveForm', { ...format.input(formInfo) });/*dm*/
\n    } else {/*dm*/
\n      this.$message.error(draftInfo?.msg);/*dm*/
\n    }/*dm*/
\n  }/*dm*/
\n}}",
				"description":"获取暂存信息",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_initDraftInfo"
			},
			{
				"default":"${function findRelatedQueryRestForm() {/*dm*/
\n  const query = $.bom.page.params()?.data || $.bom.page.query() || {};/*dm*/
\n  const {/*dm*/
\n    formId, fieldName, modelName/*dm*/
\n  } = query;/*dm*/
\n  if (formId && fieldName && modelName) {/*dm*/
\n    await $.shareData.api_getInfoAction({ modelName: modelName, id: formId });/*dm*/
\n    let format = await $.functions.fun_formFormat($.shareData.var_setting.modelName);/*dm*/
\n    $.shareData.var_saveFormAction({ [fieldName]: { ...format.input($.shareData.api_getInfo.data) } });/*dm*/
\n  }/*dm*/
\n}}",
				"description":"获取URL的id和name",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_findRelatedQueryRestForm"
			},
			{
				"default":"${function fun_initCopy(modelName) {/*dm*/
\n  const copyId = $.functions.fun_getQueryValue('copyId')/*dm*/
\n  if (copyId) {/*dm*/
\n    await $.shareData.api_getInfoAction({ id: copyId, modelName: $.shareData.var_setting.modelName });/*dm*/
\n    let res = $.shareData.api_getInfo.data;/*dm*/
\n    let format = await $.global.functions.modelValueFormat($.shareData.var_setting.modelName);/*dm*/
\n    // this.$set($.shareData, 'var_saveForm', { ...format.input(res) })/*dm*/
\n    $.shareData.var_saveFormAction({/*dm*/
\n      ...format.input(res)/*dm*/
\n    })/*dm*/
\n/*dm*/
\n   /*dm*/
\n/*dm*/
\n  }/*dm*/
\n  return/*dm*/
\n}}",
				"description":"格式化form数据",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_initCopy"
			},
			{
				"default":"${function formFormat(modelName){/*dm*/
\n  return $.global.functions.modelValueFormat(modelName, \"edit\")/*dm*/
\n}}",
				"description":"格式化form数据",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_formFormat"
			},
			{
				"default":"${function fun_loadTableData(modelName, viewCode, searchQuery = {}) {/*dm*/
\n  this.$set($.shareData.var_tables[viewCode], 'loading', true);/*dm*/
\n/*dm*/
\n  const query = {/*dm*/
\n    modelName,/*dm*/
\n    ...($.shareData.var_tables[viewCode]?.searchQuery || {}),/*dm*/
\n    ...($.shareData.var_tables[viewCode]?.params || {}),/*dm*/
\n    ...searchQuery,/*dm*/
\n    attributes: $.shareData.var_tables[viewCode]?.columns?.map(item => item.fieldName) || null/*dm*/
\n  }/*dm*/
\n  // 流程数据条件过滤/*dm*/
\n  const modelInfo = await $.global.functions.getModelDictionaryInfo(modelName);/*dm*/
\n  if (modelInfo?.wfOpen) {/*dm*/
\n    query.queryConditions.push({/*dm*/
\n      \"fieldName\": \"approval_status\",/*dm*/
\n      \"valueType\": 0,/*dm*/
\n      \"conditionType\": \"EQ\",/*dm*/
\n      \"value\": \"3\"/*dm*/
\n    })/*dm*/
\n  }/*dm*/
\n  $.shareData.api_getListAction({/*dm*/
\n    ...query/*dm*/
\n  }).then(async item => {/*dm*/
\n    this.$set($.shareData.var_tables[viewCode], 'loading', false);/*dm*/
\n/*dm*/
\n/*dm*/
\n    if ($.shareData.api_getList.code !== 200) {/*dm*/
\n      // this.$message.error('')/*dm*/
\n    } else {/*dm*/
\n      const res = await $.functions.fun_tableDataTransform('input', modelName, $.shareData.api_getList.data.records)/*dm*/
\n      this.$set($.shareData.var_tables[viewCode], 'tableList', res);/*dm*/
\n    }/*dm*/
\n    this.$set($.shareData.var_tables[viewCode]?.params, 'totalSize', $.shareData.api_getList.data.totalSize)/*dm*/
\n    this.$refs[viewCode]?.doLayout();/*dm*/
\n  }).catch(() => {/*dm*/
\n    this.$set($.shareData.var_tables[viewCode], 'loading', false);/*dm*/
\n  })/*dm*/
\n}}",
				"description":"加载表格数据",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_loadTableData"
			},
			{
				"default":"${function fun_genTableId(viewCode, modelName){/*dm*/
\n  const userData = $.bom.getHostData('userInfo')/*dm*/
\n  const genId = `${viewCode}_${userData.personId || userData.userName || userData.pin}_${modelName}`/*dm*/
\n  return genId/*dm*/
\n}}",
				"description":"获取表格唯一id",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_genTableId"
			},
			{
				"default":"${function fun_getOperatePath(modelName, operateName){/*dm*/
\n/*dm*/
\n  // return  `/ms${$.shareData.var_setting.pagePath}/${modelName}/${operateName}`;/*dm*/
\n  // 回到最简单的规则： 模型名称/页面类型/*dm*/
\n  return `/${modelName}/${operateName}`/*dm*/
\n/*dm*/
\n}}",
				"description":"获取主子表操作路径",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_getOperatePath"
			},
			{
				"default":"${function fun_initTableView({viewCode, modelName, modelText, setting, querySorts = []}) {/*dm*/
\n  const config = {/*dm*/
\n    modelName,/*dm*/
\n    modelText,/*dm*/
\n    viewCode,/*dm*/
\n    setting: {/*dm*/
\n      'showPagination': false,/*dm*/
\n      'showRadio': false,/*dm*/
\n      'paginationMode': 'complete',/*dm*/
\n      'paginationAlign': 'right',/*dm*/
\n      'pageSizes': [10, 20, 30, 40, 50],/*dm*/
\n      'showHeader': true,/*dm*/
\n      'stripe': false,/*dm*/
\n      'showNo': true,/*dm*/
\n      'selectRowType': 'single',/*dm*/
\n      'border': false,/*dm*/
\n      'mode': 'normal',/*dm*/
\n      'batchDelFlag': false,/*dm*/
\n      'addFlag': true,/*dm*/
\n      'exportDataFlag': false,/*dm*/
\n      'headerEllipsis': true,/*dm*/
\n      'cellEllipsis': true,/*dm*/
\n      'textAlign': 'left',/*dm*/
\n      'showDetail': false,/*dm*/
\n      'showDel': true,/*dm*/
\n      'showEdit': true,/*dm*/
\n      'columnShowIcon': false,/*dm*/
\n      'columnShowText': true,/*dm*/
\n      'resizeColumn': true,/*dm*/
\n      ...setting/*dm*/
\n    },/*dm*/
\n    loading: false,/*dm*/
\n    params: {/*dm*/
\n      pageNum: 1,/*dm*/
\n      pageSize: Number(localStorage.getItem(`var_basePageParams_${viewCode}`)) || 10,/*dm*/
\n      totalPage:0,/*dm*/
\n      totalSize:0/*dm*/
\n    },/*dm*/
\n    searchQuery: {/*dm*/
\n      querySorts,/*dm*/
\n      queryConditions: [],/*dm*/
\n      combinationRule: ''/*dm*/
\n    },/*dm*/
\n    tableData: {/*dm*/
\n      viewCode,/*dm*/
\n      addDatas: [],/*dm*/
\n      updateDatas: [],/*dm*/
\n      deleteIds: [],/*dm*/
\n    },/*dm*/
\n    singleSelection: '',/*dm*/
\n    multipleSelection: [],/*dm*/
\n    columns:  [],/*dm*/
\n    tableList: [],/*dm*/
\n/*dm*/
\n  }/*dm*/
\n  this.$set($.shareData.var_tables, viewCode, config);/*dm*/
\n/*dm*/
\n}}",
				"description":"初始化table",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_initTableView"
			},
			{
				"default":"${function fun_tableDataTransform(type, modelName, data){/*dm*/
\n  // 数组转table数据/*dm*/
\n  if (type == 'input') {/*dm*/
\n    let transform = await $.global.functions.modelValueFormat(modelName)/*dm*/
\n    return data.map(item => {/*dm*/
\n      let res = transform.input(item);/*dm*/
\n      return res/*dm*/
\n    })/*dm*/
\n/*dm*/
\n  }/*dm*/
\n  // table数据转数组/*dm*/
\n  if(type == 'output') {/*dm*/
\n    let transform = await $.global.functions.modelValueFormat(modelName);/*dm*/
\n/*dm*/
\n    return transform.output(data);/*dm*/
\n/*dm*/
\n  }/*dm*/
\n/*dm*/
\n}}",
				"description":"",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_tableDataTransform"
			},
			{
				"default":"${function fun_getCustomColumn(viewCode){/*dm*/
\n/*dm*/
\n  const setting = $.shareData.var_setting.table;/*dm*/
\n/*dm*/
\n  let arr = []/*dm*/
\n  if (setting[viewCode]) {/*dm*/
\n    arr = setting[viewCode]?.customButtons || []/*dm*/
\n  } else {/*dm*/
\n    arr = []/*dm*/
\n  }/*dm*/
\n  return arr.filter(v => v.enable)/*dm*/
\n}}",
				"description":"获取具体视图表格下面的操作列的自定义按钮",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_getCustomColumn"
			}
		],
		"shareData":[
			{
				"default":"{\"modelName\":\"prompt_words_config\",\"pageKey\":\"956167632830300161\",\"pagePath\":\"/prompt_words_config/form\",\"enableTitle\":true,\"columnTotal\":3,\"group\":{},\"table\":{\"default\":{\"showPlanView\":true,\"edit\":true,\"detail\":false,\"delete\":true,\"copy\":false,\"customButtons\":[],\"columnCustomButtons\":[]}},\"detailPath\":\"/prompt_words_config/detail\",\"formPath\":\"/prompt_words_config/form\",\"listPath\":\"/prompt_words_config/list\"}",
				"key":"var_setting"
			},
			{
				"default":"{/*dm*/
\n  \"openDraft\": false,/*dm*/
\n  \"typeTitle\": \"新建\",/*dm*/
\n  \"isEdit\": false,/*dm*/
\n  \"title\": \"\",/*dm*/
\n  \"detailId\": \"\",/*dm*/
\n  \"showHeader\": true,/*dm*/
\n  \"isSubModel\": false,/*dm*/
\n  \"modelText\": \"\",/*dm*/
\n  \"wfOpen\": false,/*dm*/
\n  \"wfStatus\": false/*dm*/
\n}",
				"description":"页面信息",
				"global":false,
				"key":"var_pageInfo"
			},
			{
				"default":"{}",
				"description":"表单数据",
				"key":"var_saveForm"
			},
			{
				"default":false,
				"description":"表单是否提交中",
				"key":"var_saveLoading"
			},
			{
				"default":"[]",
				"description":"",
				"key":"var_viewDetailList"
			},
			{
				"default":"",
				"description":"关联模型form",
				"global":false,
				"key":"var_findRelatedForm"
			},
			{
				"default":"{/*dm*/
\n        modelName: \"\",/*dm*/
\n        fieldName: \"\"/*dm*/
\n      }",
				"key":"var_subModelInfo"
			},
			{
				"default":"{/*dm*/
\n  /*dm*/
\n}",
				"description":"所有table信息汇总",
				"global":false,
				"key":"var_tables"
			},
			{
				"default":"{/*dm*/
\n  \"viewCode\": \"\",/*dm*/
\n  \"modelName\": \"\",/*dm*/
\n  \"modelText\": \"\",/*dm*/
\n  \"fieldName\": \"\",/*dm*/
\n  \"visible\": false,/*dm*/
\n  \"loading\": true,/*dm*/
\n}",
				"description":"查找关系信息",
				"global":false,
				"key":"var_findRelationInfo"
			},
			{
				"default":"{/*dm*/
\n  \"conditionsValue\": [/*dm*/
\n    {/*dm*/
\n      \"conditionType\": \"LIKE\",/*dm*/
\n      \"fieldName\": \"name\",/*dm*/
\n      \"value\": \"\"/*dm*/
\n    }/*dm*/
\n  ],/*dm*/
\n  \"standardQueryConditions\": [/*dm*/
\n    {/*dm*/
\n      \"conditionType\": \"LIKE\",/*dm*/
\n      \"fieldName\": \"name\",/*dm*/
\n      \"value\": \"\"/*dm*/
\n    }/*dm*/
\n  ],/*dm*/
\n  \"standardCombinationRule\": \"\"/*dm*/
\n}",
				"key":"var_currentSearchCondition"
			},
			{
				"default":"[]",
				"description":"禁止操作的字段",
				"key":"var_disabledFormField"
			},
			{
				"default":"{/*dm*/
\n  \"subModelIDPrefix\": \"subModel_\"/*dm*/
\n}",
				"description":"页面常量",
				"key":"var_consts"
			}
		],
		"action":{
			"lifecycle":{
				"mounted":{
					"arguments":[
						""
					],
					"actions":[
						{
							"handler":"${function Fun1453(){/*dm*/
\n// 初始化页面规则，生成相关运行时函数，匹配到字段规则后/*dm*/
\nthis.ruleManager = {};/*dm*/
\ntry {/*dm*/
\n  this.ruleManager = $.global.functions.handleFormRules($.shareData.var_setting.rules);/*dm*/
\n/*dm*/
\n} catch (e) {/*dm*/
\n/*dm*/
\n}/*dm*/
\n/*dm*/
\n/*dm*/
\nconst bomPage = $.bom?.page;/*dm*/
\nconst pageParams = bomPage?.params()?.data;/*dm*/
\nconst pageQuery = bomPage?.query();/*dm*/
\n// 正常编辑页判断/*dm*/
\nlet detailId = pageParams?.detailId || pageQuery?.detailId;/*dm*/
\n/*dm*/
\nlet isSubModel = false;/*dm*/
\n/*dm*/
\nconst defaultVarSaveForm = pageParams?.defaultVarSaveForm;/*dm*/
\nif (defaultVarSaveForm) {/*dm*/
\n  detailId = pageParams?.detailId/*dm*/
\n  $.shareData.var_saveFormAction(defaultVarSaveForm)/*dm*/
\n}/*dm*/
\n/*dm*/
\nconst disabledFormField = pageParams?.disabledFormField;/*dm*/
\n/*dm*/
\nif (disabledFormField) {/*dm*/
\n  $.shareData.var_disabledFormFieldAction(disabledFormField)/*dm*/
\n}/*dm*/
\n/*dm*/
\n// 子表编辑判断/*dm*/
\nif (pageParams?.pageType == 'subModel') {/*dm*/
\n  detailId = pageParams?.detailId;/*dm*/
\n  isSubModel = true;/*dm*/
\n}/*dm*/
\n$.shareData.var_pageInfoAction({/*dm*/
\n  isSubModel/*dm*/
\n})/*dm*/
\nif (detailId) {/*dm*/
\n  $.shareData.var_pageInfoAction({/*dm*/
\n    detailId,/*dm*/
\n    isEdit: !!detailId,/*dm*/
\n    typeTitle: \"编辑\"/*dm*/
\n  })/*dm*/
\n}/*dm*/
\n/*dm*/
\n/*dm*/
\n// 初始化模型/*dm*/
\nconst res = await $.global.functions.getModelDictionaryInfo($.shareData.var_setting.modelName);/*dm*/
\n$.shareData.var_pageInfoAction({/*dm*/
\n  wfOpen: res?.wfOpen,/*dm*/
\n  wfStatus: true,/*dm*/
\n  openDraft: res.draft,/*dm*/
\n  modelText: res.modelText,/*dm*/
\n  title: $.shareData.var_pageInfo.typeTitle + res.modelText/*dm*/
\n})/*dm*/
\n// 设置标题/*dm*/
\n$.bom.getHostData(\"setTitle\", $.shareData.var_pageInfo.modelText + \"-\" + $.shareData.var_pageInfo.typeTitle);/*dm*/
\n/*dm*/
\n// 该页面是子模型时，需要初始化/*dm*/
\nif ($.shareData.var_pageInfo.isSubModel) {/*dm*/
\n  $.functions.fun_initSubModelPage()/*dm*/
\n}/*dm*/
\n/*dm*/
\n/*dm*/
\n/*dm*/
\n// 编辑页/*dm*/
\nif ($.shareData.var_pageInfo.isEdit) {/*dm*/
\n  await $.functions.fun_getFormEdit(detailId, pageParams?.formData);/*dm*/
\n}/*dm*/
\n/*dm*/
\n/*dm*/
\n/*dm*/
\nif (!$.shareData.var_pageInfo.isSubModel && !$.shareData.var_pageInfo.isEdit) {/*dm*/
\n  // 支持暂存处理/*dm*/
\n  await $.functions.fun_initDraftInfo();/*dm*/
\n  // 复制处理/*dm*/
\n  await $.functions.fun_initCopy()/*dm*/
\n}/*dm*/
\n// 详情页面或者编辑页面，新增时需要将查找关系数据处理一下/*dm*/
\nawait $.functions.fun_findRelatedQueryRestForm();/*dm*/
\n/*dm*/
\nawait $.functions.fun_initFormView($.shareData.var_setting.modelName);/*dm*/
\n/*dm*/
\nif (!$.shareData.var_pageInfo.isEdit) {/*dm*/
\n  return;/*dm*/
\n}/*dm*/
\n/*dm*/
\n/*dm*/
\n// await $.functions.fun_initFormView($.shareData.var_setting.modelName);/*dm*/
\n// 加载子模型数据/*dm*/
\nconst views = $.shareData.var_viewDetailList;/*dm*/
\nviews.map(view => {/*dm*/
\n  view.subModelList.map(model => {/*dm*/
\n    const tableKey = model.relatedListViewCode;/*dm*/
\n    let tableParams = {};/*dm*/
\n    if (detailId) {/*dm*/
\n      tableParams = {/*dm*/
\n        \"pageNum\": 1,/*dm*/
\n        \"pageSize\": 100,/*dm*/
\n        queryConditions: [{/*dm*/
\n          fieldName: model.fieldName,/*dm*/
\n          conditionType: \"EQ\",/*dm*/
\n          value: detailId/*dm*/
\n        }]/*dm*/
\n      }/*dm*/
\n    }/*dm*/
\n    $.functions.fun_loadTableData(model.modelName, tableKey, tableParams);/*dm*/
\n  });/*dm*/
\n});/*dm*/
\n/*dm*/
\n/*dm*/
\n}}",
							"type":"function",
							"tags":[]
						}
					]
				}
			},
			"on":{
				"findRelationCallback":{
					"arguments":[
						"data"
					],
					"actions":[
						{
							"handler":"${function Handler(){ $.shareData.var_findRelatedFormAction(data)}}",
							"type":"function"
						}
					]
				},
				"changeSetting":{
					"arguments":[
						"setting"
					],
					"actions":[
						{
							"handler":"${function Handler(){$.shareData.var_settingAction(setting)}}",
							"type":"function"
						}
					]
				}
			}
		},
		"id":"956167632830300161",
		"styleCode":".editPage {/*dm*/
\n  padding: 16px 24px 0px;/*dm*/
\n  position: relative;/*dm*/
\n  box-sizing: border-box;/*dm*/
\n  height: 100%;/*dm*/
\n  display: flex;/*dm*/
\n  flex-direction: column;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.editPage .el-form-item--small .el-form-item__content {/*dm*/
\n  line-height: 1;/*dm*/
\n  min-height: 32px/*dm*/
\n}/*dm*/
\n/*dm*/
\n.editPage .editPageForm .el-row {/*dm*/
\n  position: static;/*dm*/
\n  margin: 0 !important;/*dm*/
\n  display: block/*dm*/
\n}/*dm*/
\n/*dm*/
\n.editPage .editPageFooter {/*dm*/
\n  flex-shrink: 0;/*dm*/
\n  height: 64px;/*dm*/
\n  background: #fff;/*dm*/
\n  padding: 0 24px !important;/*dm*/
\n  display: flex;/*dm*/
\n  align-items: center;/*dm*/
\n  justify-content: flex-end;/*dm*/
\n  border-top: 1px solid #EBEEF5;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.tableScolle .el-table__body-wrapper {/*dm*/
\n    /* height: 81.5% !important; *//*dm*/
\n}/*dm*/
\n/*dm*/
\n.editPage .recordsDialogBox .el-dialog__body {/*dm*/
\n  height: 460px;/*dm*/
\n  max-height: 460px;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.editPage .el-collapse-item__content {/*dm*/
\n  padding: 16px 8px;/*dm*/
\n}/*dm*/
\n/*dm*/
\n.el-collapse-item__header {/*dm*/
\n  width: max-content;/*dm*/
\n}/*dm*/
\n/*dm*/
\n/* .recordsDialogBox .el-dialog__body {/*dm*/
\n  padding-bottom: 0px;/*dm*/
\n  max-height: none !important;/*dm*/
\n} *//*dm*/
\n/*dm*/
\n.joy-condition .searchBase {/*dm*/
\n  border-radius: 8px;/*dm*/
\n}",
		"apiConfigs":[
			{
				"default":"{code:200,msg:'',data:''}",
				"transform":"function filter_aeZFJf(data) {return data;}",
				"method":"POST",
				"isShareData":true,
				"name":"api_saveData",
				"description":"新建行数据",
				"global":false,
				"url":"/api/v1/model/:modelName"
			},
			{
				"transform":"function filter_ZdrnTZ(data) {\r/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果\r/*dm*/
\n  return data;\r/*dm*/
\n}",
				"method":"POST",
				"isShareData":true,
				"name":"api_getPreUploadUrl",
				"description":"预上传接口",
				"url":"/api/v1/attachment/preUpload"
			},
			{
				"transform":"function filter_jstRNX(data) {\r/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果\r/*dm*/
\n  return data;\r/*dm*/
\n}",
				"method":"GET",
				"isShareData":true,
				"name":"api_getCommonDraftInfo",
				"description":"根据id查询查询暂存想信息",
				"url":"/api/v1/model/common_draft/:id"
			},
			{
				"transform":"function filter_jstRNX(data) {\r/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果\r/*dm*/
\n  return data;\r/*dm*/
\n}",
				"method":"POST",
				"isShareData":true,
				"name":"api_postCommonDraft",
				"description":"暂存",
				"url":"/api/v1/model/common_draft"
			},
			{
				"default":"{\"code\":200,\"msg\":\"\",\"data\":{}}",
				"transform":"",
				"isModel":true,
				"method":"GET",
				"isShareData":true,
				"name":"api_getInfo",
				"description":"查找关系获取详情信息",
				"url":"/api/v1/model/:modelName/:id"
			},
			{
				"transform":"function filter_Y6biFKTj(data) {\r/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果\r/*dm*/
\n  return data;\r/*dm*/
\n}",
				"method":"GET",
				"isShareData":true,
				"name":"api_getDefaultData",
				"description":"获取模型的一条缺省数据",
				"global":false,
				"url":"/api/v1/dev/model/:modelName/default"
			},
			{
				"transform":"function filter_4MQRY8Z2(data) {\r/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果\r/*dm*/
\n  return data;\r/*dm*/
\n}",
				"method":"GET",
				"isShareData":true,
				"name":"api_getReferencedInfo",
				"description":"根据关系获取引用字段信息",
				"global":false,
				"url":"/api/v1/field/:modelName/:fieldName/:id/quote/linkage"
			},
			{
				"default":"{code:200,msg:'',data:{totalSize:0,records: []}}",
				"transform":"function filter_aeZFJf(data) {/*dm*/
\n        return JSON.parse(JSON.stringify(data));/*dm*/
\n      }",
				"method":"POST",
				"isShareData":true,
				"name":"api_getList",
				"description":"获取表格数据",
				"url":"/api/v1/model/:modelName/list"
			},
			{
				"default":"[]",
				"transform":"function filter_i7SH2hSN(data) {/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果/*dm*/
\n  return data.data;/*dm*/
\n}",
				"method":"GET",
				"isShareData":true,
				"name":"api_getFormPageInfo",
				"description":"获取页面信息",
				"global":false,
				"url":"/api/v2/views/:modelName/:pageKey/form_view"
			}
		],
		"key":1751873930993,
		"attrs":{
			"style":{
				"height":"100%"
			},
			"setting":"{\"name\": \"page\"}"
		}
	},
	"editorSettingDsl":"{\"enableTitle\":true,\"columnTotal\":3,\"group\":{},\"table\":{\"default\":{\"showPlanView\":true,\"edit\":true,\"detail\":false,\"delete\":true,\"copy\":false,\"customButtons\":[],\"columnCustomButtons\":[]}}}",
	"pageInterceptor":"",
	"pageSource":"model",
	"frame":"vue"
}