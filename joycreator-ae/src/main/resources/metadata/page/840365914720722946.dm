{
	"usePlatform":"",
	"enableAdvanced":true,
	"componentCode":"ipms@1.0.0",
	"editable":false,
	"pageTemplate":"PC标准增删改查模板",
	"edition":"v2",
	"pageKey":"840365914720722946",
	"pageSettingDsl":"{\"detailPath\":\"/ipms_user/detail\",\"formPath\":\"/ipms_user/form\",\"listPath\":\"/ipms_user/list\",\"modelName\":\"ipms_user\",\"pageKey\":\"840365914720722946\",\"pagePath\":\"/ipms_user/detail\"}",
	"pageName":"用户_详情",
	"pageTemplateCode":"PCDefault",
	"modelName":"ipms_user",
	"pageType":"detail_v4",
	"pageDsl":{
		"components":[
			{
				"componentType":"custom",
				"children":[
					{
						"children":[
							{
								"children":[
									{
										"libraryName":"@jd/joyui",
										"componentType":"UILibrary",
										"children":[
											{
												"componentType":"custom",
												"children":[
													{
														"libraryName":"@jd/joyui",
														"componentType":"UILibrary",
														"action":{
															"fire":{
																"navtoDetail":{
																	"arguments":[
																		"field",
																		"modelName"
																	],
																	"actions":[
																		{
																			"handler":"${function Fun67674(field, modelName){const fieldProps = __item.fieldProps;/*dm*/
\n/*dm*/
\nlet detailPath = $.shareData.var_setting.detailPath;/*dm*/
\n/*dm*/
\nif(fieldProps.fieldName != 'name') {/*dm*/
\n  detailPath = `/${fieldProps.relatedModel.modelName}/detail`;/*dm*/
\n}/*dm*/
\n/*dm*/
\nconst detailId = $.shareData.var_formInfo[__item.fieldName]?.id;/*dm*/
\n/*dm*/
\n// 兼容查找关系/*dm*/
\n$.bom.page.open(detailPath + '?detailId=' + detailId + '', {}, async () => {/*dm*/
\n/*dm*/
\n}, {});/*dm*/
\n}}",
																			"type":"function"
																		}
																	]
																},
																"handleDownload":{
																	"arguments":[
																		"file"
																	],
																	"actions":[
																		{
																			"handler":"${function Fun_wBytKP(file){/*dm*/
\nawait $.shareData.api_preDownloadAction({ originFileName: file.originFileName })/*dm*/
\n/*dm*/
\nfetch($.shareData.api_preDownload.presignedObjectUrl, {/*dm*/
\n  method: 'GET',/*dm*/
\n  responseType: 'blob',/*dm*/
\n}).then(/*dm*/
\n  res => {/*dm*/
\n    return res.blob();/*dm*/
\n  })/*dm*/
\n  .then(blob => {/*dm*/
\n/*dm*/
\n    let bl = new Blob([blob]);/*dm*/
\n/*dm*/
\n    var link = document.createElement('a');/*dm*/
\n/*dm*/
\n    link.href = window.URL.createObjectURL(bl);/*dm*/
\n/*dm*/
\n    link.download = file.name;/*dm*/
\n/*dm*/
\n    link.click();/*dm*/
\n/*dm*/
\n    window.URL.revokeObjectURL(link.href);/*dm*/
\n/*dm*/
\n  }).catch(err => {/*dm*/
\n    console.log('error', error)/*dm*/
\n  });/*dm*/
\n}}",
																			"type":"function"
																		}
																	]
																}
															}
														},
														"id":"ModelField-pAyPT3Ex",
														"componentName":"ModelField",
														"title":"ModelField表单子项",
														"version":"1.8.3",
														"props":{
															"formType":"info",
															"originProps":"${() => {/*dm*/
\n  return {/*dm*/
\n    column: $.shareData.var_setting.group?.[group.groupCode]?.column || $.shareData.var_setting?.column || 3/*dm*/
\n  }/*dm*/
\n}}",
															"fieldProps":"${__item.fieldProps}",
															"businessType":"${__item.businessType}",
															"value":"${__item.value}"
														},
														"attrs":{
															"style":{}
														}
													}
												],
												"id":"VFor-tkCA7JYy",
												"componentName":"VFor",
												"title":"Model循环容器",
												"props":{
													"forEach":"${() => {/*dm*/
\n  console.log(group.fieldList, 'nnnnnn')/*dm*/
\n  return group.fieldList;/*dm*/
\n}}",
													"forEachKey":"__item",
													"index":"$fieldIndex"
												}
											}
										],
										"id":"DivWrapper-CfGsXCcW",
										"componentName":"ModelForm",
										"title":"表单组件",
										"version":"1.8.3",
										"props":{
											"ref":"infoFormRef",
											"size":"small",
											"labelPosition":"left"
										},
										"attrs":{
											"class":"editPageForm"
										}
									},
									{
										"children":[
											{
												"componentType":"custom",
												"children":[
													{
														"componentType":"custom",
														"children":[
															{
																"componentType":"custom",
																"componentName":"TextWrapper",
																"id":"TextWrapper-dNQaEPnS",
																"props":{
																	"children":"${__subModel.relatedShowName}"
																},
																"attrs":{
																	"style":{
																		"border-left":"3px solid #4871E4",
																		"display":"inline-block",
																		"padding-left":"4px",
																		"line-height":1,
																		"height":"14px"
																	},
																	"class":""
																}
															}
														],
														"componentName":"DivWrapper",
														"id":"DivWrapper-PMzRBxDE",
														"attrs":{
															"style":{
																"display":"flex",
																"justify-content":"space-between",
																"align-items":"center",
																"margin-bottom":"8px"
															}
														}
													},
													{
														"componentType":"custom",
														"children":[
															{
																"libraryName":"@jd/joyui",
																"componentType":"UILibrary",
																"__setting":{
																	"settingData":{
																		"pageSizes":"${[10,20,30,40]}",
																		"editPage":"undefined",
																		"headerEllipsis":true,
																		"resizeColumn":true,
																		"pageSize":10,
																		"showRefresh":true,
																		"batchDelFlag":true,
																		"buttonShowText":true,
																		"mode":"normal",
																		"columnShowIcon":false,
																		"showPlanView":true,
																		"stripe":true,
																		"addPage":"undefined",
																		"paginationMode":"complete",
																		"showOverflowTooltip":true,
																		"border":false,
																		"showEdit":true,
																		"columnShowText":true,
																		"buttonShowIcon":false,
																		"textAlign":"left",
																		"paginationAlign":"right",
																		"showPagination":true,
																		"addFlag":true,
																		"showDetail":true,
																		"exportDataFlag":false,
																		"cellEllipsis":true,
																		"showNo":true,
																		"showDel":true,
																		"detailPage":"undefined",
																		"draftFlag":false
																	}
																},
																"children":[
																	{
																		"componentType":"custom",
																		"children":[
																			{
																				"componentType":"custom",
																				"children":[
																					{
																						"componentType":"custom",
																						"children":[
																							{
																								"componentType":"custom",
																								"id":"TextWrapper-RSSSbkzD",
																								"componentName":"TextWrapper",
																								"attrs":{
																									"style":{
																										"color":"#909399",
																										"font-weight":"normal",
																										"display":"block",
																										"width":"100%",
																										"font-size":"14px",
																										"line-height":"66px"
																									}
																								},
																								"props":{
																									"children":" 暂无数据"
																								},
																								"chosen":false
																							}
																						],
																						"id":"DivWrapper-EBpNZiaF",
																						"componentName":"DivWrapper",
																						"title":"暂无数据",
																						"attrs":{
																							"style":{
																								"background-color":"#ffffff",
																								"width":"100%",
																								"height":"100%"
																							}
																						},
																						"props":{
																							"name":"DivWrapper",
																							"slot":"empty"
																						},
																						"chosen":false
																					},
																					{
																						"componentType":"custom",
																						"children":[
																							{
																								"componentType":"custom",
																								"componentName":"TableColumn",
																								"id":"TableColumn-K3Yr5wTY",
																								"props":{
																									"show-overflow-tooltip":true,
																									"reserve-selection":true,
																									":selectable":"${functions(scope){ return scope['$extra']?.dataPermission ==2 }}",
																									"width":"48",
																									"fixed":"left",
																									"type":"selection",
																									"align":"center",
																									"v-slot":"scope"
																								}
																							}
																						],
																						"id":"VIf-A2pkdsHf",
																						"componentName":"VIf",
																						"props":{
																							"conditions":"${[ $.shareData.var_basePageSetting[__subModel.tableKey]?.selectRowType==='many' || $.shareData.var_basePageSetting[__subModel.tableKey]?.batchDelFlag]}"
																						}
																					},
																					{
																						"componentType":"custom",
																						"children":[
																							{
																								"componentType":"custom",
																								"componentName":"TableColumn",
																								"id":"TableColumn-mrmQamiy",
																								"props":{
																									"resizable":false,
																									"width":"60",
																									"fixed":"left",
																									"label":"序号",
																									"type":"index",
																									"align":"left",
																									"class-name":"color-text-secondary"
																								}
																							}
																						],
																						"id":"VIf-zmSeeyG7",
																						"componentName":"VIf",
																						"props":{
																							"conditions":"${[$.shareData.var_basePageSetting[__subModel.tableKey]?.showNo]}"
																						}
																					},
																					{
																						"componentType":"custom",
																						"children":[
																							{
																								"componentType":"custom",
																								"children":[
																									{
																										"libraryName":"@jd/joyui",
																										"componentType":"UILibrary",
																										"action":{
																											"fire":{
																												"navtoDetail":{
																													"arguments":[
																														"field",
																														"modelName"
																													],
																													"actions":[
																														{
																															"handler":"${function Fun62526(field, modelName){let pageId = $.functions.fun_getTableNavUrl('ms', __subModel.modelName, \"detail\")/*dm*/
\nif (modelName) {/*dm*/
\n  pageId = $.functions.fun_getTableNavUrl(\"\", modelName, \"detail\")/*dm*/
\n}/*dm*/
\nconst detailId = scope.row[item.name]?.id || scope.row.id; // 兼容查找关系/*dm*/
\n$.bom.page.open(pageId + '?detailId=' + detailId + '', {}, async () => {/*dm*/
\n  if ($.shareData.var_tabActiveName !== 'first') {/*dm*/
\n    await $.functions.fun_loadCache(__subModel.tableKey, $.shareData.var_tabActiveName);/*dm*/
\n    $.functions.fun_loadTableData(__subModel.modelName, __subModel.tableKey)/*dm*/
\n  }/*dm*/
\n}, {});/*dm*/
\n}}",
																															"type":"function"
																														}
																													]
																												},
																												"handleDownload":{
																													"arguments":[
																														"file"
																													],
																													"actions":[
																														{
																															"handler":"${function handler(file){/*dm*/
\nawait $.shareData.preDownloadAction({ originFileName: file.originFileName })/*dm*/
\n/*dm*/
\nfetch($.shareData.preDownload.presignedObjectUrl, {/*dm*/
\n  method: 'GET',/*dm*/
\n  responseType: 'blob',/*dm*/
\n}).then(/*dm*/
\n  res => {/*dm*/
\n    return res.blob();/*dm*/
\n  })/*dm*/
\n  .then(blob => {/*dm*/
\n/*dm*/
\n    let bl = new Blob([blob]);/*dm*/
\n/*dm*/
\n    var link = document.createElement('a');/*dm*/
\n/*dm*/
\n    link.href = window.URL.createObjectURL(bl);/*dm*/
\n/*dm*/
\n    link.download = file.name;/*dm*/
\n/*dm*/
\n    link.click();/*dm*/
\n/*dm*/
\n    window.URL.revokeObjectURL(link.href);/*dm*/
\n/*dm*/
\n  }).catch(err => {/*dm*/
\n    console.log('error', error)/*dm*/
\n  });/*dm*/
\n}}",
																															"type":"function"
																														}
																													]
																												}
																											}
																										},
																										"componentName":"ModelField",
																										"id":"ModelField-dChxBxQp",
																										"title":"模型字段",
																										"version":"1.8.3",
																										"props":{
																											"originProps":{},
																											"formType":"table",
																											"fieldProps":"${item.modelField}",
																											"v-open":"${ () => {/*dm*/
\n                if (true) {/*dm*/
\n                  // console.error('子模型新建或编辑时， 不支持跳转啊');/*dm*/
\n                  return;/*dm*/
\n                }/*dm*/
\n                if(!$.shareData.var_baseFindRelatedPages[__subModel.modelName] || !item?.name) {/*dm*/
\n                  return;/*dm*/
\n                }/*dm*/
\n                const pageId = $.shareData.var_baseFindRelatedPages[__subModel.modelName][item.name];/*dm*/
\n                if (!pageId) {/*dm*/
\n                  return;/*dm*/
\n                }/*dm*/
\n                const detailId = scope.row[item.name]?.id || scope.row.id;/*dm*/
\n                const jumpUrl = pageId + '?detailId=' + detailId + ''/*dm*/
\n                return jumpUrl/*dm*/
\n              }}",
																											"businessType":"${item.modelField.businessType}",
																											"value":"${scope.row[item.name]}"
																										}
																									}
																								],
																								"componentName":"TableColumn",
																								"id":"TableColumn-dpGzPZhC",
																								"props":{
																									"show-overflow-tooltip":"${$.shareData.var_basePageSetting[__subModel.tableKey].cellEllipsis}",
																									"prop":"${item.name}",
																									"fixed":false,
																									"label":"${item.label}",
																									"sortable":false,
																									"align":"${$.shareData.var_basePageSetting[__subModel.tableKey].textAlign}",
																									"v-slot":"scope",
																									"min-width":"${() => {\r/*dm*/
\n  if (item?.modelField.businessType === 'dateTime') {\r/*dm*/
\n    return item?.displayWidth || 180\r/*dm*/
\n  }\r/*dm*/
\n  return item?.displayWidth || 140\r/*dm*/
\n}}"
																								}
																							}
																						],
																						"id":"VFor-TwhBmStD",
																						"componentName":"VFor",
																						"title":"table列循环容器",
																						"props":{
																							"forEach":"${() => {/*dm*/
\n  console.log(__subModel.tableKey)/*dm*/
\n/*dm*/
\n  console.log($.shareData.var_baseTableColumnShow)/*dm*/
\n/*dm*/
\n  return $.shareData.var_baseTableColumnShow[__subModel.tableKey];/*dm*/
\n}}",
																							"forEachKey":"item",
																							"index":"$index"
																						}
																					},
																					{
																						"componentType":"custom",
																						"children":[
																							{
																								"componentType":"custom",
																								"children":[
																									{
																										"libraryName":"@jd/joyui",
																										"componentType":"UILibrary",
																										"action":{
																											"fire":{
																												"tab-click":{
																													"arguments":[
																														"tab"
																													],
																													"actions":[
																														{
																															"handler":"${function handler(tab){/*dm*/
\nconst modelName = __subModel.modelName;/*dm*/
\nconst detailId = scope.row?.id;/*dm*/
\n/*dm*/
\nconst detailPage = $.functions.fun_getTableNavUrl('ms', __subModel.modelName, 'detail')/*dm*/
\nconst addPage = $.functions.fun_getTableNavUrl('ms', __subModel.modelName, 'form')/*dm*/
\nif (tab == 'detail' && detailId) {/*dm*/
\n  $.bom.page?.open(detailPage, {/*dm*/
\n    detailId,/*dm*/
\n    pageType: \"dialog\"/*dm*/
\n  }, async (data) => {/*dm*/
\n  }, {/*dm*/
\n    options: {/*dm*/
\n      width: '960px',/*dm*/
\n      title: '详情',/*dm*/
\n      closeOnClickModal: false,/*dm*/
\n      customClass: 'sub-model-dialog',/*dm*/
\n    },/*dm*/
\n    type: 'dialog'/*dm*/
\n  });/*dm*/
\n}/*dm*/
\nif (tab == 'edit') {/*dm*/
\n  if (detailId) {/*dm*/
\n    $.bom.page?.open(addPage, {/*dm*/
\n      detailId,/*dm*/
\n      pageType: 'subModel',/*dm*/
\n      subModel: __subModel,/*dm*/
\n      formData: scope.row/*dm*/
\n    }, async (data) => {/*dm*/
\n      if (!data) return;/*dm*/
\n      const tableKey = __subModel.modelName + '|' + __subModel.fieldName;/*dm*/
\n      console.log('编辑子模型回调', data, scope.$index);/*dm*/
\n      // 编辑/*dm*/
\n      const oldRow = scope.row;/*dm*/
\n      const rowIndex = scope.$index;/*dm*/
\n      let transform = await $.global.functions.modelValueFormat(__subModel.modelName)/*dm*/
\n/*dm*/
\n      // 保存编辑数据/*dm*/
\n      // this.$set($.shareData.var_baseTableList[tableKey], rowIndex, transform.input({/*dm*/
\n      //   ...oldRow,/*dm*/
\n      //   ...data/*dm*/
\n      // }));/*dm*/
\n      // 显示列表/*dm*/
\n      this.$set($.shareData.var_baseTableList[tableKey], rowIndex, {/*dm*/
\n        ...oldRow,/*dm*/
\n        ...data/*dm*/
\n      });/*dm*/
\n      console.log('111', $.shareData.var_baseTableList[tableKey]);/*dm*/
\n      if (data.id.indexOf('subModel') > -1) {/*dm*/
\n        const [name, addIndex] = data.id.split('_');/*dm*/
\n        const newRow = {/*dm*/
\n          ...oldRow,/*dm*/
\n          ...data/*dm*/
\n        }/*dm*/
\n        delete newRow.id;/*dm*/
\n        $.shareData.var_subModelTable[tableKey].addDatas[addIndex] = transform.output(newRow);/*dm*/
\n      } else {/*dm*/
\n        // 单独记录修改行/*dm*/
\n        const updateRowKey = $.shareData.var_subModelTable[tableKey].updateDatas.findIndex(i => i.id == data.id);/*dm*/
\n        if (updateRowKey == -1) {/*dm*/
\n          $.shareData.var_subModelTable[tableKey].updateDatas.push(transform.output(data));/*dm*/
\n        } else {/*dm*/
\n          $.shareData.var_subModelTable[tableKey].updateDatas[updateRowKey] = transform.output(data);/*dm*/
\n        }/*dm*/
\n      }/*dm*/
\n    }, {/*dm*/
\n      options: {/*dm*/
\n        width: '960px',/*dm*/
\n        title: '编辑',/*dm*/
\n        closeOnClickModal: false,/*dm*/
\n        customClass: 'sub-model-dialog',/*dm*/
\n      },/*dm*/
\n      type: 'dialog'/*dm*/
\n    });/*dm*/
\n  };/*dm*/
\n}/*dm*/
\nif (tab == 'delete') {/*dm*/
\n  this.$confirm('此操作将永久删除, 是否继续?', '提示', {/*dm*/
\n    confirmButtonText: '确定',/*dm*/
\n    cancelButtonText: '取消',/*dm*/
\n    type: 'warning'/*dm*/
\n  }).then(() => {/*dm*/
\n    // 删除本地缓存数据/*dm*/
\n    const rowKey = scope.$index;/*dm*/
\n    const tableKey = __subModel.modelName + '|' + __subModel.fieldName;/*dm*/
\n    $.shareData.var_baseTableList[tableKey].splice(rowKey, 1);/*dm*/
\n    if (scope.row?.id.indexOf('subModel') > -1) {/*dm*/
\n      const [, addRowKey] = scope.row?.id.split('_');/*dm*/
\n      $.shareData.var_subModelTable[tableKey].addDatas.splice(addRowKey, 1);/*dm*/
\n    }/*dm*/
\n    // 添加子模型表单数据/*dm*/
\n    $.shareData.var_subModelTable[tableKey].deleteIds.push(detailId)/*dm*/
\n  });/*dm*/
\n}/*dm*/
\n}}",
																															"type":"function"
																														}
																													]
																												}
																											}
																										},
																										"id":"MoreButton-N4R8HhTn",
																										"componentName":"MoreButton",
																										"version":"1.8.3",
																										"props":{
																											"styleState":"${[]}",
																											"padding":"0px 16px 0px 0px",
																											"data":"${() => {/*dm*/
\n  let list = [];/*dm*/
\n  const tableKey = __subModel.tableKey;/*dm*/
\n  let edit = false ? $.shareData.permissionData?.edit : true;/*dm*/
\n  let detail = false ? $.shareData.permissionData?.detail : true;/*dm*/
\n  let deletes = false ? $.shareData.permissionData?.delete : true;/*dm*/
\n  if ($.shareData.var_basePageSetting[tableKey]?.showEdit && edit) {/*dm*/
\n    list.push({ \"label\": \"编辑\", \"name\": \"edit\", \"type\": \"text\", \"round\": \"round\", \"size\": \"small\" })/*dm*/
\n  };/*dm*/
\n  if ($.shareData.var_basePageSetting[tableKey]?.showDetail && detail) {/*dm*/
\n    list.push({ \"label\": \"详情\", \"name\": \"detail\", \"type\": \"text\", \"round\": \"round\", \"size\": \"small\" })/*dm*/
\n  };/*dm*/
\n  if ($.shareData.var_basePageSetting[tableKey]?.showDel && deletes) {/*dm*/
\n    list.push({ \"label\": \"删除\", \"name\": \"delete\", \"type\": \"text\", \"round\": \"round\", \"size\": \"small\" });/*dm*/
\n  };/*dm*/
\n  return list;/*dm*/
\n}}",
																											"moreRound":"${false}",
																											"max-count":"${3}",
																											"textAlign":"left",
																											"marginLeft":"${0 * 1}"
																										}
																									}
																								],
																								"componentName":"TableColumn",
																								"id":"TableColumn-xXmNnPDx",
																								"props":{
																									"prop":"action",
																									"fixed":"right",
																									"label":"操作",
																									"v-slot":"scope",
																									"min-width":"172px"
																								}
																							}
																						],
																						"componentName":"VIf",
																						"id":"VIf-fyE8GSTr",
																						"props":{
																							"conditions":"${[ $.shareData.var_basePageSetting[__subModel.tableKey]?.mode==='normal']}"
																						}
																					}
																				],
																				"action":{
																					"fire":{
																						"header-dragend":{
																							"arguments":[
																								"newWidth",
																								"oldWidth",
																								"column",
																								"event"
																							],
																							"actions":[
																								{
																									"handler":"${function handler(newWidth,oldWidth,column,event){/*dm*/
\n                            const genId = $.functions.fun_genTableId(__subModel.tableKey);\r/*dm*/
\n/*dm*/
\n                            const { property } = column\r/*dm*/
\n\r/*dm*/
\n/*dm*/
\n                            const { businessType, fieldWidth, minWidth } = $.functions.getFieldsBusinessType(__subModel.tableKey, property);\r/*dm*/
\n\r/*dm*/
\n/*dm*/
\n                            const minNewWidth = Math.max(minWidth, newWidth)\r/*dm*/
\n\r/*dm*/
\n/*dm*/
\n                            const newColumnWidth = {\r/*dm*/
\n  ...$.shareData.tableColumnWidth.widths,\r/*dm*/
\n  [property]: minNewWidth\r/*dm*/
\n}\r/*dm*/
\n/*dm*/
\n                            $.shareData.tableColumnWidth.widths = newColumnWidth;\r/*dm*/
\n/*dm*/
\n                            // 修复数据、重置表格宽度\r/*dm*/
\n/*dm*/
\n                              this.$refs['jtable'].store.states._columns = this.$refs['jtable']?.store.states._columns.map(item => {\r/*dm*/
\n/*dm*/
\n                              if (item.id === column.id && item.minWidth && item.width) {\r/*dm*/
\n/*dm*/
\n                                return {\r/*dm*/
\n        ...item,\r/*dm*/
\n        realWidth: minNewWidth,\r/*dm*/
\n        width: minNewWidth\r/*dm*/
\n      }\r/*dm*/
\n/*dm*/
\n                              }\r/*dm*/
\n    return item\r/*dm*/
\n  })\r/*dm*/
\n/*dm*/
\n                              this.$refs['jtable']?.store?.scheduleLayout(true);\r/*dm*/
\n/*dm*/
\n                              this.$nextTick(() => {\r/*dm*/
\n    // 更新列的宽度\r/*dm*/
\n/*dm*/
\n                                this.$refs['jtable'].layout.updateColumnsWidth();\r/*dm*/
\n/*dm*/
\n                              })\r/*dm*/
\n/*dm*/
\n                              localStorage.setItem(genId, JSON.stringify(newColumnWidth))}/*dm*/
\n                          }",
																									"type":"function"
																								}
																							]
																						},
																						"current-change":{
																							"arguments":[
																								"val"
																							],
																							"actions":[
																								{
																									"handler":"${function Fun57084(val){/*dm*/
\n                                if($.shareData.var_basePageSetting[__subModel.tableKey]?.selectRowType==='single'){/*dm*/
\n                                  $.shareData.singleSelectionAction(val.id)/*dm*/
\n                                }/*dm*/
\n                              }}",
																									"type":"function"
																								}
																							]
																						},
																						"sort-change":{
																							"arguments":[
																								"column",
																								"prop"
																							],
																							"actions":[
																								{
																									"handler":"${function Fun_47sXNY(column, prop){/*dm*/
\n                              if ($.shareData.var_baseSearchQuery[__subModel.tableKey]?.querySorts.length > 1){/*dm*/
\n/*dm*/
\n                                $.shareData.var_baseSearchQuery[__subModel.tableKey]?.querySorts.splice(0, 1);/*dm*/
\n/*dm*/
\n                              }/*dm*/
\n/*dm*/
\n/*dm*/
\n/*dm*/
\n                              if (column.order){/*dm*/
\n/*dm*/
\n                                $.shareData.var_baseSearchQuery[__subModel.tableKey]?.querySorts.unshift({/*dm*/
\n/*dm*/
\n                                  fieldName: column.prop,/*dm*/
\n/*dm*/
\n                                  desc: column.order == \"descending\"/*dm*/
\n/*dm*/
\n                                })/*dm*/
\n/*dm*/
\n                              }/*dm*/
\n    /*dm*/
\n/*dm*/
\n/*dm*/
\n                              await $.functions.fun_loadTableData(__subModel.modelName, __subModel.tableKey);/*dm*/
\n/*dm*/
\n                          }}",
																									"type":"function"
																								}
																							]
																						},
																						"selection-change":{
																							"arguments":[
																								"selection"
																							],
																							"actions":[
																								{
																									"handler":"${function Fun2186(selection){/*dm*/
\n                                  $.shareData.var_baseMultipleSelectionAction({/*dm*/
\n                                    [__subModel.tableKey]: selection/*dm*/
\n                                  })/*dm*/
\n                              }}",
																									"type":"function"
																								}
																							]
																						}
																					}
																				},
																				"id":"Table-bwZbfCip",
																				"componentName":"Table",
																				"title":"表格区域",
																				"attrs":{
																					"style":{},
																					"class":"${$.shareData.var_basePageSetting[__subModel.tableKey].headerEllipsis?'headerEllipsis':''}",
																					"setting":"${JSON.stringify({/*dm*/
\n  \"name\": \"table\", pageKey: $.shareData.var_setting.pageKey, /*dm*/
\n  \"editorViewCode\": __subModel.tableKey,/*dm*/
\n  \"modelName\": __subModel.modelName/*dm*/
\n})}"
																				},
																				"props":{
																					"border":"${false}",
																					"max-height":"400px",
																					"headerEllipsis":"${$.shareData.var_basePageSetting[__subModel.tableKey].headerEllipsis}",
																					"data":"${$.shareData.var_baseTableList[__subModel.tableKey]}",
																					"show-header":"${$.shareData.var_basePageSetting[__subModel.tableKey].showHeader}",
																					"ref":"${__subModel.tableKey}",
																					"size":"small",
																					"v-loading":"${$.shareData.var_baseTableLoading[__subModel.tableKey]}",
																					"tooltip-effect":"dark",
																					"tree-props":"${{ hasChildren: \"hasChildren\", children: \"children\" }}",
																					"stripe":"${$.shareData.var_basePageSetting[__subModel.tableKey].stripe}",
																					"style":"width: 100%",
																					"height":"",
																					"row-key":"id",
																					"highlight-current-row":"${$.shareData.var_basePageSetting[__subModel.tableKey]?.selectRowType==='single'}"
																				}
																			}
																		],
																		"componentName":"DivWrapper",
																		"id":"DivWrapper-N8niznRJ",
																		"title":"表格+操作按钮",
																		"props":{
																			"name":"DivWrapper"
																		},
																		"attrs":{
																			"class":"records-joy-table-body"
																		}
																	}
																],
																"action":{
																	"fire":{
																		"handleSizeChange":{
																			"arguments":[
																				"val"
																			],
																			"actions":[
																				{
																					"handler":"${function Fun50593(val){/*dm*/
\n                            $.shareData.var_basePageParams[__subModel.tableKey].pageSize = val;/*dm*/
\n                            localStorage.setItem(`var_basePageParams_${table}`, JSON.stringify({/*dm*/
\n                              pageSize: val/*dm*/
\n                            }))/*dm*/
\n                            $.functions.fun_loadTableData(__subModel.modelName, __subModel.tableKey)/*dm*/
\n                          }}",
																					"type":"function"
																				}
																			]
																		},
																		"handleCurrentChange":{
																			"arguments":[
																				"value"
																			],
																			"actions":[
																				{
																					"handler":"${function Fun59331(value){$.shareData.var_basePageParams[__subModel.tableKey].pageNum = value;/*dm*/
\n$.functions.fun_loadTableData(__subModel.modelName, __subModel.tableKey);/*dm*/
\n}}",
																					"type":"function"
																				}
																			]
																		}
																	}
																},
																"id":"ModelTable-K3mNAR6i",
																"componentName":"ModelTable",
																"title":"列表组件",
																"version":"1.8.3",
																"props":{
																	"pageSizes":"${$.shareData.var_basePageSetting[__subModel.tableKey]?.pageSizes}",
																	"editPage":"undefined",
																	"buttonShowIcon":false,
																	"showPagination":false,
																	"showRefresh":false,
																	"batchDelFlag":false,
																	"buttonShowText":"",
																	"addFlag":true,
																	"mode":"${$.shareData.var_basePageSetting[__subModel.tableKey]?.mode}",
																	"columnShowIcon":false,
																	"modelName":"__subModel.modelName",
																	"exportDataFlag":false,
																	"ref":"record-table",
																	"showPlanView":true,
																	"pageParams":"${$.shareData.var_basePageParams[__subModel.tableKey]}",
																	"addPage":"undefined",
																	"paginationMode":"${$.shareData.var_basePageSetting[__subModel.tableKey]?.paginationMode}",
																	"detailPage":"undefined",
																	"model":"multiple",
																	"draftFlag":false,
																	"multipleSelection":"${$.shareData.var_baseMultipleSelection[__subModel.tableKey]}"
																},
																"attrs":{
																	"style":{
																		"height":"100%"
																	},
																	"class":"tableScolle sub-model-table"
																}
															}
														],
														"componentName":"DivWrapper",
														"id":"DivWrapper-X6s2seX8"
													}
												],
												"componentName":"DivWrapper",
												"id":"DivWrapper-XEBWNTBa",
												"attrs":{
													"style":{
														"padding":"0 8px",
														"clear":"both",
														"margin-bottom":"16px"
													},
													"class":"sub-model-root"
												}
											}
										],
										"id":"VFor-mYnMBJZK",
										"componentName":"VFor",
										"props":{
											"forEach":"${group.subModelList}",
											"forEachKey":"__subModel",
											"index":"$index"
										}
									}
								],
								"componentName":"CollapseItem",
								"id":"CollapseItem-KEicFwtM",
								"props":{
									"name":"${group.groupName}",
									"title":"${group.groupName}"
								},
								"attrs":{
									"style":{
										"width":"100%",
										"marginBottom":"20px"
									},
									"class":"",
									"setting":"${JSON.stringify({ name: 'group-config', groupCode: group.groupCode, pageKey: $.shareData.var_setting.pageKey })}"
								}
							}
						],
						"componentName":"Collapse",
						"id":"Collapse-kK63NEbf",
						"props":{
							"accordion":"",
							"v-model":"${group.collapsed}"
						},
						"attrs":{
							"style":{
								"width":"100%"
							},
							"class":"${!group.config.enableTitle ? 'system-collapse-hidden-wrapper' : ''}"
						}
					}
				],
				"componentName":"Export",
				"id":"DivWrapper-M2BfJpGB",
				"title":"分组模块(组件)",
				"attrs":{
					"name":"FieldGroup",
					"style":{
						"width":"100%"
					},
					"run":false
				},
				"props":{
					"group":"${{/*dm*/
\n  \"collapsed\": [\"分组\"], // 展开内容/*dm*/
\n  \"groupName\": \"分组\",/*dm*/
\n  \"fieldList\": [],/*dm*/
\n  \"subModelList\": []/*dm*/
\n}}"
				}
			},
			{
				"componentType":"custom",
				"children":[
					{
						"componentType":"custom",
						"children":[
							{
								"libraryName":"@jd/joyui",
								"componentType":"UILibrary",
								"__setting":{},
								"children":[
									{
										"libraryName":"@jd/joyui",
										"componentType":"UILibrary",
										"action":{
											"fire":{
												"handleReturn":{
													"arguments":[
														""
													],
													"actions":[
														{
															"handler":"${function handler(){if (history.state) {\r/*dm*/
\n  $.bom.page.close() || $.bom.route('go', -1);\r/*dm*/
\n} else {\r/*dm*/
\n  $.bom.route('push', { path: '/' })\r/*dm*/
\n}}}",
															"type":"function"
														}
													]
												}
											}
										},
										"componentName":"TitleButtonBreadCrumb",
										"id":"TitleButtonBreadCrumb37704",
										"title":"按钮面包屑",
										"version":"1.8.3",
										"props":{
											"btnText":"",
											"size":"medium",
											"round":"${false}",
											"plain":"${false}",
											"nativeType":"button",
											"icon":"joy-left",
											"disabled":"${false}",
											"loading":"${false}",
											"autofocus":"${false}",
											"btnType":"text",
											"circle":"${false}"
										}
									},
									{
										"libraryName":"@jd/joyui",
										"componentType":"UILibrary",
										"__setting":{
											"remoteMethod":"${}",
											"icon":"",
											"remote":"true",
											"reserveKeyword":"false",
											"selectSearch":"",
											"collapseTags":"false",
											"allowCreate":"false",
											"automaticDropdown":"false",
											"labelFromRemoteMethod":"false",
											"options":"${$.shareData.var_pageOptions}",
											"disabled":"false",
											"placeholder":"",
											"valueKey":"value",
											"filterable":"true",
											"clearable":"false",
											"btnText":"",
											"loadingText":"加载中",
											"autocomplete":"off",
											"multipleLimit":0,
											"multiple":"false",
											"loading":"false",
											"noDataText":"无数据",
											"popperAppendToBody":"false",
											"titleName":"${$.shareData.var_formInfo?.name}",
											"size":"mini",
											"defaultFirstOption":"false",
											"popperClass":"",
											"name":"",
											"noMatchText":"无匹配数据"
										},
										"children":[],
										"action":{
											"fire":{
												"change":{
													"arguments":[
														"val"
													],
													"actions":[
														{
															"handler":"${function Fun_hJMiQ8(val){/*dm*/
\n  const { origin, pathname, search } = window.location;/*dm*/
\n  try {/*dm*/
\n    const [, oldValue] = search.match(/detailId=(.*?)&/);/*dm*/
\n    window.location.href = origin + pathname + search.replace(oldValue, val);/*dm*/
\n  } catch(err) {/*dm*/
\n    window.location.href = origin + pathname + search + '&detailId=' + val;/*dm*/
\n  }/*dm*/
\n}}",
															"type":"function"
														}
													]
												}
											}
										},
										"componentName":"TitlePageSearch",
										"id":"TitlePageSearch84225",
										"title":"切换页面按钮",
										"version":"1.8.3",
										"props":{
											"icon":"",
											"reserveKeyword":"${false}",
											"remote":"${true}",
											"selectSearch":"",
											"collapseTags":"${false}",
											"allowCreate":"${false}",
											"automaticDropdown":"${false}",
											"labelFromRemoteMethod":"${false}",
											"options":"${$.shareData.var_pageOptions}",
											"disabled":"${false}",
											"placeholder":"",
											"valueKey":"value",
											"filterable":"${true}",
											"clearable":"${false}",
											"btnText":"",
											"loadingText":"加载中",
											"autocomplete":"off",
											"multipleLimit":0,
											"multiple":"${false}",
											"loading":"${false}",
											"noDataText":"无数据",
											":remote-method":"${/*dm*/
\n                        function bb(query){/*dm*/
\n                          if (query !== '') {/*dm*/
\n                            let params = {/*dm*/
\n                              modelName: $.shareData.var_setting.modelName,/*dm*/
\n                              \"pageNum\": 1,/*dm*/
\n                              \"pageSize\": 10,/*dm*/
\n                              \"totalSize\": 42,/*dm*/
\n                              \"queryConditions\": [/*dm*/
\n                                {\"fieldName\": \"name\",\"value\": query,\"conditionType\": \"LIKE\"}/*dm*/
\n                              ],/*dm*/
\n                              \"querySorts\": [{\"fieldName\": \"update_time\",\"desc\": true}]/*dm*/
\n                            }/*dm*/
\n                            await $.shareData.api_getListAction(params)/*dm*/
\n                            const list = $.shareData.api_getList?.data?.records/*dm*/
\n                            const id = $.functions.fun_getQueryValue('detailId')/*dm*/
\n                            if (list.every(i => i.id !== id)) {/*dm*/
\n                              list.unshift({/*dm*/
\n                                id,/*dm*/
\n                                name: $.shareData.var_formInfo?.name/*dm*/
\n                              });/*dm*/
\n                            }/*dm*/
\n                            $.shareData.var_pageOptions = list/*dm*/
\n                          } else {/*dm*/
\n                            $.shareData.var_pageOptions = [];/*dm*/
\n                          }/*dm*/
\n                        }}",
											"popperAppendToBody":"${false}",
											"titleName":"${$.shareData.var_formInfo?.name || $.shareData.var_formInfo?.id}",
											"size":"mini",
											"defaultFirstOption":"${false}",
											"popperClass":"",
											"name":"",
											"noMatchText":"无匹配数据"
										}
									},
									{
										"libraryName":"@jd/joyui",
										"componentType":"UILibrary",
										"children":[
											{
												"componentType":"custom",
												"children":[
													{
														"componentType":"custom",
														"action":{
															"fire":{
																"click":{
																	"arguments":[],
																	"actions":[
																		{
																			"handler":"${function Fun26963(){/*dm*/
\n                                        const params = new URLSearchParams(location.search)/*dm*/
\n                                        const detailId = params.get(\"detailId\");/*dm*/
\n                                        const id = $.shareData.var_setting.formPath;/*dm*/
\n                                        if (id) {/*dm*/
\n                                          $.bom.page.open(id + '?detailId=' + $.shareData.var_formInfo.id, {}, () => {/*dm*/
\n                                            $.functions.fun_initView();/*dm*/
\n                                          }, {});/*dm*/
\n                                        }/*dm*/
\n                                      }}",
																			"type":"function"
																		}
																	]
																}
															}
														},
														"id":"Button19625",
														"componentName":"Button",
														"title":"编辑按钮",
														"props":{
															"size":"small",
															"children":"编辑",
															"type":"primary"
														},
														"attrs":{
															"style":{}
														}
													}
												],
												"componentName":"VIf",
												"id":"VIf72901",
												"props":{
													"conditions":"${[$.functions.fun_verifRole(\"edit\") && !$.shareData.showModelFlow && $.shareData.var_setting.enableButtons]}"
												}
											}
										],
										"id":"IfWrap73741",
										"componentName":"IfWrap",
										"title":"显隐容器",
										"version":"1.8.3",
										"props":{
											"isShow":""
										},
										"attrs":{
											"style":{
												"display":"flex",
												"flex-basis":"10px"
											}
										}
									}
								],
								"componentName":"TitlePage",
								"id":"TitlePage13112",
								"title":"标题",
								"version":"1.8.3",
								"props":{}
							}
						],
						"componentName":"VIf",
						"id":"VIf-PcW3KzrF",
						"props":{
							"conditions":"${[$.shareData.var_showHeader && $.shareData.var_setting.enableTitle]}"
						}
					},
					{
						"libraryName":"@jd/joyui",
						"componentType":"UILibrary",
						"children":[
							{
								"componentType":"UILibrary",
								"__setting":{
									"formType":"info",
									"modelInfo":{
										"modelName":"ceshimoxing"
									},
									"labelPosition":"left",
									"labelWidth":"142px",
									"button_group_active":"center",
									"span":4,
									"button_group":[]
								},
								"children":[
									{
										"children":[
											{
												"componentType":"custom",
												"componentName":"Import",
												"id":"DivWrapper-7PwfQAa3",
												"title":"关键字分组",
												"attrs":{
													"name":"FieldGroup",
													"style":{}
												},
												"props":{
													"group":"${__group}"
												}
											}
										],
										"id":"VFor-nAJsPSzc1",
										"componentName":"VFor",
										"props":{
											"forEach":"${$.shareData.var_viewData.keyInfo}",
											"forEachKey":"__group",
											"index":"$index"
										}
									}
								],
								"componentName":"DivWrapper",
								"id":"Form88934",
								"title":"容器",
								"attrs":{
									"style":{}
								},
								"props":{
									"ref":"infoFormTop",
									"size":"small",
									"labelPosition":"left",
									"model":"${$.shareData.var_formInfo}"
								}
							},
							{
								"children":[
									{
										"componentType":"custom",
										"children":[
											{
												"componentType":"custom",
												"children":[
													{
														"componentType":"custom",
														"children":[
															{
																"componentType":"UILibrary",
																"children":[
																	{
																		"children":[
																			{
																				"componentType":"custom",
																				"componentName":"Import",
																				"id":"Import-p2BCS33S",
																				"title":"基本信息分组",
																				"attrs":{
																					"name":"FieldGroup",
																					"style":{}
																				},
																				"props":{
																					"group":"${__group}"
																				}
																			}
																		],
																		"id":"VFor-nAJsPSzc",
																		"componentName":"VFor",
																		"props":{
																			"forEach":"${$.shareData.var_viewData.baseInfo}",
																			"forEachKey":"__group",
																			"index":"$index"
																		}
																	}
																],
																"componentName":"DivWrapper",
																"id":"ModelForm46082",
																"title":"表单组件",
																"props":{
																	"ref":"infoFormTop",
																	"size":"small",
																	"labelPosition":"left",
																	"model":"${$.shareData.var_viewData.baseInfo}"
																},
																"attrs":{
																	"style":{
																		"paddingBottom":"24px",
																		"width":"100%"
																	}
																}
															}
														],
														"componentName":"TabPane",
														"id":"TabPane93520",
														"props":{
															"name":"first",
															"label":"基本信息"
														},
														"attrs":{
															"style":{
																"background-color":"#fff",
																"padding":"0",
																"display":"block",
																"margin-bottom":"12px"
															}
														},
														"chosen":false
													}
												],
												"componentName":"VIf",
												"id":"VIf-QESti4a7",
												"props":{
													"conditions":"${[$.shareData.var_showBaseInfoTab && $.shareData.var_viewData?.baseInfo?.length]}"
												},
												"tags":[
													"baseInfoTabRoot"
												]
											},
											{
												"componentType":"custom",
												"children":[
													{
														"componentType":"custom",
														"children":[
															{
																"componentType":"custom",
																"componentName":"TableColumn",
																"id":"TableColumn-6eS2CpAx",
																"props":{
																	"show-overflow-tooltip":true,
																	"prop":"role_name",
																	"label":"角色名称"
																}
															},
															{
																"componentType":"custom",
																"componentName":"TableColumn",
																"id":"TableColumn-QjaH6Qmy",
																"props":{
																	"show-overflow-tooltip":true,
																	"prop":"role_code",
																	"label":"角色编码"
																}
															},
															{
																"componentType":"custom",
																"componentName":"TableColumn",
																"id":"TableColumn-Zjf3Scnj",
																"props":{
																	"show-overflow-tooltip":"",
																	"prop":"description",
																	"label":"描述"
																}
															}
														],
														"id":"Table-xnbJ6hH2",
														"componentName":"Table",
														"title":"列表组件",
														"props":{
															"size":"small",
															"data":"${$.shareData.getPermissionUserInfo?.data?.role$obj || []}",
															"show-header":true
														},
														"attrs":{
															"style":{
																"min-height":"400px"
															}
														}
													}
												],
												"componentName":"TabPane",
												"id":"TabPane-RKAAxX7W",
												"props":{
													"name":"role",
													"label":"所属角色"
												},
												"attrs":{
													"style":{
														"background-color":"#FFFFFF",
														"margin-bottom":"12px"
													}
												},
												"tags":[
													"findRelationshipTabsTag"
												]
											},
											{
												"componentType":"custom",
												"children":[
													{
														"componentType":"custom",
														"children":[
															{
																"componentType":"custom",
																"componentName":"TableColumn",
																"id":"TableColumn-4zCsJQE4",
																"props":{
																	"show-overflow-tooltip":true,
																	"prop":"name",
																	"label":"组织名称"
																}
															},
															{
																"componentType":"custom",
																"componentName":"TableColumn",
																"id":"TableColumn-sjJKAYWf",
																"props":{
																	"show-overflow-tooltip":true,
																	"prop":"code",
																	"label":"组织编码"
																}
															},
															{
																"componentType":"custom",
																"componentName":"TableColumn",
																"id":"TableColumn-ZRrEtdBX",
																"props":{
																	"show-overflow-tooltip":true,
																	"prop":"full_path_name",
																	"label":"组织全路径名称"
																}
															},
															{
																"componentType":"custom",
																"componentName":"TableColumn",
																"id":"TableColumn-7Fdm6BG6",
																"props":{
																	"show-overflow-tooltip":true,
																	"prop":"full_path_code",
																	"label":"组织全路径编码"
																}
															}
														],
														"id":"Table-iayPSAsf",
														"componentName":"Table",
														"title":"列表组件",
														"props":{
															"size":"small",
															"data":"${$.shareData.getPermissionUserInfo?.data?.business_organization$obj || []}",
															"show-header":true
														},
														"attrs":{
															"style":{
																"min-height":"400px"
															}
														}
													}
												],
												"componentName":"TabPane",
												"id":"TabPane-MikZRWn6",
												"props":{
													"name":"business",
													"label":"业务组织"
												},
												"attrs":{
													"style":{
														"background-color":"#FFFFFF",
														"margin-bottom":"12px"
													}
												},
												"tags":[
													"findRelationshipTabsTag"
												]
											},
											{
												"componentType":"custom",
												"children":[
													{
														"componentType":"custom",
														"children":[
															{
																"componentType":"custom",
																"children":[
																	{
																		"componentType":"custom",
																		"componentName":"TableColumn",
																		"id":"TableColumn-6JDmMJrR",
																		"props":{
																			"prop":"create_time",
																			"label":"修改时间"
																		}
																	},
																	{
																		"componentType":"custom",
																		"children":[
																			{
																				"libraryName":"@jd/joyui",
																				"componentType":"UILibrary",
																				"componentName":"ModelField",
																				"id":"ModelField-kfFBystx",
																				"version":"1.5.30",
																				"props":{
																					"formType":"table",
																					"fieldProps":"${}",
																					"businessType":"user",
																					"value":"${() => {/*dm*/
\n  const {/*dm*/
\n    headImage,/*dm*/
\n    fullDetpName: orgTierName,/*dm*/
\n    positionName,/*dm*/
\n    userName: erp,/*dm*/
\n    realName/*dm*/
\n  } = scope.row[\"created_user$user\"];/*dm*/
\n  return {/*dm*/
\n    headImage,/*dm*/
\n    orgTierName,/*dm*/
\n    positionName,/*dm*/
\n    erp,/*dm*/
\n    name: realName,/*dm*/
\n  }/*dm*/
\n}}"
																				}
																			}
																		],
																		"componentName":"TableColumn",
																		"id":"TableColumn-PYcH42PK",
																		"props":{
																			"prop":"created_user",
																			"label":"操作人",
																			"v-slot":"scope"
																		}
																	},
																	{
																		"componentType":"custom",
																		"children":[
																			{
																				"componentType":"custom",
																				"componentName":"TextWrapper",
																				"id":"TextWrapper-J7e7P7br",
																				"props":{
																					"v-html":"${scope.row.detail}"
																				}
																			}
																		],
																		"componentName":"TableColumn",
																		"id":"TableColumn-fWQ34Nwc",
																		"props":{
																			"prop":"detail",
																			"label":"详情",
																			"v-slot":"scope"
																		}
																	}
																],
																"id":"Table96654",
																"componentName":"Table",
																"title":"列表组件",
																"props":{
																	"border":false,
																	"size":"small",
																	"data":"${$.shareData.var_fieldTrackList}"
																}
															}
														],
														"componentName":"TabPane",
														"id":"TabPane-GTKBYHdb",
														"props":{
															"name":"fieldTrack",
															"label":"字段值跟踪"
														},
														"attrs":{
															"style":{
																"background-color":"#FFFFFF",
																"margin-bottom":"12px"
															}
														},
														"tags":[
															"findRelationshipTabsTag"
														]
													}
												],
												"componentName":"VIf",
												"id":"VIf-W6m6NY6G",
												"props":{
													"conditions":"${[$.shareData.var_showFieldTrack]}"
												},
												"tags":[
													"fieldTrackTabRoot"
												]
											},
											{
												"children":[
													{
														"children":[
															{
																"componentType":"custom",
																"children":[
																	{
																		"componentType":"custom",
																		"children":[
																			{
																				"libraryName":"@jd/joyui",
																				"componentType":"UILibrary",
																				"action":{
																					"fire":{
																						"keywordSearch":{
																							"arguments":[
																								"data"
																							],
																							"actions":[
																								{
																									"handler":"${function handler(){console.log(data, 'nnnnnbbbbb')/*dm*/
\n$.shareData.var_baseSearchQuery[__tab.tableKey].keywordQuery = {/*dm*/
\n  keyword: data.keywordQuery,/*dm*/
\n  scopeAttributes: data.attributes,/*dm*/
\n};/*dm*/
\n$.shareData.var_baseSearchQuery[__tab.tableKey].pageNum = 1;/*dm*/
\nconst { modelName } = __tab;/*dm*/
\n$.functions.fun_loadTableData(modelName, __tab.tableKey)/*dm*/
\n}}",
																									"type":"function"
																								}
																							]
																						},
																						"keywordInput":{
																							"arguments":[
																								"data"
																							],
																							"actions":[
																								{
																									"handler":"${function Handler(){/*dm*/
\n                                            __tab.keywordQuery.keyword = data.keywordQuery;/*dm*/
\n                                          }}",
																									"type":"function"
																								}
																							]
																						}
																					}
																				},
																				"id":"KeywordSearch52526",
																				"componentName":"KeywordSearch",
																				"version":"1.8.3",
																				"props":{
																					"modelName":"${__tab.modelName}",
																					"ref":"${'keywordSearch' + __tab.tableKey}",
																					"showKeywordSearch":"${true}",
																					"attributes":"${__tab.keyWordList}"
																				}
																			},
																			{
																				"componentType":"custom",
																				"action":{
																					"fire":{
																						"click":{
																							"actions":[
																								{
																									"handler":"${function handle(){/*dm*/
\n// 查找关联模型新建页pageId/*dm*/
\nconst associatedList = $.shareData.var_associatedList;/*dm*/
\nconst fieldName = __tab.relatedFieldName;/*dm*/
\n/*dm*/
\nconst { id: formId } = $.shareData.var_formInfo;/*dm*/
\nconst path = $.functions.fun_getTableNavUrl(\"ass\", __tab.modelName, 'form')/*dm*/
\nconst query = {};/*dm*/
\nquery.formId = formId;/*dm*/
\nquery.fieldName = fieldName;/*dm*/
\nquery.modelName = $.shareData.var_setting.modelName;/*dm*/
\nif (formId) {/*dm*/
\n  $.bom.page.open({ path: path, query: query }, query, async () => {/*dm*/
\n    if ($.shareData.var_tabActiveName !== 'first') {/*dm*/
\n      await $.functions.fun_loadCache(__tab.tableKey, $.shareData.var_tabActiveName);/*dm*/
\n      $.functions.fun_loadTableData(__tab.modelName, __tab.tableKey)/*dm*/
\n    }/*dm*/
\n  }, {});/*dm*/
\n}/*dm*/
\n}}",
																									"type":"function"
																								}
																							]
																						}
																					}
																				},
																				"componentName":"Button",
																				"id":"Button63357",
																				"props":{
																					"size":"small",
																					"children":"新建",
																					"v-open":"${/*dm*/
\n() => {/*dm*/
\n  // 查找关联模型新建页pageId/*dm*/
\n  const fieldName = __tab.fieldName;/*dm*/
\n  const { id: formId } = $.shareData.var_formInfo;/*dm*/
\n  let path = $.shareData.var_setting.pagePath + `/${__tab.modelName}/form`/*dm*/
\n  const query = {};/*dm*/
\n  query.formId = formId;/*dm*/
\n  query.fieldName = fieldName;/*dm*/
\n  query.modelName = $.shareData.var_setting.modelName;/*dm*/
\n/*dm*/
\n  if (query && Object.keys(query).length > 0) {/*dm*/
\n    let arr = Object.keys(query);/*dm*/
\n    let queryStr = arr.map((item, index) => {/*dm*/
\n      return item + '=' + query[item]/*dm*/
\n    }).join('&');/*dm*/
\n    path += '?' + queryStr/*dm*/
\n  }/*dm*/
\n  return path/*dm*/
\n}/*dm*/
\n}",
																					"type":"primary"
																				},
																				"chosen":false
																			}
																		],
																		"componentName":"DivWrapper",
																		"id":"DivWrapper33766",
																		"attrs":{
																			"style":{
																				"display":"flex",
																				"marginBottom":"8px",
																				"justifyContent":"space-between"
																			}
																		}
																	},
																	{
																		"children":[
																			{
																				"libraryName":"@jd/joyui",
																				"componentType":"UILibrary",
																				"__setting":{
																					"settingData":{
																						"pageSizes":"${[10,20,30,40]}",
																						"editPage":"undefined",
																						"headerEllipsis":true,
																						"resizeColumn":true,
																						"pageSize":10,
																						"showRefresh":true,
																						"batchDelFlag":true,
																						"buttonShowText":true,
																						"mode":"normal",
																						"columnShowIcon":false,
																						"showPlanView":true,
																						"stripe":true,
																						"addPage":"undefined",
																						"paginationMode":"complete",
																						"showOverflowTooltip":true,
																						"border":false,
																						"showEdit":true,
																						"columnShowText":true,
																						"buttonShowIcon":false,
																						"textAlign":"left",
																						"paginationAlign":"right",
																						"showPagination":true,
																						"addFlag":true,
																						"showDetail":true,
																						"exportDataFlag":false,
																						"cellEllipsis":true,
																						"showNo":true,
																						"showDel":true,
																						"detailPage":"undefined",
																						"draftFlag":false
																					}
																				},
																				"children":[
																					{
																						"componentType":"custom",
																						"children":[
																							{
																								"componentType":"custom",
																								"children":[
																									{
																										"componentType":"custom",
																										"children":[
																											{
																												"componentType":"custom",
																												"id":"Image65170",
																												"componentName":"Image",
																												"props":{
																													"src":"${\"http://s3.cn-north-1.jdcloud-oss.com/jz-material/lcapicon/1658309481895-3905270251763847417.png\"}"
																												},
																												"chosen":false,
																												"attrs":{
																													"style":{
																														"width":"120px"
																													}
																												}
																											},
																											{
																												"componentType":"custom",
																												"id":"TextWrapper58451",
																												"componentName":"TextWrapper",
																												"attrs":{
																													"style":{
																														"margin":"-30px 0 0 0",
																														"color":"rgba(129,128,148,1)",
																														"font-weight":"normal",
																														"display":"block",
																														"width":"100%",
																														"font-size":"14px"
																													}
																												},
																												"props":{
																													"children":" 暂无内容"
																												},
																												"chosen":false
																											}
																										],
																										"id":"DivWrapper23402",
																										"componentName":"DivWrapper",
																										"title":"暂无数据",
																										"attrs":{
																											"style":{
																												"background-color":"#ffffff",
																												"width":"100%",
																												"height":"100%"
																											}
																										},
																										"props":{
																											"name":"DivWrapper",
																											"slot":"empty"
																										},
																										"chosen":false
																									},
																									{
																										"componentType":"custom",
																										"children":[
																											{
																												"componentType":"custom",
																												"componentName":"TableColumn",
																												"id":"TableColumn82879",
																												"props":{
																													"show-overflow-tooltip":true,
																													"reserve-selection":true,
																													":selectable":"${functions(scope){ return scope['$extra']?.dataPermission ==2 }}",
																													"width":"48",
																													"fixed":"left",
																													"type":"selection",
																													"align":"center",
																													"v-slot":"scope"
																												}
																											}
																										],
																										"id":"VIf32143",
																										"componentName":"VIf",
																										"props":{
																											"conditions":"${[ $.shareData.var_basePageSetting[__tab.tableKey]?.selectRowType==='many' || $.shareData.var_basePageSetting[__tab.tableKey]?.batchDelFlag]}"
																										}
																									},
																									{
																										"componentType":"custom",
																										"children":[
																											{
																												"componentType":"custom",
																												"componentName":"TableColumn",
																												"id":"TableColumn49702",
																												"props":{
																													"resizable":false,
																													"width":"60",
																													"fixed":"left",
																													"label":"序号",
																													"type":"index",
																													"align":"left",
																													"class-name":"color-text-secondary"
																												}
																											}
																										],
																										"id":"VIf96597",
																										"componentName":"VIf",
																										"props":{
																											"conditions":"${[$.shareData.var_basePageSetting[__tab.tableKey]?.showNo]}"
																										}
																									},
																									{
																										"componentType":"custom",
																										"children":[
																											{
																												"componentType":"custom",
																												"children":[
																													{
																														"libraryName":"@jd/joyui",
																														"componentType":"UILibrary",
																														"action":{
																															"fire":{
																																"navtoDetail":{
																																	"arguments":[
																																		"field",
																																		"modelName"
																																	],
																																	"actions":[
																																		{
																																			"handler":"${function Fun73401(field, modelName){let pageId = $.functions.fun_getTableNavUrl('ass', __tab.modelName, \"detail\")/*dm*/
\nif(modelName) {/*dm*/
\n  pageId = $.functions.fun_getTableNavUrl('', modelName, \"detail\")/*dm*/
\n}/*dm*/
\nconst detailId = scope.row[item.name]?.id || scope.row.id; // 兼容查找关系/*dm*/
\n$.bom.page.open(pageId + '?detailId=' + detailId + '', {}, async () => {/*dm*/
\n  if ($.shareData.var_tabActiveName !== 'first') {/*dm*/
\n    await $.functions.fun_loadCache(__tab.tableKey, $.shareData.var_tabActiveName);/*dm*/
\n    $.functions.fun_loadTableData(__tab.modelName, __tab.tableKey)/*dm*/
\n  }/*dm*/
\n}, {});/*dm*/
\n}}",
																																			"type":"function"
																																		}
																																	]
																																},
																																"handleDownload":{
																																	"arguments":[
																																		"file"
																																	],
																																	"actions":[
																																		{
																																			"handler":"${function handler(file){\r/*dm*/
\nawait $.shareData.preDownloadAction({ originFileName: file.originFileName })\r/*dm*/
\n\r/*dm*/
\nfetch($.shareData.preDownload.presignedObjectUrl, {\r/*dm*/
\n  method: 'GET',\r/*dm*/
\n  responseType: 'blob',\r/*dm*/
\n}).then(\r/*dm*/
\n  res => {\r/*dm*/
\n    return res.blob();\r/*dm*/
\n  })\r/*dm*/
\n  .then(blob => {\r/*dm*/
\n\r/*dm*/
\n    let bl = new Blob([blob]);\r/*dm*/
\n\r/*dm*/
\n    var link = document.createElement('a');\r/*dm*/
\n\r/*dm*/
\n    link.href = window.URL.createObjectURL(bl);\r/*dm*/
\n\r/*dm*/
\n    link.download = file.name;\r/*dm*/
\n\r/*dm*/
\n    link.click();\r/*dm*/
\n\r/*dm*/
\n    window.URL.revokeObjectURL(link.href);\r/*dm*/
\n\r/*dm*/
\n  }).catch(err => {\r/*dm*/
\n    console.log('error', error)\r/*dm*/
\n  });\r/*dm*/
\n}}",
																																			"type":"function"
																																		}
																																	]
																																}
																															}
																														},
																														"componentName":"ModelField",
																														"id":"ModelField89578",
																														"title":"模型字段",
																														"version":"1.8.3",
																														"props":{
																															"originProps":{},
																															"formType":"table",
																															"fieldProps":"${item.modelField}",
																															"v-open":"${ () => {/*dm*/
\n                if (false) {/*dm*/
\n                  // console.error('子模型新建或编辑时， 不支持跳转啊');/*dm*/
\n                  return;/*dm*/
\n                }/*dm*/
\n                if(!$.shareData.var_baseFindRelatedPages[$.shareData.var_activeTabModelName] || !item?.name) {/*dm*/
\n                  return;/*dm*/
\n                }/*dm*/
\n                const pageId = $.shareData.var_baseFindRelatedPages[$.shareData.var_activeTabModelName][item.name];/*dm*/
\n                if (!pageId) {/*dm*/
\n                  return;/*dm*/
\n                }/*dm*/
\n                const detailId = scope.row[item.name]?.id || scope.row.id;/*dm*/
\n                const jumpUrl = pageId + '?detailId=' + detailId + ''/*dm*/
\n                return jumpUrl/*dm*/
\n              }}",
																															"businessType":"${item.modelField.businessType}",
																															"value":"${scope.row[item.name]}"
																														}
																													}
																												],
																												"componentName":"TableColumn",
																												"id":"TableColumn97489",
																												"props":{
																													"show-overflow-tooltip":"${$.shareData.var_basePageSetting[__tab.tableKey].cellEllipsis}",
																													"prop":"${item.name}",
																													"fixed":false,
																													"label":"${item.label}",
																													"sortable":"${() => {\r/*dm*/
\n  if (\r/*dm*/
\n    [\"text\", \"richText\", \"attachment\", \"user\", \"multipleUser\", \"url\"].includes(item.modelField.businessType)\r/*dm*/
\n    ||\r/*dm*/
\n    item.modelField.entityField === false\r/*dm*/
\n  ) {\r/*dm*/
\n    return false;\r/*dm*/
\n  }\r/*dm*/
\n  return \"custom\";\r/*dm*/
\n}}",
																													"align":"${$.shareData.var_basePageSetting[__tab.tableKey].textAlign}",
																													"v-slot":"scope",
																													"min-width":"${() => {\r/*dm*/
\n  if (item?.modelField.businessType === 'dateTime') {\r/*dm*/
\n    return item?.displayWidth || 180\r/*dm*/
\n  }\r/*dm*/
\n  return item?.displayWidth || 140\r/*dm*/
\n}}"
																												}
																											}
																										],
																										"id":"VFor83198",
																										"componentName":"VFor",
																										"title":"table列循环容器",
																										"props":{
																											"forEach":"${() => {/*dm*/
\n/*dm*/
\n  console.log($.shareData.var_baseTableColumnShow, __tab.tableKey, 'nnnndbbbb')/*dm*/
\n  return $.shareData.var_baseTableColumnShow[__tab.tableKey]/*dm*/
\n}}",
																											"forEachKey":"item",
																											"index":"$index"
																										}
																									},
																									{
																										"componentType":"custom",
																										"children":[
																											{
																												"componentType":"custom",
																												"children":[
																													{
																														"libraryName":"@jd/joyui",
																														"componentType":"UILibrary",
																														"action":{
																															"fire":{
																																"tab-click":{
																																	"arguments":[
																																		"tab"
																																	],
																																	"actions":[
																																		{
																																			"handler":"${function Fun14548(){/*dm*/
\nconst modelName = $.shareData.var_baseSearchQuery[__tab.tableKey].modelName;/*dm*/
\nif (tab == 'edit') {/*dm*/
\n  let editUrl = (() => {/*dm*/
\n    const detailId = scope.row?.id;/*dm*/
\n    return $.functions.fun_getTableNavUrl(\"ass\", __tab.modelName, 'form') + '?detailId=' + detailId + ''/*dm*/
\n  })();/*dm*/
\n    $.bom.page.open(editUrl, {}, async () => {/*dm*/
\n      if ($.shareData.var_tabActiveName !== 'first') {/*dm*/
\n        await $.functions.fun_loadCache(__tab.tableKey, $.shareData.var_tabActiveName);/*dm*/
\n        $.functions.fun_loadTableData(__tab.modelName, __tab.tableKey)/*dm*/
\n      }/*dm*/
\n    }, {});/*dm*/
\n}/*dm*/
\nif (tab == 'detail') {/*dm*/
\n  const detailId = scope.row?.id;/*dm*/
\n  const params = scope.row.$extra?.dataPermission;/*dm*/
\n  $.bom.page.open({ path: $.functions.fun_getTableNavUrl(\"ass\", __tab.modelName, 'detail'), query: { detailId, permission: params } }, {}, async () => {/*dm*/
\n    if ($.shareData.var_tabActiveName !== 'first') {/*dm*/
\n      await $.functions.fun_loadCache(__tab.tableKey, $.shareData.var_tabActiveName);/*dm*/
\n      $.functions.fun_loadTableData(__tab.modelName, __tab.tableKey)/*dm*/
\n    }/*dm*/
\n  }, {});/*dm*/
\n}/*dm*/
\nif (tab == 'delete') {/*dm*/
\n  this.$confirm('此操作将永久删除, 是否继续?', '提示', {/*dm*/
\n    confirmButtonText: '确定',/*dm*/
\n    cancelButtonText: '取消',/*dm*/
\n    type: 'warning'/*dm*/
\n  }).then(() => {/*dm*/
\n    $.shareData.api_deleteDataByIdAction({ modelName, id: scope.row?.id }).then(() => {/*dm*/
\n      if ($.shareData.api_deleteDataById) {/*dm*/
\n        this.$message.success('删除成功!');/*dm*/
\n        const { totalSize, pageNum, pageSize } = $.shareData.var_basePageParams[__tab.tableKey]/*dm*/
\n        if (totalSize - pageSize * (pageNum - 1) - 1 <= 0 && pageNum > 1) {/*dm*/
\n          $.shareData.var_basePageParams[__tab.tableKey].pageNum = pageNum;/*dm*/
\n        }/*dm*/
\n        $.functions.fun_loadTableData(__tab.modelName, __tab.tableKey);/*dm*/
\n      } else {/*dm*/
\n        this.$message.error('删除失败')/*dm*/
\n      }/*dm*/
\n    }).catch((error) => {/*dm*/
\n      this.$message.error(error)/*dm*/
\n    })/*dm*/
\n  }).catch(() => { });/*dm*/
\n}/*dm*/
\n}}",
																																			"type":"function"
																																		}
																																	]
																																}
																															}
																														},
																														"id":"MoreButton38570",
																														"componentName":"MoreButton",
																														"version":"1.8.3",
																														"props":{
																															"styleState":"${[]}",
																															"padding":"0px 8px",
																															"data":"${() => {/*dm*/
\n  let list = [];/*dm*/
\n  let edit = false ? $.shareData.permissionData?.edit : true;/*dm*/
\n  let detail = false ? $.shareData.permissionData?.detail : true;/*dm*/
\n  let deletes = false ? $.shareData.permissionData?.delete : true;/*dm*/
\n  let editUrl = (() => {/*dm*/
\n    const detailId = scope.row?.id;/*dm*/
\n    return $.functions.fun_getTableNavUrl(\"ass\", __tab.modelName, 'form') + '?detailId=' + detailId + ''/*dm*/
\n  })();/*dm*/
\n  let detailUrl = (() => {/*dm*/
\n    const detailId = scope.row?.id;/*dm*/
\n    return $.functions.fun_getTableNavUrl(\"ass\", __tab.modelName, 'detail') + '?detailId=' + detailId + ''/*dm*/
\n  })();/*dm*/
\n  if ($.functions.fun_getTableConfig(__tab.tableKey)?.edit && scope.row['$extra'].dataPermission == 2 && edit) {/*dm*/
\n    list.push({ \"label\": \"编辑\", \"name\": \"edit\", \"type\": \"text\", \"round\": \"round\", \"size\": \"small\", \"v-open\": editUrl })/*dm*/
\n  };/*dm*/
\n  if ($.functions.fun_getTableConfig(__tab.tableKey)?.detail && [1, 2].some((i) => i == scope.row['$extra'].dataPermission) && detail) {/*dm*/
\n    list.push({ \"label\": \"详情\", \"name\": \"detail\", \"type\": \"text\", \"round\": \"round\", \"size\": \"small\", \"v-open\": detailUrl });/*dm*/
\n  };/*dm*/
\n  if ($.functions.fun_getTableConfig(__tab.tableKey)?.delete && scope.row['$extra'].dataPermission == 2 && deletes) {/*dm*/
\n    list.push({ \"label\": \"删除\", \"name\": \"delete\", \"type\": \"text\", \"round\": \"round\", \"size\": \"small\" });/*dm*/
\n  };/*dm*/
\n  return list;/*dm*/
\n}/*dm*/
\n}",
																															"moreRound":"${false}",
																															"max-count":"${3}",
																															"textAlign":"left",
																															"marginLeft":"${0 * 1}"
																														}
																													}
																												],
																												"componentName":"TableColumn",
																												"id":"TableColumn58922",
																												"props":{
																													"prop":"action",
																													"fixed":"right",
																													"label":"操作",
																													"v-slot":"scope",
																													"min-width":"172px"
																												}
																											}
																										],
																										"componentName":"VIf",
																										"id":"VIf31978",
																										"props":{
																											"conditions":"${[ $.shareData.var_basePageSetting[__tab.tableKey]?.mode==='normal']}"
																										}
																									}
																								],
																								"action":{
																									"fire":{
																										"header-dragend":{
																											"arguments":[
																												"newWidth",
																												"oldWidth",
																												"column",
																												"event"
																											],
																											"actions":[
																												{
																													"handler":"${function handler(newWidth,oldWidth,column,event){/*dm*/
\nconst genId = $.functions.fun_genTableId(__tab.tableKey);/*dm*/
\n/*dm*/
\nconst { property } = column/*dm*/
\n/*dm*/
\n/*dm*/
\nconst { businessType, fieldWidth, minWidth } = $.functions.getFieldsBusinessType(__tab.tableKey, property);/*dm*/
\n/*dm*/
\n/*dm*/
\nconst minNewWidth = Math.max(minWidth, newWidth)/*dm*/
\n/*dm*/
\n/*dm*/
\nconst newColumnWidth = {/*dm*/
\n  ...$.shareData.tableColumnWidth.widths,/*dm*/
\n  [property]: minNewWidth/*dm*/
\n}/*dm*/
\n/*dm*/
\n$.shareData.tableColumnWidth.widths = newColumnWidth;/*dm*/
\n/*dm*/
\n// 修复数据、重置表格宽度/*dm*/
\n/*dm*/
\nthis.$refs['jtable'].store.states._columns = this.$refs['jtable']?.store.states._columns.map(item => {/*dm*/
\n/*dm*/
\n  if (item.id === column.id && item.minWidth && item.width) {/*dm*/
\n/*dm*/
\n    return {/*dm*/
\n      ...item,/*dm*/
\n      realWidth: minNewWidth,/*dm*/
\n      width: minNewWidth/*dm*/
\n    }/*dm*/
\n/*dm*/
\n  }/*dm*/
\n  return item/*dm*/
\n})/*dm*/
\n/*dm*/
\nthis.$refs['jtable']?.store?.scheduleLayout(true);/*dm*/
\n/*dm*/
\nthis.$nextTick(() => {/*dm*/
\n  // 更新列的宽度/*dm*/
\n/*dm*/
\n  this.$refs['jtable'].layout.updateColumnsWidth();/*dm*/
\n/*dm*/
\n})/*dm*/
\n/*dm*/
\nlocalStorage.setItem(genId, JSON.stringify(newColumnWidth))}}",
																													"type":"function"
																												}
																											]
																										},
																										"current-change":{
																											"arguments":[
																												"val"
																											],
																											"actions":[
																												{
																													"handler":"${function Fun48487(val){/*dm*/
\n                                if($.shareData.var_basePageSetting[__tab.tableKey]?.selectRowType==='single'){/*dm*/
\n                                  $.shareData.singleSelectionAction(val.id)/*dm*/
\n                                }/*dm*/
\n                              }}",
																													"type":"function"
																												}
																											]
																										},
																										"sort-change":{
																											"arguments":[
																												"column",
																												"prop"
																											],
																											"actions":[
																												{
																													"handler":"${function Fun_47sXNY(column, prop){/*dm*/
\nif ($.shareData.var_baseSearchQuery[__tab.tableKey]?.querySorts.length > 1) {/*dm*/
\n/*dm*/
\n  $.shareData.var_baseSearchQuery[__tab.tableKey]?.querySorts.splice(0, 1);/*dm*/
\n/*dm*/
\n}/*dm*/
\n/*dm*/
\n/*dm*/
\n/*dm*/
\nif (column.order) {/*dm*/
\n/*dm*/
\n  $.shareData.var_baseSearchQuery[__tab.tableKey]?.querySorts.unshift({/*dm*/
\n/*dm*/
\n    fieldName: column.prop,/*dm*/
\n/*dm*/
\n    desc: column.order == \"descending\"/*dm*/
\n/*dm*/
\n  })/*dm*/
\n/*dm*/
\n}/*dm*/
\n/*dm*/
\n/*dm*/
\n/*dm*/
\nawait $.functions.fun_loadTableData(__tab.modelName, __tab.tableKey);/*dm*/
\n/*dm*/
\n}}",
																													"type":"function"
																												}
																											]
																										},
																										"selection-change":{
																											"arguments":[
																												"selection"
																											],
																											"actions":[
																												{
																													"handler":"${function Fun27292(selection){/*dm*/
\n                                  $.shareData.var_baseMultipleSelectionAction({/*dm*/
\n                                    [__tab.tableKey]: selection/*dm*/
\n                                  })/*dm*/
\n                              }}",
																													"type":"function"
																												}
																											]
																										}
																									}
																								},
																								"id":"Table66106",
																								"componentName":"Table",
																								"title":"表格区域",
																								"attrs":{
																									"class":"${$.shareData.var_basePageSetting[__tab.tableKey].headerEllipsis?'headerEllipsis':''}",
																									"setting":"${JSON.stringify({/*dm*/
\n  pageKey: $.shareData.var_setting.pageKey, \"name\": \"table\",/*dm*/
\n  \"editorViewCode\": __tab.tableKey,/*dm*/
\n  \"modelName\": __tab.modelName/*dm*/
\n })}"
																								},
																								"props":{
																									"border":"${false}",
																									"headerEllipsis":"${$.shareData.var_basePageSetting[__tab.tableKey].headerEllipsis}",
																									"data":"${$.shareData.var_baseTableList[__tab.tableKey]}",
																									"show-header":"${$.shareData.var_basePageSetting[__tab.tableKey].showHeader}",
																									"ref":"${__tab.tableKey}",
																									"size":"small",
																									"v-loading":"${$.shareData.var_baseTableLoading[__tab.tableKey]}",
																									"tooltip-effect":"dark",
																									"tree-props":"${{ hasChildren: \"hasChildren\", children: \"children\" }}",
																									"stripe":"${$.shareData.var_basePageSetting[__tab.tableKey].stripe}",
																									"style":"width: 100%",
																									"height":"100%",
																									"row-key":"id",
																									"highlight-current-row":"${$.shareData.var_basePageSetting[__tab.tableKey]?.selectRowType==='single'}"
																								}
																							}
																						],
																						"componentName":"DivWrapper",
																						"id":"DivWrapper41519",
																						"title":"表格+操作按钮",
																						"props":{
																							"name":"DivWrapper"
																						},
																						"attrs":{
																							"class":"records-joy-table-body"
																						}
																					}
																				],
																				"action":{
																					"fire":{
																						"handleSizeChange":{
																							"arguments":[
																								"val"
																							],
																							"actions":[
																								{
																									"handler":"${function Fun91232(val){/*dm*/
\n                                          $.shareData.var_basePageParams[__tab.tableKey].pageSize = val;/*dm*/
\n                                          localStorage.setItem(`var_basePageParams___tab.tableKey`, JSON.stringify({/*dm*/
\n                                            pageSize: val/*dm*/
\n                                          }))/*dm*/
\n                                          $.functions.fun_loadTableData(__tab.modelName, __tab.tableKey)/*dm*/
\n                                        }}",
																									"type":"function"
																								}
																							]
																						},
																						"handleCurrentChange":{
																							"arguments":[
																								"value"
																							],
																							"actions":[
																								{
																									"handler":"${function Fun26674(value){/*dm*/
\n                                          $.shareData.var_basePageParams[__tab.tableKey].pageNum = value;/*dm*/
\n                                          $.functions.fun_loadTableData(__tab.modelName, __tab.tableKey);/*dm*/
\n                                        }}",
																									"type":"function"
																								}
																							]
																						}
																					}
																				},
																				"id":"ModelTable7477",
																				"componentName":"ModelTable",
																				"title":"列表组件",
																				"version":"1.8.3",
																				"props":{
																					"pageSizes":"${$.shareData.var_basePageSetting[__tab.tableKey]?.pageSizes}",
																					"editPage":"undefined",
																					"buttonShowIcon":false,
																					"showPagination":"${$.shareData.var_basePageSetting[__tab.tableKey]?.showPagination}",
																					"showRefresh":false,
																					"batchDelFlag":false,
																					"buttonShowText":true,
																					"addFlag":true,
																					"mode":"${$.shareData.var_basePageSetting[__tab.tableKey]?.mode}",
																					"columnShowIcon":false,
																					"modelName":"associatedList",
																					"exportDataFlag":false,
																					"ref":"record-table",
																					"showPlanView":true,
																					"pageParams":"${$.shareData.var_basePageParams[__tab.tableKey]}",
																					"addPage":"undefined",
																					"paginationMode":"${$.shareData.var_basePageSetting[__tab.tableKey]?.paginationMode}",
																					"detailPage":"undefined",
																					"model":"multiple",
																					"draftFlag":false,
																					"multipleSelection":"${$.shareData.var_baseMultipleSelection[__tab.tableKey]}"
																				},
																				"attrs":{
																					"style":{
																						"height":"100%"
																					},
																					"class":"tableScolle link-find-table"
																				}
																			}
																		],
																		"componentName":"VIf",
																		"id":"VIf-TbtwpCT4",
																		"props":{
																			"conditions":"${() => {/*dm*/
\n/*dm*/
\n  console.log(__tab.tableKey, $.shareData.var_tabActiveName, '$.shareData.var_tabActiveName')/*dm*/
\n  return [__tab.tableKey == $.shareData.var_tabActiveName]/*dm*/
\n}}"
																		}
																	}
																],
																"id":"TabPane82727",
																"componentName":"TabPane",
																"props":{
																	"name":"${__tab.tableKey}",
																	"label":"${__tab.tabName}"
																},
																"attrs":{
																	"style":{
																		"background-color":"#FFFFFF",
																		"position":"relative",
																		"margin-bottom":"12px",
																		"height":"100%"
																	}
																},
																"chosen":false
															},
															{
																"componentType":"custom",
																"children":[
																	{
																		"children":[
																			{
																				"componentName":"Include",
																				"id":"Include-bJse8ir2",
																				"props":{
																					":onLoad":"${function handler(){const detailId = $.functions.fun_getQueryValue('detailId');/*dm*/
\nconsole.log(__tab.fieldName, 'nnnnbbbbb')/*dm*/
\n$.$emit('treeQuery', {/*dm*/
\n  queryConditions: [/*dm*/
\n    {/*dm*/
\n      fieldName: __tab.fieldName,/*dm*/
\n      conditionType: 'EQ',/*dm*/
\n      value: detailId || ''/*dm*/
\n    }/*dm*/
\n  ]/*dm*/
\n}, {/*dm*/
\n  [__tab.fieldName]: $.shareData.var_formInfo/*dm*/
\n});}}",
																					"pageId":"${__tab.pagePath}"
																				},
																				"attrs":{
																					"style":{
																						"top":"0",
																						"left":"0",
																						"width":"100%",
																						"position":"absolute",
																						"height":"100%"
																					}
																				}
																			}
																		],
																		"componentName":"VIf",
																		"id":"VIf-sKnjcdWZ",
																		"props":{
																			"conditions":"${[__tab.tableKey == $.shareData.var_tabActiveName]}"
																		}
																	}
																],
																"id":"TabPane-xHs6zWY2",
																"componentName":"TabPane",
																"props":{
																	"name":"${__tab.tableKey}",
																	"label":"${__tab.tabName}"
																},
																"attrs":{
																	"style":{
																		"background-color":"#FFFFFF",
																		"position":"relative",
																		"height":"100%"
																	}
																},
																"chosen":false
															}
														],
														"componentName":"VIf",
														"id":"VIf-4jBhp77h",
														"props":{
															"conditions":"${[!__tab.pagePath, __tab.pagePath]}"
														}
													}
												],
												"id":"Vfor41990",
												"componentName":"VFor",
												"props":{
													"forEach":"${$.shareData.var_viewData.tabsInfo}",
													"forEachKey":"__tab",
													"index":"$index"
												},
												"tags":[
													"relateTabsRoot"
												]
											},
											{
												"componentType":"custom",
												"children":[
													{
														"componentType":"custom",
														"children":[
															{
																"children":[
																	{
																		"componentType":"custom",
																		"children":[
																			{
																				"componentType":"custom",
																				"children":[
																					{
																						"componentType":"custom",
																						"children":[
																							{
																								"componentName":"TextWrapper",
																								"id":"TextWrapper-3ra5FBAm",
																								"props":{
																									"children":"审批流程"
																								},
																								"attrs":{
																									"style":{}
																								}
																							}
																						],
																						"componentName":"DivWrapper",
																						"id":"DivWrapper-r4Bs4dr8",
																						"attrs":{
																							"style":{
																								"margin-bottom":"10xpx"
																							}
																						},
																						"props":{
																							"name":"DivWrapper"
																						}
																					},
																					{
																						"libraryName":"@jd/joyui",
																						"componentType":"UILibrary",
																						"children":[],
																						"action":{
																							"fire":{}
																						},
																						"componentName":"ModelFlow",
																						"id":"ModelFlow-rdPWTHd7",
																						"version":"1.8.13",
																						"props":{
																							"activeStep":0,
																							"steps":"${$.shareData.flowInfo.approvalDetail ? $.shareData.flowInfo.approvalDetail.approvalNodeList : []}"
																						}
																					}
																				],
																				"componentName":"DivWrapper",
																				"id":"DivWrapper-zRcm6Tcd",
																				"title":"审批流程",
																				"attrs":{
																					"style":{
																						"border":"1px solid #EBEEF5",
																						"border-radius":"4px",
																						"padding":"10px",
																						"margin-bottom":"10px"
																					}
																				},
																				"props":{
																					"name":"DivWrapper"
																				}
																			},
																			{
																				"componentType":"custom",
																				"children":[
																					{
																						"children":[
																							{
																								"children":[
																									{
																										"componentType":"custom",
																										"children":[
																											{
																												"componentName":"TableColumn",
																												"id":"TableColumn-2YrF2fEi",
																												"title":"序号列",
																												"props":{
																													"width":"64px",
																													"label":"序号",
																													"type":"index"
																												}
																											},
																											{
																												"componentType":"custom",
																												"componentName":"TableColumn",
																												"id":"TableColumn-5pHeyGiN",
																												"title":"任务名称 TableColumn",
																												"props":{
																													"show-overflow-tooltip":true,
																													"prop":"taskName",
																													"column-key":"",
																													"index":0,
																													"label":"任务名称",
																													"v-slot":"",
																													"min-width":"154px"
																												},
																												"attrs":{
																													"style":{
																														"width":"154px"
																													}
																												}
																											},
																											{
																												"componentType":"custom",
																												"children":[
																													{
																														"libraryName":"@jd/joyui",
																														"componentType":"UILibrary",
																														"componentName":"ErpLcdp",
																														"id":"ErpLcdp-RnDatGH3",
																														"version":"1.8.15",
																														"props":{
																															"data":"${scope.row}",
																															"displayName":true,
																															"vertical":true,
																															"avatar":true
																														}
																													}
																												],
																												"componentName":"TableColumn",
																												"id":"TableColumn-D6d8r32E",
																												"title":"当前审批人 TableColumn",
																												"props":{
																													"show-overflow-tooltip":true,
																													"prop":"name",
																													"column-key":"",
																													"index":0,
																													"label":"审批人",
																													"v-slot":"scope",
																													"min-width":"154px"
																												},
																												"attrs":{
																													"style":{
																														"width":"154px"
																													}
																												}
																											},
																											{
																												"componentType":"custom",
																												"children":[
																													{
																														"componentName":"TextWrapper",
																														"id":"TextWrapper-r6zhWN2S",
																														"props":{
																															"children":"${scope.row.optStatusVal}"
																														},
																														"attrs":{
																															"style":{
																																"background-color":"${scope.row.optStatus == '2' || scope.row.optStatus == '11' ? '#feeaec' : 'rgba(41,204,49,0.1)'}",
																																"border-radius":"28px",
																																"padding":"0 17px",
																																"color":"${scope.row.optStatus == '2' || scope.row.optStatus == '11' ? '#f52f3e' : '#29CC31'}",
																																"display":"inline-block",
																																"line-height":"28px",
																																"height":"28px",
																																"text-align":"center"
																															}
																														}
																													}
																												],
																												"componentName":"TableColumn",
																												"id":"TableColumn-8szsXpEW",
																												"title":"审批结果 TableColumn",
																												"props":{
																													"show-overflow-tooltip":true,
																													"prop":"optStatusVal",
																													"column-key":"",
																													"index":0,
																													"label":"审批结果",
																													"v-slot":"scope",
																													"min-width":"154px"
																												},
																												"attrs":{
																													"style":{
																														"width":"154px"
																													}
																												}
																											},
																											{
																												"componentType":"custom",
																												"children":[
																													{
																														"componentName":"TextWrapper",
																														"id":"TextWrapper-pM5cRQtY",
																														"props":{
																															"children":"${scope.row.approveContent ? scope.row.approveContent : \"-\"}"
																														}
																													}
																												],
																												"componentName":"TableColumn",
																												"id":"TableColumn-HtKn8dP3",
																												"title":"审批意见 TableColumn",
																												"props":{
																													"show-overflow-tooltip":true,
																													"prop":"approveContent",
																													"column-key":"",
																													"width":"320",
																													"index":0,
																													"label":"审批意见",
																													"v-slot":"scope",
																													"min-width":"100"
																												},
																												"chosen":false,
																												"attrs":{
																													"style":{
																														"width":"320px"
																													}
																												}
																											},
																											{
																												"componentType":"custom",
																												"componentName":"TableColumn",
																												"id":"TableColumn-dR7ZkfD7",
																												"title":"审批时间 TableColumn",
																												"props":{
																													"show-overflow-tooltip":true,
																													"prop":"approveTime",
																													"width":"194",
																													"index":0,
																													"label":"审批时间",
																													"v-slot":"",
																													"min-width":"100"
																												},
																												"chosen":false,
																												"attrs":{
																													"style":{
																														"width":"194px"
																													}
																												}
																											},
																											{
																												"componentType":"custom",
																												"componentName":"TableColumn",
																												"id":"TableColumn-G8rjYbKT",
																												"title":"创建时间 TableColumn",
																												"props":{
																													"show-overflow-tooltip":true,
																													"prop":"createTime",
																													"width":"",
																													"index":0,
																													"label":"创建时间",
																													"v-slot":"",
																													"min-width":"194"
																												},
																												"chosen":false,
																												"attrs":{
																													"style":{
																														"width":"194px"
																													}
																												}
																											}
																										],
																										"id":"Table-Qy6mFJGc",
																										"componentName":"Table",
																										"props":{
																											"empty-text":"  ",
																											"data":"${$.shareData.flowInfo.approvalDetail ? $.shareData.flowInfo.approvalDetail.approvedTaskList : []}",
																											"size":"small",
																											"tree-props":"${{ hasChildren: \"hasChildren\", children: \"children\" }}",
																											"current-row-key":"",
																											"row-class-name":"",
																											"show-header":true,
																											"header-row-class-name":"tableHeader"
																										},
																										"chosen":false,
																										"attrs":{
																											"style":{},
																											"class":"page_table"
																										}
																									}
																								],
																								"componentName":"CollapseItem",
																								"id":"CollapseItem-JFkswTG7",
																								"props":{
																									"disable":"${false}",
																									"name":"${__item.name}",
																									"title":"${__item.title}",
																									"key":"${__item.name}"
																								},
																								"attrs":{
																									"style":{
																										"margin-bottom":"10px"
																									}
																								}
																							}
																						],
																						"id":"VFor-z6eXTDKz",
																						"componentName":"VFor",
																						"props":{
																							"forEach":"${[{ name: '1', title: '审批历史' }]}",
																							"forEachKey":"__item"
																						},
																						"attrs":{
																							"style":{}
																						}
																					}
																				],
																				"componentName":"Collapse",
																				"id":"Collapse-AzdaeWwW",
																				"props":{
																					"accordion":false,
																					"v-model":"1"
																				}
																			},
																			{
																				"componentType":"custom",
																				"children":[
																					{
																						"children":[
																							{
																								"children":[
																									{
																										"children":[
																											{
																												"componentType":"custom",
																												"children":[
																													{
																														"componentType":"custom",
																														"componentName":"TextWrapper",
																														"id":"TextWrapper-2sSFtk8p",
																														"props":{
																															"children":"暂无数据"
																														},
																														"attrs":{
																															"style":{}
																														}
																													}
																												],
																												"componentName":"DivWrapper",
																												"id":"DivWrapper-eHXx8zPX",
																												"title":"评论无数据",
																												"attrs":{
																													"style":{
																														"margin-bottom":"10px",
																														"text-align":"center"
																													}
																												},
																												"props":{
																													"name":"DivWrapper"
																												}
																											},
																											{
																												"componentType":"custom",
																												"children":[
																													{
																														"children":[
																															{
																																"componentType":"custom",
																																"children":[
																																	{
																																		"componentType":"custom",
																																		"children":[
																																			{
																																				"componentName":"TextWrapper",
																																				"id":"TextWrapper-SiYc8GsC",
																																				"props":{
																																					"children":"${__item.comment.val}"
																																				}
																																			}
																																		],
																																		"componentName":"DivWrapper",
																																		"id":"DivWrapper-J7jcrcAF",
																																		"attrs":{
																																			"style":{}
																																		},
																																		"props":{
																																			"name":"DivWrapper"
																																		}
																																	},
																																	{
																																		"componentType":"custom",
																																		"children":[
																																			{
																																				"componentName":"TextWrapper",
																																				"id":"TextWrapper-wPpTSaxf",
																																				"props":{
																																					"children":"${__item.comment.msg}"
																																				},
																																				"attrs":{
																																					"style":{
																																						"padding-left":"10px"
																																					}
																																				}
																																			}
																																		],
																																		"componentName":"DivWrapper",
																																		"id":"DivWrapper-e8MiYyH3",
																																		"attrs":{
																																			"style":{
																																				"margin-top":"5px",
																																				"margin-bottom":"5px"
																																			}
																																		},
																																		"props":{
																																			"name":"DivWrapper"
																																		}
																																	},
																																	{
																																		"componentType":"custom",
																																		"children":[
																																			{
																																				"children":[
																																					{
																																						"componentType":"custom",
																																						"children":[
																																							{
																																								"componentType":"custom",
																																								"children":[
																																									{
																																										"componentName":"TextWrapper",
																																										"id":"TextWrapper-tJHT2ket",
																																										"props":{
																																											"children":"${replyItem.val}"
																																										},
																																										"attrs":{
																																											"style":{
																																												"margin-right":"5px"
																																											}
																																										}
																																									}
																																								],
																																								"componentName":"DivWrapper",
																																								"id":"DivWrapper-fWpSzDYm",
																																								"attrs":{
																																									"style":{}
																																								},
																																								"props":{
																																									"name":"DivWrapper"
																																								}
																																							},
																																							{
																																								"componentName":"TextWrapper",
																																								"id":"TextWrapper-hGwDGiQx",
																																								"props":{
																																									"children":"${replyItem.msg}"
																																								},
																																								"attrs":{
																																									"style":{
																																										"padding-left":"10px"
																																									}
																																								}
																																							}
																																						],
																																						"componentName":"DivWrapper",
																																						"id":"DivWrapper-YBYsa3hm",
																																						"attrs":{
																																							"style":{
																																								"background-color":"#eee",
																																								"padding-top":"5px",
																																								"padding-left":"10px",
																																								"padding-bottom":"5px",
																																								"padding-right":"10px"
																																							}
																																						},
																																						"props":{
																																							"name":"DivWrapper"
																																						}
																																					}
																																				],
																																				"componentName":"VFor",
																																				"id":"VFor-CyBYpAYh",
																																				"props":{
																																					"forEach":"${__item.replyList}",
																																					"forEachKey":"replyItem",
																																					"index":"$index"
																																				},
																																				"attrs":{
																																					"style":{
																																						"background-color":"#dedede"
																																					}
																																				}
																																			}
																																		],
																																		"componentName":"DivWrapper",
																																		"id":"DivWrapper-hYca3EwA",
																																		"attrs":{
																																			"style":{}
																																		},
																																		"props":{
																																			"name":"DivWrapper"
																																		}
																																	}
																																],
																																"componentName":"DivWrapper",
																																"id":"DivWrapper-TxykmYBc",
																																"title":"评论item",
																																"attrs":{
																																	"style":{}
																																},
																																"props":{
																																	"name":"DivWrapper"
																																}
																															}
																														],
																														"componentName":"VFor",
																														"id":"VFor-Hm6aaMJR",
																														"props":{
																															"forEach":"${$.shareData.flowInfo.approvalComment}",
																															"forEachKey":"__item",
																															"index":"$index"
																														}
																													}
																												],
																												"componentName":"DivWrapper",
																												"id":"DivWrapper-ES2KkSSC",
																												"title":"评论有数据",
																												"attrs":{
																													"style":{
																														"padding-left":"0px",
																														"padding-right":"0px"
																													}
																												},
																												"props":{
																													"name":"DivWrapper"
																												}
																											}
																										],
																										"componentName":"VIf",
																										"id":"VIf-Xs2JYCTM",
																										"props":{
																											"conditions":"${[Array.isArray($.shareData.flowInfo.approvalComment) && $.shareData.flowInfo.approvalComment.length == 0]}"
																										},
																										"attrs":{
																											"style":{}
																										}
																									}
																								],
																								"componentName":"CollapseItem",
																								"id":"CollapseItem-BdCDDdNb",
																								"props":{
																									"disable":"${false}",
																									"name":"${__item.name}",
																									"title":"${__item.title}",
																									"key":"${__item.name}"
																								}
																							}
																						],
																						"id":"VFor-nJS4dMCw",
																						"componentName":"VFor",
																						"props":{
																							"forEach":"${[{ name: '1', title: '评论' }]}",
																							"forEachKey":"__item"
																						},
																						"attrs":{
																							"style":{
																								"margin-top":"10px"
																							}
																						}
																					}
																				],
																				"componentName":"Collapse",
																				"id":"Collapse-NSdnJiQM",
																				"props":{
																					"accordion":false,
																					"v-model":"1"
																				}
																			}
																		],
																		"componentName":"DivWrapper",
																		"id":"DivWrapper-dFjsiwQE",
																		"title":"模型关联流程信息",
																		"attrs":{
																			"style":{
																				"margin-top":"10px"
																			}
																		},
																		"props":{
																			"name":"DivWrapper"
																		}
																	}
																],
																"componentName":"VIf",
																"id":"VIf-6QWW4262",
																"props":{
																	"conditions":"${[$.shareData.var_tabActiveName == 'modelFlowTab' && $.shareData.flowInfo]}"
																}
															}
														],
														"componentName":"TabPane",
														"id":"TabPane-FNPxsQQA",
														"props":{
															"name":"modelFlowTab",
															"label":"审批信息"
														},
														"attrs":{
															"style":{
																"background-color":"#fff",
																"padding":"0",
																"display":"block",
																"margin-bottom":"12px"
															}
														}
													}
												],
												"componentName":"VIf",
												"id":"VIf-eRzFdFCZ",
												"props":{
													"conditions":"${[$.shareData.showModelFlow]}"
												},
												"tags":[
													"modelFlowTabRoot"
												]
											}
										],
										"action":{
											"fire":{
												"tab-click":{
													"arguments":[
														"tab",
														"event"
													],
													"actions":[
														{
															"handler":"${function Fun_exzRsG(tab, event){\r/*dm*/
\nif (tab.name == 'business') {\r/*dm*/
\n  $.shareData.var_tabActiveNameAction(tab.name);\r/*dm*/
\n  return\r/*dm*/
\n}\r/*dm*/
\nconst __tab = $.shareData.var_viewData.tabsInfo.find(item => item.tableKey == tab.name)\r/*dm*/
\nif (tab.name != $.shareData.var_tabActiveName) {\r/*dm*/
\n  $.shareData.var_tabActiveNameAction(tab.name);\r/*dm*/
\n  if (!['first', 'fieldTrack', 'modelFlowTab'].includes(tab.name)) {\r/*dm*/
\n    // 清空组件内的值\r/*dm*/
\n    this.$refs['keywordSearch' + __tab.tableKey].keywordQuery = '';\r/*dm*/
\n  }\r/*dm*/
\n}\r/*dm*/
\n\r/*dm*/
\n\r/*dm*/
\n$.functions.fun_tabClick({ name: $.shareData.var_tabActiveName }, __tab);\r/*dm*/
\n}}",
															"type":"function"
														}
													]
												}
											}
										},
										"id":"Tabs29100",
										"componentName":"Tabs",
										"title":"标签容器",
										"attrs":{
											"class":"detail-tabs"
										},
										"props":{
											"v-model":"${$.shareData.var_tabActiveName}",
											"tab-position":"top"
										}
									}
								],
								"componentName":"VIf",
								"id":"VIf-fjJ8Afc7",
								"props":{
									"conditions":"${() => {/*dm*/
\n  // 没有任何tab时/*dm*/
\n  if ([/*dm*/
\n    $.shareData.var_showBaseInfoTab && $.shareData.var_viewData?.baseInfo?.length,/*dm*/
\n    $.shareData.var_showFieldTrack,/*dm*/
\n    $.shareData.var_viewData?.tabsInfo?.length > 0,/*dm*/
\n    $.shareData.showModelFlow/*dm*/
\n  ].filter(Boolean).length === 0) {/*dm*/
\n    return [false]/*dm*/
\n  }/*dm*/
\n/*dm*/
\n  return [$.shareData.var_setting.enableTabs !== false];/*dm*/
\n}}"
								}
							}
						],
						"id":"ModelDrivenMarkup56213",
						"componentName":"ModelDrivenMarkup",
						"title":"模型辅助配置",
						"version":"1.8.3",
						"attrs":{
							"style":{
								"flex-direction":"column",
								"flex":1,
								"display":"flex"
							}
						},
						"props":{
							"modelName":"ceshimoxing",
							"modelText":"测试模型",
							"formType":"info",
							"tools":[]
						}
					}
				],
				"id":"DivWrapper43892",
				"componentName":"DivWrapper",
				"title":"测试模型详情页面",
				"attrs":{
					"class":"detailPageRoot"
				}
			}
		],
		"functions":[
			{
				"default":"${function fun_verifRole(code){/*dm*/
\n            if(!(code in $.shareData.var_pageRole)) {/*dm*/
\n                return true;/*dm*/
\n            }/*dm*/
\n            return $.shareData.var_pageRole[code]/*dm*/
\n}}",
				"description":"高级筛选是否启用有多少个",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_verifRole"
			},
			{
				"default":"${function handleTabClick(tab, __tab) {/*dm*/
\n          // 点击tab时，保存当前tab状态/*dm*/
\n          const detailId = $.functions.fun_getQueryValue('detailId');/*dm*/
\n          const params = $.bom.page.query();/*dm*/
\n          $.bom.page.replaceQuery({/*dm*/
\n            ...params,/*dm*/
\n            tabActive: tab.name/*dm*/
\n          })/*dm*/
\n          if (tab.name === 'fieldTrack') {/*dm*/
\n              const modelName = $.shareData.var_setting.modelName;/*dm*/
\n              await $.shareData.api_getFieldTrackListAction({/*dm*/
\n                  id: detailId,/*dm*/
\n                  modelName,/*dm*/
\n                  pageNum: 1,/*dm*/
\n                  pageSize: 1000/*dm*/
\n              });/*dm*/
\n              $.shareData.var_fieldTrackList = $.shareData.api_getFieldTrackList?.data.records || [];/*dm*/
\n              return;/*dm*/
\n          } else {/*dm*/
\n              if (!__tab?.modelName) {/*dm*/
\n                  return;/*dm*/
\n              }/*dm*/
\n              const modelName = __tab.modelName/*dm*/
\n            /*dm*/
\n              $.shareData.var_baseSearchQuery[__tab.tableKey].modelName = modelName;/*dm*/
\n              $.shareData.var_baseSearchQuery[__tab.tableKey].pageNum = 1;/*dm*/
\n              console.log(__tab.tableKey, 'bcbcbc')/*dm*/
\n              $.shareData.var_baseSearchQuery[__tab.tableKey].queryConditions = __tab.queryConditions;/*dm*/
\n              await $.functions.fun_loadCache(__tab.tableKey, __tab.modelname)/*dm*/
\n              $.functions.fun_loadTableData(modelName, __tab.tableKey)/*dm*/
\n          }/*dm*/
\n         /*dm*/
\n}}",
				"description":"关联列表点击统一处理",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_tabClick"
			},
			{
				"default":"${/*dm*/
\nfunction initGroup(tableKey, modelViewData) {/*dm*/
\n  // 初始化分组数据/*dm*/
\n  const var_basePageSetting = {/*dm*/
\n    'columnPlanId': null,/*dm*/
\n    'showPagination': true,/*dm*/
\n    'paginationMode': 'complete',/*dm*/
\n    'paginationAlign': 'right',/*dm*/
\n    'pageSizes': [10, 20, 30, 40, 50],/*dm*/
\n    'showHeader': true,/*dm*/
\n    'stripe': false,/*dm*/
\n    'showNo': true,/*dm*/
\n    'selectRowType': 'single',/*dm*/
\n    'border': false,/*dm*/
\n    'mode': 'normal',/*dm*/
\n    'batchDelFlag': false,/*dm*/
\n    'addFlag': true,/*dm*/
\n    'exportDataFlag': false,/*dm*/
\n    'addPage': '',/*dm*/
\n    'editPage': '',/*dm*/
\n    'detailPage': '',/*dm*/
\n    'headerEllipsis': true,/*dm*/
\n    'cellEllipsis': true,/*dm*/
\n    'textAlign': 'left',/*dm*/
\n    'showDetail': true,/*dm*/
\n    'showDel': false,/*dm*/
\n    'showEdit': false,/*dm*/
\n    'columnShowIcon': false,/*dm*/
\n    'columnShowText': true,/*dm*/
\n    'resizeColumn': true,/*dm*/
\n  }/*dm*/
\n  const var_baseSearchQuery = {/*dm*/
\n    \"querySorts\": [/*dm*/
\n     /*dm*/
\n    ]/*dm*/
\n  }/*dm*/
\n/*dm*/
\n  if(modelViewData) {/*dm*/
\n    var_baseSearchQuery.querySorts = [/*dm*/
\n      ...(modelViewData.listViews.viewSortList.map(a => {/*dm*/
\n        return {/*dm*/
\n          fieldName: a.fieldName,/*dm*/
\n          desc: a.sortOrder == 'desc'/*dm*/
\n        }/*dm*/
\n      }) || [])/*dm*/
\n    ]/*dm*/
\n  }/*dm*/
\n/*dm*/
\n  $.shareData.var_basePageParams[tableKey] = {/*dm*/
\n    \"pageNum\": 1,/*dm*/
\n    \"pageSize\": 10,/*dm*/
\n    \"totalPage\": 0,/*dm*/
\n    \"totalSize\": 0/*dm*/
\n  }/*dm*/
\n  $.shareData.var_baseSearchQuery[tableKey] = {/*dm*/
\n    ...var_baseSearchQuery/*dm*/
\n  }/*dm*/
\n  $.shareData.var_basePageSetting[tableKey] = {/*dm*/
\n    ...var_basePageSetting/*dm*/
\n  }/*dm*/
\n}/*dm*/
\n}",
				"description":"",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_initSubModelTable"
			},
			{
				"default":"${function handle(newView = []) {/*dm*/
\n  // 未配置视图或视图数据异常，字段则按照默认规则展示/*dm*/
\n  console.log(newView)/*dm*/
\n/*dm*/
\n  const pageInfo = $.shareData.var_formInfo;/*dm*/
\n/*dm*/
\n  const modelFields = $.global.shareData.modelsInfoData.fieldsMap[$.shareData.var_setting.modelName];/*dm*/
\n/*dm*/
\n  const subModelList = []/*dm*/
\n/*dm*/
\n  const newGroup = newView.viewGroupList.map(item => {/*dm*/
\n    let groupConfig = { ...$.functions.fun_getGroupConfig(item.groupCode) }/*dm*/
\n/*dm*/
\n    if (!groupConfig.enableTitle) {/*dm*/
\n      groupConfig.isUnfold = true;/*dm*/
\n    }/*dm*/
\n/*dm*/
\n    return {/*dm*/
\n      displayLocation: item.displayLocation,/*dm*/
\n      groupCode: item.groupCode,/*dm*/
\n      enableState: item.enableState,/*dm*/
\n      showIndex: item.showIndex,/*dm*/
\n      groupName: item.groupName,/*dm*/
\n      config: groupConfig,/*dm*/
\n      collapsed: groupConfig.isUnfold ? [item.groupName] : [],/*dm*/
\n      fieldList: (item.groupInfoList || [])?.map(item => {/*dm*/
\n        const field = modelFields.get(item.fieldName);/*dm*/
\n        return {/*dm*/
\n          fieldName: field.fieldName,/*dm*/
\n          businessType: field.businessType,/*dm*/
\n          fieldProps: field,/*dm*/
\n          value: pageInfo[field.fieldName]/*dm*/
\n        }/*dm*/
\n      }),/*dm*/
\n      subModelList: item.viewRelatedList.map(item => {/*dm*/
\n        $.functions.fun_initSubModelTable(item.relatedListViewCode)/*dm*/
\n        const newItem = {/*dm*/
\n          ...item,/*dm*/
\n          isSubModel: true,/*dm*/
\n          modelName: item.relatedModelName,/*dm*/
\n          fieldName: item.relatedFieldName,/*dm*/
\n          tableKey: item.relatedListViewCode,/*dm*/
\n          ...(groupConfig?.table?.[item.relatedListViewCode] || {})/*dm*/
\n        };/*dm*/
\n        subModelList.push(newItem)/*dm*/
\n        return newItem;/*dm*/
\n      })/*dm*/
\n    }/*dm*/
\n  }).sort((a, b) => a.showIndex - b.showIndex)/*dm*/
\n/*dm*/
\n  const tabs = newView.viewRelatedList.map(async item => {/*dm*/
\n      let modelInfo = await $.global.functions.getModelDictionaryInfo(item.relatedModelName);/*dm*/
\n      $.functions.fun_initSubModelTable(item.relatedListViewCode, item)/*dm*/
\n      const newItem = {/*dm*/
\n        ...item,/*dm*/
\n        tabName: item.relatedShowName,/*dm*/
\n        tableKey: item.relatedListViewCode,/*dm*/
\n        modelName: item.relatedModelName,/*dm*/
\n        fieldName: item.relatedFieldName,/*dm*/
\n        keywordQuery: {/*dm*/
\n/*dm*/
\n        },/*dm*/
\n        keyWordList: item.listViews.keyWordList.map(item => item.fieldName),/*dm*/
\n        queryConditions: [{/*dm*/
\n          value: $.shareData.var_formInfo.id,/*dm*/
\n          fieldName: item.relatedFieldName,/*dm*/
\n          conditionType: \"EQ\"/*dm*/
\n        }]/*dm*/
\n      };/*dm*/
\n      if(modelInfo.tree) {/*dm*/
\n        newItem.pagePath = `/${modelInfo.modelName}/list`/*dm*/
\n      }/*dm*/
\n      subModelList.push(newItem);/*dm*/
\n      return newItem/*dm*/
\n  })/*dm*/
\n/*dm*/
\n  $.shareData.var_viewDataAction(/*dm*/
\n    {/*dm*/
\n      keyInfo: newGroup.filter(item => item.displayLocation == 0 && item.enableState),/*dm*/
\n      baseInfo: newGroup.filter(item => item.displayLocation == 1 && item.enableState),/*dm*/
\n      tabsInfo: await Promise.all(tabs)/*dm*/
\n    }/*dm*/
\n  )/*dm*/
\n/*dm*/
\n  // 子模型处理/*dm*/
\n  await Promise.all(subModelList.map(async subModel => {/*dm*/
\n    await $.global.functions.getModelDictionaryInfo(subModel.relatedModelName);/*dm*/
\n    /*dm*/
\n    let allFieldList = []/*dm*/
\n    // 解析视图/*dm*/
\n    subModel?.listViews?.viewInfoList?.map(item => {/*dm*/
\n      const modelField = $.global.shareData.modelsInfoData.fieldsMap[subModel.relatedModelName].get(item.fieldName);/*dm*/
\n/*dm*/
\n      if (item.isEnable && !item.relatedFieldList?.length) {/*dm*/
\n          allFieldList.push({/*dm*/
\n            ...item,/*dm*/
\n            name: item.fieldName,/*dm*/
\n            label: modelField.fieldText,/*dm*/
\n            fieldWidth: item.displayWidth,/*dm*/
\n            defaultSelectValue: modelField.defaultSelect,/*dm*/
\n            modelField,/*dm*/
\n          })/*dm*/
\n      }/*dm*/
\n/*dm*/
\n      if (item.relatedFieldList?.length > 0) {/*dm*/
\n        const field = modelField;/*dm*/
\n        console.log(field, 'nnnnnnnn')/*dm*/
\n        item.relatedFieldList.map(item => {/*dm*/
\n          const relatedField = field.relatedModel.modelFields.find(it1 => it1.fieldName === item.relatedFieldName)/*dm*/
\n          // 再添加循环的数据/*dm*/
\n          if (item.isEnable) {/*dm*/
\n            allFieldList.push({/*dm*/
\n              ...item,/*dm*/
\n              name: item.fieldName,/*dm*/
\n              fieldWidth: item.displayWidth,/*dm*/
\n              fieldName: `${item.fieldName}.${item.relatedFieldName}`,/*dm*/
\n              fieldText: relatedField.fieldText,/*dm*/
\n              label: relatedField.fieldText,/*dm*/
\n              businessType: field.businessType,/*dm*/
\n              index: item.showIndex,/*dm*/
\n              modelField: {/*dm*/
\n                ...field,/*dm*/
\n                calculationFieldName: item.relatedFieldName/*dm*/
\n              }/*dm*/
\n            })/*dm*/
\n          }/*dm*/
\n        })/*dm*/
\n      }/*dm*/
\n/*dm*/
\n      return null/*dm*/
\n    })/*dm*/
\n    /*dm*/
\n    allFieldList = allFieldList.sort((a,b) => a.showIndex - b.showIndex);/*dm*/
\n    this.$set($.shareData.var_baseTableColumnShow, subModel.tableKey, allFieldList);/*dm*/
\n/*dm*/
\n    // 子模型直接查询/*dm*/
\n    if (subModel.isSubModel) {/*dm*/
\n      let data = await $.shareData.api_getListAction({/*dm*/
\n        modelName: subModel.relatedModelName,/*dm*/
\n        pageSize: 1000,/*dm*/
\n        attributes: allFieldList.map(item => item.fieldName),/*dm*/
\n        pageNum: 1,/*dm*/
\n        querySorts: [/*dm*/
\n          ...(subModel?.viewSortList?.map(item => {/*dm*/
\n            return {/*dm*/
\n              fieldName: item.fieldName,/*dm*/
\n              desc: item.sortOrder == 'desc'/*dm*/
\n            }/*dm*/
\n          }) || [])/*dm*/
\n        ],/*dm*/
\n        queryConditions: [/*dm*/
\n          {/*dm*/
\n            value: $.shareData.var_formInfo.id,/*dm*/
\n            fieldName: subModel.relatedFieldName,/*dm*/
\n            conditionType: \"EQ\"/*dm*/
\n          }/*dm*/
\n        ]/*dm*/
\n      }).catch(err => console.log(err));/*dm*/
\n      let transform = await $.global.functions.modelValueFormat(subModel.relatedModelName)/*dm*/
\n      this.$set($.shareData.var_baseTableList, subModel.tableKey, data?.data?.records?.map(item => {/*dm*/
\n        let data = transform.input(item);/*dm*/
\n        return data/*dm*/
\n      }) || []);/*dm*/
\n    }/*dm*/
\n    return/*dm*/
\n  }))/*dm*/
\n/*dm*/
\n/*dm*/
\n/*dm*/
\n  return;/*dm*/
\n/*dm*/
\n  if (!Array.isArray(newView) || newView.length === 0) {/*dm*/
\n    $.shareData.keyFieldList = [];/*dm*/
\n    return;/*dm*/
\n  }/*dm*/
\n  let view = newView.sort((a, b) => { return a.groupOrder - b.groupOrder })/*dm*/
\n  // 批量调用子模型数据/*dm*/
\n  const allPromise = [];/*dm*/
\n  // 防止同一模型多次调用列表接口/*dm*/
\n  let modelMap = {};/*dm*/
\n  const { detailId } = $.bom.page.query();/*dm*/
\n  // 分组处理/*dm*/
\n  view.filter(group => group.subModel).map(group => {/*dm*/
\n    const models = JSON.parse(group.subModel) || [];/*dm*/
\n    models.map(model => {/*dm*/
\n      $.functions.fun_initSubModelTable(model);/*dm*/
\n      if (!modelMap[model.modelName]) {/*dm*/
\n        modelMap[model.modelName] = true;/*dm*/
\n        allPromise.push($.global.functions.getModelDictionaryInfo(model.modelName));/*dm*/
\n      }/*dm*/
\n      const tableKey = model.modelName + '|' + model.fieldName;/*dm*/
\n      allPromise.push($.functions.fun_loadTableDefaultPlanData(model.modelName, tableKey));/*dm*/
\n      let tableParams = {};/*dm*/
\n      if (detailId) {/*dm*/
\n        tableParams = {/*dm*/
\n          \"pageNum\": 1,/*dm*/
\n          \"pageSize\": 1000,/*dm*/
\n          queryConditions: [{/*dm*/
\n            fieldName: model.fieldName,/*dm*/
\n            conditionType: \"EQ\",/*dm*/
\n            value: detailId/*dm*/
\n          }]/*dm*/
\n        }/*dm*/
\n      }/*dm*/
\n      allPromise.push($.functions.fun_loadTableData(model.modelName, tableKey, tableParams));/*dm*/
\n    })/*dm*/
\n  })/*dm*/
\n  await Promise.all(allPromise);/*dm*/
\n  // 格式化字段数据/*dm*/
\n  function formatModel(viewField) {/*dm*/
\n    const { fieldName } = viewField;/*dm*/
\n    const fieldObj = modelFields.get(fieldName);/*dm*/
\n    if (!fieldObj) return undefined;/*dm*/
\n    return {/*dm*/
\n      fieldName,/*dm*/
\n      value: pageInfo[fieldName],/*dm*/
\n      formType: 'info',/*dm*/
\n      businessType: fieldObj.businessType,/*dm*/
\n      fieldProps: fieldObj/*dm*/
\n    }/*dm*/
\n  }/*dm*/
\n  // 格式化分组数据/*dm*/
\n  function formatGroup(group) {/*dm*/
\n    const { fieldName, groupName, subModel } = group;/*dm*/
\n    let fieldList = [];/*dm*/
\n    if (fieldName) {/*dm*/
\n      const tempList = JSON.parse(fieldName) || []/*dm*/
\n      fieldList = tempList.filter(i => i.fieldName).sort((a, b) => a.order - b.order).map(formatModel).filter(i => i);/*dm*/
\n    }/*dm*/
\n    let subModelList = []/*dm*/
\n    if (subModel) {/*dm*/
\n      const tempList = JSON.parse(subModel) || []/*dm*/
\n      subModelList = tempList;/*dm*/
\n    }/*dm*/
\n    return {/*dm*/
\n      ...group,/*dm*/
\n      collapsed: [groupName],/*dm*/
\n      fieldList,/*dm*/
\n      subModelList/*dm*/
\n    }/*dm*/
\n  }/*dm*/
\n  // 关键信息分区/*dm*/
\n  const keyGroups = view/*dm*/
\n    .filter(group => group.area === 1)/*dm*/
\n    .map(formatGroup);/*dm*/
\n  console.log(keyGroups, 'nnnnnnn')/*dm*/
\n  $.shareData.var_keyGroups = keyGroups;/*dm*/
\n  // 基础信息分区/*dm*/
\n  const baseGroups = view/*dm*/
\n    .filter(group => group.area === 2)/*dm*/
\n    .map(formatGroup);/*dm*/
\n  $.shareData.var_baseGroups = baseGroups;/*dm*/
\n  // 基本信息Tab状态/*dm*/
\n  $.shareData.var_showBaseInfoTab = baseGroups.some(item => item.groupEnable);/*dm*/
\n}}",
				"description":"视图分组处理",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_initViewGroup"
			},
			{
				"default":"${function getQueryValue(queryName){/*dm*/
\n                  const params = $.bom.page.params()?.data || {}/*dm*/
\n                  const query = $.bom.page.query();/*dm*/
\n                  // 区分打开方式 page or dialog/*dm*/
\n                  if (params?.pageType) { // 子模型/*dm*/
\n                    $.shareData.var_showHeader = false;/*dm*/
\n                    return params[queryName] || query[queryName];/*dm*/
\n                  } else {/*dm*/
\n                    return query[queryName];/*dm*/
\n                  }/*dm*/
\n                }}",
				"description":"获取页面id",
				"type":"function",
				"title":"",
				"key":"fun_getQueryValue"
			},
			{
				"default":"${function getFormInfo(modelInfo) {/*dm*/
\n  const detailId = $.shareData.var_detailId || $.functions.fun_getQueryValue('detailId');/*dm*/
\n/*dm*/
\n  const view = await $.shareData.api_getDetailViewAction({/*dm*/
\n    modelName: $.shareData.var_setting.modelName,/*dm*/
\n    pageKey: $.shareData.var_setting.pageKey/*dm*/
\n  })/*dm*/
\n/*dm*/
\n/*dm*/
\n  if (detailId) {/*dm*/
\n/*dm*/
\n/*dm*/
\n/*dm*/
\n    $.shareData.var_loadingAction(true)/*dm*/
\n    // await $.shareData.api_getPageInfoAction({ id: detailId, modelName: $.shareData.var_setting.modelName });/*dm*/
\n/*dm*/
\n    await $.shareData.getPermissionUserInfoAction({ id: detailId })/*dm*/
\n    // 页面数据/*dm*/
\n    // const { data } = $.shareData.api_getPageInfo;/*dm*/
\n    const { data } = $.shareData.getPermissionUserInfo;/*dm*/
\n    // 模型关联流程信息/*dm*/
\n    if (data.wfOpen && data.flowInfo) {/*dm*/
\n      $.shareData.showModelFlowAction(data.wfOpen);/*dm*/
\n/*dm*/
\n      //后台数据处理/*dm*/
\n      const approvedTaskList = data.flowInfo.approvalDetail.approvedTaskList/*dm*/
\n      const approvalNodeList = data.flowInfo.approvalDetail.approvalNodeList/*dm*/
\n/*dm*/
\n      approvedTaskList.forEach(item => {/*dm*/
\n        item.optStatusVal = $.shareData.optStatusMap[item.optStatus]/*dm*/
\n        item.erp = item.pin;/*dm*/
\n        item.orgTierName = item.orgFullName;/*dm*/
\n        item.headImage = item.headImg/*dm*/
\n          ? item.headImg/*dm*/
\n          : 'https://s3.cn-north-1.jdcloud-oss.com/jz-material/lcapicon/16569286533561534875558738759042.svg';/*dm*/
\n      })/*dm*/
\n/*dm*/
\n      let activeStepIndex = -1;/*dm*/
\n      const steps = approvalNodeList.map((item, index) => {/*dm*/
\n        const newItem = { ...item };/*dm*/
\n        if (newItem.status === 1) {/*dm*/
\n          newItem.iconType = 'flow-success';/*dm*/
\n/*dm*/
\n          if (newItem.approvedTaskList) {/*dm*/
\n            newItem.nodeList = newItem.approvedTaskList.map((item) => {/*dm*/
\n              item.erp = item.pin;/*dm*/
\n              item.orgTierName = item.orgFullName;/*dm*/
\n              item.headImage = item.headImg/*dm*/
\n                ? item.headImg/*dm*/
\n                : 'https://s3.cn-north-1.jdcloud-oss.com/jz-material/lcapicon/16569286533561534875558738759042.svg';/*dm*/
\n              return item;/*dm*/
\n            });/*dm*/
\n          }/*dm*/
\n        } else if (newItem.status === 0) {/*dm*/
\n          newItem.iconType = 'flow-progress';/*dm*/
\n/*dm*/
\n          if (newItem.approvingTaskList) {/*dm*/
\n            newItem.nodeList = newItem.approvingTaskList.map((item) => {/*dm*/
\n              item.erp = item.pin;/*dm*/
\n              item.orgTierName = item.orgFullName;/*dm*/
\n              item.headImage = item.headImg/*dm*/
\n                ? item.headImg/*dm*/
\n                : 'https://s3.cn-north-1.jdcloud-oss.com/jz-material/lcapicon/16569286533561534875558738759042.svg';/*dm*/
\n              return item;/*dm*/
\n            });/*dm*/
\n          }/*dm*/
\n/*dm*/
\n          if (activeStepIndex === -1) {/*dm*/
\n            activeStepIndex = index;/*dm*/
\n          }/*dm*/
\n        }/*dm*/
\n/*dm*/
\n        return newItem;/*dm*/
\n      });/*dm*/
\n      // 修改当前审批节点的icon/*dm*/
\n      if (activeStepIndex !== -1) {/*dm*/
\n        steps[activeStepIndex].iconType = 'flow-way';/*dm*/
\n      }/*dm*/
\n      steps.shift();/*dm*/
\n      data.flowInfo.approvalDetail.approvalNodeList = steps;/*dm*/
\n      const flowInfo = { ...data.flowInfo };/*dm*/
\n      $.shareData.flowInfoAction(flowInfo)/*dm*/
\n/*dm*/
\n/*dm*/
\n      //评论数据处理/*dm*/
\n      const approvalComment = data.flowInfo.approvalComment/*dm*/
\n      approvalComment.forEach(item => {/*dm*/
\n        item.comment.val = `${item.comment.sendName}（${item.comment.sendErp}）${item.comment.createdTime}`/*dm*/
\n        if (item.replyList && item.replyList.length > 0) {/*dm*/
\n          item.replyList.forEach(replyItem => {/*dm*/
\n            replyItem.val = `${replyItem.sendName}（${replyItem.sendErp}） 回复 ${replyItem.toName}（${replyItem.toErp}） ${replyItem.createdTime}`/*dm*/
\n          })/*dm*/
\n        }/*dm*/
\n      })/*dm*/
\n/*dm*/
\n    }/*dm*/
\n/*dm*/
\n    const modelName = $.shareData.var_setting.modelName;/*dm*/
\n    // 获取转换Utils对象/*dm*/
\n    const transObj = await $.global.functions.modelValueFormat(modelName);/*dm*/
\n    const transData = transObj.input(data);/*dm*/
\n    $.shareData.var_formInfo = transData;/*dm*/
\n    // 字段跟踪/*dm*/
\n    let fields = $.global.shareData.modelsInfoData.fields[modelName];/*dm*/
\n    const hasFieldTrack = fields.filter(item => item.fieldTrack).length > 0;/*dm*/
\n    // console.log('hasFieldTrack', hasFieldTrack);/*dm*/
\n    $.shareData.showFieldTrack = hasFieldTrack;/*dm*/
\n    // console.log('hasFieldTrack', $.shareData.showFieldTrack);/*dm*/
\n    $.shareData.var_showFieldTrack = hasFieldTrack;/*dm*/
\n    // 根据分组信息渲染/*dm*/
\n    await $.functions.fun_initViewGroup(view.data);/*dm*/
\n    // 关联关系查询/*dm*/
\n    $.functions.fun_handleAssociated();/*dm*/
\n/*dm*/
\n/*dm*/
\n    // 业务组织/*dm*/
\n    // await $.shareData.getBusOrgListAction({ loginName: $.shareData.getPermissionUserInfo.data.login_name })/*dm*/
\n  } else {/*dm*/
\n    $.shareData.get_defaultListAction({ modelName: $.shareData.var_setting.modelName }).then(async res => {/*dm*/
\n      $.shareData.var_loadingAction(true);/*dm*/
\n      // 获取转换Utils对象/*dm*/
\n      const modelName = $.shareData.var_setting.modelName;/*dm*/
\n      const transObj = await $.global.functions.modelValueFormat(modelName);/*dm*/
\n      const transData = transObj.input(res.data);/*dm*/
\n      $.shareData.var_formInfo = transData;/*dm*/
\n      // 根据分组信息渲染/*dm*/
\n      await $.functions.fun_initViewGroup(view.data);/*dm*/
\n      // 关联关系查询/*dm*/
\n      $.functions.fun_handleAssociated();/*dm*/
\n    });/*dm*/
\n  }/*dm*/
\n  const name = $.shareData.var_formInfo?.name || $.shareData.var_formInfo?.id/*dm*/
\n  const title = name ? `${name}-${modelInfo.modelText}-详情` : `${modelInfo.modelText}-详情`;/*dm*/
\n  $.bom.getHostData(\"setTitle\", title);/*dm*/
\n}}",
				"description":"获取页面信息",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_initView"
			},
			{
				"default":"${/*dm*/
\nfunction Fun_Gs83RKm2() {/*dm*/
\n  const urlQuery = $.bom.page.query();/*dm*/
\n  let { tabActive = '' } = urlQuery || {};/*dm*/
\n  // 基本信息关闭 && 字段跟踪关闭/*dm*/
\n  if ($.functions.fun_getSubModel()) {/*dm*/
\n    tabActive = ''/*dm*/
\n  }/*dm*/
\n  let showBase = $.shareData.var_showBaseInfoTab && $.shareData.var_viewData.baseInfo.length > 0;/*dm*/
\n  if (showBase) {/*dm*/
\n    $.shareData.var_tabActiveName = tabActive || 'first';/*dm*/
\n  } else {/*dm*/
\n    if ($.shareData.var_showFieldTrack) {/*dm*/
\n      $.shareData.var_tabActiveName = tabActive || 'fieldTrack';/*dm*/
\n    } else {/*dm*/
\n      $.shareData.var_tabActiveName = $.shareData.var_viewData.tabsInfo.find(item => item.editorViewCode)?.relatedListViewCode || '';/*dm*/
\n    }/*dm*/
\n  }/*dm*/
\n  console.log($.shareData.var_viewData.tabsInfo, 'nbnbnb')/*dm*/
\n  $.functions.fun_tabClick({ name: $.shareData.var_tabActiveName }, $.shareData.var_viewData.tabsInfo.find(item => item.editorViewCode));/*dm*/
\n}/*dm*/
\n}",
				"description":"获取页面信息",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_handleAssociated"
			},
			{
				"default":"${/*dm*/
\n          function() {/*dm*/
\n            const params = $.bom.page.params()?.data || {}/*dm*/
\n            return !!params?.pageType/*dm*/
\n          }/*dm*/
\n        }",
				"description":"判断是否是子窗口",
				"type":"function",
				"title":"",
				"key":"fun_getSubModel"
			},
			{
				"default":"${function fun_ getGroupEnable(){/*dm*/
\n  if ($.shareData.var_baseGroups.length > 0) {/*dm*/
\n    if ($.shareData.var_baseGroups.some(item => item.groupEnable)) {/*dm*/
\n      return [/*dm*/
\n        {/*dm*/
\n          \"label\": \"基本信息\",/*dm*/
\n          \"name\": \"first\"/*dm*/
\n        }/*dm*/
\n      ];/*dm*/
\n    } else {/*dm*/
\n      return [];/*dm*/
\n    }/*dm*/
\n  }/*dm*/
\n}}",
				"description":"是否是系统基本信息",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_getGroupEnable"
			},
			{
				"default":"${function fun_loadTableData(modelName, modelKey, searchQuery = {}) {/*dm*/
\n      this.$set($.shareData.var_baseTableLoading, modelKey, true);/*dm*/
\n      const query = {/*dm*/
\n        modelName,/*dm*/
\n        ...($.shareData.var_baseSearchQuery[modelKey] || {}),/*dm*/
\n        ...($.shareData.var_basePageParams[modelKey] || {}),/*dm*/
\n        ...searchQuery,/*dm*/
\n        attributes: $.shareData.var_baseTableColumnShow[modelKey]?.map(item => item.fieldName)/*dm*/
\n      }/*dm*/
\n      // 流程数据条件过滤/*dm*/
\n      const modelInfo = await $.global.functions.getModelDictionaryInfo(modelName);/*dm*/
\n      console.log('modelInfo', modelInfo)/*dm*/
\n      if (modelInfo?.wfOpen) {/*dm*/
\n        query.queryConditions.push({/*dm*/
\n          \"fieldName\": \"approval_status\",/*dm*/
\n          \"valueType\": 0,/*dm*/
\n          \"conditionType\": \"EQ\",/*dm*/
\n          \"value\": \"3\"/*dm*/
\n        })/*dm*/
\n      }/*dm*/
\n      console.log('query', query)/*dm*/
\n      $.shareData.api_getListAction({/*dm*/
\n        ...query/*dm*/
\n      }).then(async item => {/*dm*/
\n        $.shareData.var_baseTableLoadingAction({/*dm*/
\n          [modelKey]: false/*dm*/
\n        });/*dm*/
\n        let transform = await $.global.functions.modelValueFormat(modelName)/*dm*/
\n        if ($.shareData.api_getList.code !== 200) {/*dm*/
\n          this.$message.error($.shareData.beiguanlianmoxing71321saveGetList.msg)/*dm*/
\n        } else {/*dm*/
\n          this.$set($.shareData.var_baseTableList, modelKey, $.shareData.api_getList.data.records.map(item => {/*dm*/
\n            let data = transform.input(item);/*dm*/
\n            return data/*dm*/
\n          }))/*dm*/
\n        }/*dm*/
\n        this.$set($.shareData.var_basePageParams[modelKey], 'totalSize', $.shareData.api_getList.data.totalSize)/*dm*/
\n        this.$refs.jtable?.doLayout();/*dm*/
\n      }).catch(() => {/*dm*/
\n        $.shareData.var_baseTableLoadingAction({/*dm*/
\n          [modelKey]: false/*dm*/
\n        });/*dm*/
\n      })/*dm*/
\n    }}",
				"description":"加载表格数据",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_loadTableData"
			},
			{
				"default":"${function fun_loadTableDefaultPlanData(modelName, modelKey, tableRef){/*dm*/
\n          this.$set($.shareData.var_baseTableList, modelKey, []);/*dm*/
\n          await $.shareData.api_tablePlanListAction({/*dm*/
\n            modelName, planId: 0/*dm*/
\n          })/*dm*/
\n          const planId = $.shareData.api_tablePlanList[0]?.id/*dm*/
\n          await $.shareData.api_tablePlanInfoAction({/*dm*/
\n            modelName, planId, system: true/*dm*/
\n          }).then(() => {/*dm*/
\n            const planInfoDtoList = $.shareData.api_tablePlanInfo.planInfoDtoList?.map(item => {/*dm*/
\n              return {/*dm*/
\n                ...item,/*dm*/
\n                modelField: $.global.shareData.modelsInfoData.fieldsMap[modelName].get(item.name)/*dm*/
\n              }/*dm*/
\n            });/*dm*/
\n            $.shareData.var_baseTableColumnShow[modelKey] = planInfoDtoList.filter(item => item.show).sort((a, b) => {/*dm*/
\n              return a.index - b.index/*dm*/
\n            });/*dm*/
\n            if(tableRef) {/*dm*/
\n              this.$nextTick(() => {/*dm*/
\n                this.$refs[tableRef]?.doLayout();/*dm*/
\n              });/*dm*/
\n            }/*dm*/
\n          })/*dm*/
\n      }}",
				"description":"加载表格视图",
				"type":"function",
				"title":"",
				"key":"fun_loadTableDefaultPlanData"
			},
			{
				"default":"${function fun_loadCache(modelKey, cacheKey) {/*dm*/
\n      let pageParams = localStorage.getItem(`var_basePageParams_${cacheKey}`)/*dm*/
\n      if(pageParams) {/*dm*/
\n        pageParams = JSON.parse(pageParams);/*dm*/
\n        Object.assign($.shareData.var_basePageParams[modelKey], pageParams)/*dm*/
\n      }/*dm*/
\n    }}",
				"description":"初始化缓存数据",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_loadCache"
			},
			{
				"default":"${function fun_genTableId(modelName){/*dm*/
\n  const userData = $.bom.getHostData('userInfo')/*dm*/
\n  const genId = `${$.shareData.api_tablePlanInfo?.id}_${userData.personId || userData.userName || userData.pin}_${modelName}`/*dm*/
\n  return genId/*dm*/
\n}}",
				"description":"获取表格唯一id",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_genTableId"
			},
			{
				"default":"${function fun_getTableConfig(planCode){/*dm*/
\n  return $.shareData.var_setting?.table?.[planCode] || $.shareData.var_setting?.table?.default || {}/*dm*/
\n}}",
				"description":"",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_getTableConfig"
			},
			{
				"default":"${function fun_getGroupConfig(groupCode){/*dm*/
\n/*dm*/
\n  return $.shareData.var_setting?.group?.[groupCode] || $.shareData.var_setting?.group?.default || {}/*dm*/
\n}}",
				"description":"",
				"type":"function",
				"title":"",
				"key":"fun_getGroupConfig"
			},
			{
				"default":"${function fun_getTableNavUrl(type=\"default\", modelName=\"\", urlType){/*dm*/
\n/*dm*/
\n    // 子模型/*dm*/
\n    if(type == 'ms') {/*dm*/
\n      return `/ms${$.shareData.var_setting.pagePath}/${modelName}/${urlType}`;/*dm*/
\n    }/*dm*/
\n/*dm*/
\n    // 关联关系/*dm*/
\n    if(type == 'ass') {/*dm*/
\n      return `/assoc${$.shareData.var_setting.pagePath}/${modelName}/${urlType}`;/*dm*/
\n    }/*dm*/
\n/*dm*/
\n    // 普通的/*dm*/
\n    return `/${modelName}/${urlType}`/*dm*/
\n}}",
				"description":"",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_getTableNavUrl"
			},
			{
				"default":"${function fun_isInclude(){/*dm*/
\n  if (this.$el.parentNode.getAttribute(\"c_v_l\") === \"Include\") {/*dm*/
\n    return true;/*dm*/
\n  }/*dm*/
\n  return false;/*dm*/
\n}}",
				"description":"",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_isInclude"
			}
		],
		"shareData":[
			{
				"default":"true",
				"description":"是否展示页头",
				"key":"var_showHeader"
			},
			{
				"default":"true",
				"description":"是否展示基本信息",
				"key":"var_showBaseInfoTab"
			},
			{
				"default":"false",
				"description":"字段跟踪Tab开关",
				"key":"var_showFieldTrack"
			},
			{
				"default":"false",
				"description":"审批流程Tab开关",
				"key":"showModelFlow"
			},
			{
				"default":"{}",
				"description":"审批流程数据",
				"key":"flowInfo"
			},
			{
				"default":"{\r/*dm*/
\n  0: '待办',\r/*dm*/
\n  1: '同意',\r/*dm*/
\n  2: '拒绝',\r/*dm*/
\n  3: '提交',\r/*dm*/
\n  6: '驳回',\r/*dm*/
\n  7: '后加签',\r/*dm*/
\n  8: '前加签',\r/*dm*/
\n  9: '转派',\r/*dm*/
\n  10: '驳回直送',\r/*dm*/
\n  11: '回退'\r/*dm*/
\n}",
				"description":"审批结果Map",
				"key":"optStatusMap"
			},
			{
				"default":"[]",
				"description":"选中关联模型信息",
				"key":"var_fieldTrackList"
			},
			{
				"default":"",
				"description":"选中关联模型信息",
				"key":"var_activeTabModelName"
			},
			{
				"default":"[]",
				"description":"关联模型列表数据",
				"key":"var_associatedList"
			},
			{
				"default":"[]",
				"description":"关联模型Tabs",
				"key":"var_relateTabs"
			},
			{
				"default":"[]",
				"description":"关键信息分区",
				"key":"var_keyGroups"
			},
			{
				"default":"[]",
				"description":"基础信息分区",
				"key":"var_baseGroups"
			},
			{
				"default":"first",
				"description":"tab切换状态",
				"key":"var_tabActiveName"
			},
			{
				"default":"{}",
				"description":"表单数据",
				"key":"var_formInfo"
			},
			{
				"default":"[]",
				"description":"测试模型模型下其他数据",
				"key":"var_pageOptions"
			},
			{
				"default":false,
				"description":"测试模型数据加载",
				"key":"var_loading"
			},
			{
				"default":"{\"name\":\"792010485771395074\",\"ceshimoxing\":\"792010485771395074\"}",
				"description":"查找关系字段对应详情页面ID",
				"key":"var_findRelatedPages"
			},
			{
				"default":"{}",
				"description":"页面权限",
				"key":"var_pageRole"
			},
			{
				"default":"{}",
				"description":"审批流程数据",
				"key":"flowInfo"
			},
			{
				"default":"{\"__subModel.modelName\":{\"querySorts\":[{\"fieldName\":\"update_time\",\"desc\":true}]},\"associatedList\":{\"querySorts\":[{\"fieldName\":\"update_time\",\"desc\":true}]}}",
				"description":"查询条件表格",
				"key":"var_baseSearchQuery"
			},
			{
				"default":"{}",
				"description":"查询条件表格",
				"global":false,
				"key":"var_baseTableList"
			},
			{
				"default":"{\"__subModel.modelName\":false,\"associatedList\":false}",
				"description":"表格加载loading",
				"key":"var_baseTableLoading"
			},
			{
				"default":"{}",
				"description":"相关模型信息",
				"key":"var_baseFindRelatedInfo"
			},
			{
				"default":"{}",
				"description":"相关模型信息",
				"key":"var_baseFindRelatedPages"
			},
			{
				"default":"{\"__subModel.modelName\":{\"columnPlanId\":null,\"showPagination\":true,\"paginationMode\":\"complete\",\"paginationAlign\":\"right\",\"pageSizes\":[10,20,30,40,50],\"showHeader\":true,\"stripe\":false,\"showNo\":true,\"selectRowType\":\"single\",\"border\":false,\"mode\":\"normal\",\"batchDelFlag\":true,\"addFlag\":true,\"exportDataFlag\":false,\"addPage\":\"\",\"editPage\":\"\",\"detailPage\":\"\",\"headerEllipsis\":true,\"cellEllipsis\":true,\"textAlign\":\"left\",\"showDetail\":true,\"showDel\":true,\"showEdit\":true,\"columnShowIcon\":false,\"columnShowText\":true,\"resizeColumn\":true},\"associatedList\":{\"columnPlanId\":null,\"showPagination\":true,\"paginationMode\":\"complete\",\"paginationAlign\":\"right\",\"pageSizes\":[10,20,30,40,50],\"showHeader\":true,\"stripe\":false,\"showNo\":true,\"selectRowType\":\"single\",\"border\":false,\"mode\":\"normal\",\"batchDelFlag\":true,\"addFlag\":true,\"exportDataFlag\":false,\"addPage\":\"\",\"editPage\":\"\",\"detailPage\":\"\",\"headerEllipsis\":true,\"cellEllipsis\":true,\"textAlign\":\"left\",\"showDetail\":true,\"showDel\":true,\"showEdit\":true,\"columnShowIcon\":false,\"columnShowText\":true,\"resizeColumn\":true}}",
				"description":"表格配置相关",
				"key":"var_basePageSetting"
			},
			{
				"default":"{\"__subModel.modelName\":[],\"associatedList\":[]}",
				"description":"表格显示列",
				"key":"var_baseTableColumnShow"
			},
			{
				"default":"{\"__subModel.modelName\":{\"pageNum\":1,\"pageSize\":10,\"totalPage\":1,\"totalSize\":1},\"associatedList\":{\"pageNum\":1,\"pageSize\":10,\"totalPage\":1,\"totalSize\":1}}",
				"description":"表格分页配置",
				"key":"var_basePageParams"
			},
			{
				"default":"{\"__subModel.modelName\":[],\"associatedList\":[]}",
				"description":"表格多选",
				"key":"var_baseMultipleSelection"
			},
			{
				"default":"\"{\\r/*dm*/
\n  \\\"dateTime\\\": 180,\\r/*dm*/
\n  \\\"date\\\": 120,\\r/*dm*/
\n  \\\"user\\\": 110,\\r/*dm*/
\n  \\\"multipleUser\\\": 300,\\r/*dm*/
\n  \\\"text\\\": 120,\\r/*dm*/
\n  \\\"number\\\": 120,\\r/*dm*/
\n  \\\"phone\\\": 120,\\r/*dm*/
\n  \\\"findRelated\\\": 120,\\r/*dm*/
\n  \\\"findRelatedMulti\\\": 120,\\r/*dm*/
\n  \\\"email\\\": 120,\\r/*dm*/
\n  \\\"checkBox\\\": 120,\\r/*dm*/
\n  \\\"select\\\": 120,\\r/*dm*/
\n  \\\"multipleSelect\\\": 120,\\r/*dm*/
\n  \\\"url\\\": 120,\\r/*dm*/
\n  \\\"autoNumber\\\": 120,\\r/*dm*/
\n  \\\"formula\\\": 120,\\r/*dm*/
\n  \\\"summary\\\": 120,\\r/*dm*/
\n  \\\"string\\\": 120\\r/*dm*/
\n}\"",
				"global":false,
				"key":"var_tableBusinessTypeWidth"
			},
			{
				"default":"{\"modelName\":\"ipms_user\",\"pageKey\":\"840365914720722946\",\"enableTitle\":true,\"enableTabs\":true,\"enableButtons\":true,\"columns\":3,\"title\":\"\",\"table\":{\"default\":{\"showPlanView\":true,\"edit\":true,\"detail\":false,\"delete\":true,\"copy\":false}},\"group\":{\"default\":{\"enableTitle\":true,\"isUnfold\":true,\"column\":3}},\"column\":3,\"detailPath\":\"/ipms_user/detail\",\"formPath\":\"/ipms_user/form\",\"listPath\":\"/ipms_user/list\",\"pagePath\":\"/ipms_user/detail\"}",
				"key":"var_setting"
			},
			{
				"default":"{/*dm*/
\n  \"keyInfo\": [],/*dm*/
\n  \"baseInfo\": [],/*dm*/
\n  \"tabsInfo\": []/*dm*/
\n}",
				"description":"视图数据",
				"global":false,
				"key":"var_viewData"
			},
			{
				"default":"\"\"",
				"global":false,
				"key":"var_detailId"
			}
		],
		"action":{
			"lifecycle":{
				"mounted":{
					"arguments":[],
					"actions":[
						{
							"handler":"${function handler(){\r/*dm*/
\nawait $.shareData.api_getPageRoleAction({ pageKey: $.shareData.var_setting.pageKey })\r/*dm*/
\n$.shareData.var_pageRoleAction($.shareData.api_getPageRole)}}",
							"type":"function"
						},
						{
							"handler":"${function Fun15041(){// 代表静态id就不执行了\r/*dm*/
\nif ($.functions.fun_isInclude()) {\r/*dm*/
\n  return;\r/*dm*/
\n}\r/*dm*/
\nconst modelInfo = await $.global.functions.getModelDictionaryInfo($.shareData?.var_setting?.modelName);\r/*dm*/
\n$.functions.fun_initView(modelInfo);\r/*dm*/
\n\r/*dm*/
\n}}",
							"type":"function"
						}
					]
				}
			},
			"on":{
				"changeSetting":{
					"arguments":[
						"setting"
					],
					"actions":[
						{
							"handler":"${function Handler(setting){$.shareData.var_settingAction(setting)}}",
							"type":"function"
						}
					]
				},
				"changeItem":{
					"arguments":[
						"data"
					],
					"actions":[
						{
							"handler":"${function Handler(){if (!$.functions.fun_isInclude()) {\r/*dm*/
\n  return;\r/*dm*/
\n}\r/*dm*/
\n$.shareData.var_detailId = data.id;\r/*dm*/
\n(async () => {\r/*dm*/
\n  const modelInfo = await $.global.functions.getModelDictionaryInfo($.shareData?.var_setting?.modelName);\r/*dm*/
\n  $.functions.fun_initView(modelInfo);\r/*dm*/
\n})()\r/*dm*/
\n}}",
							"type":"function"
						}
					]
				}
			}
		},
		"id":"840365914720722946",
		"styleCode":".sub-model-root {\r/*dm*/
\n    .el-table__empty-text > div {\r/*dm*/
\n      min-height: unset !important;\r/*dm*/
\n      /* padding: 16px 0 0 !important; */\r/*dm*/
\n    }\r/*dm*/
\n  }\r/*dm*/
\n  .sub-model-dialog {\r/*dm*/
\n    .el-dialog__body {\r/*dm*/
\n      padding: 0 !important;\r/*dm*/
\n    }\r/*dm*/
\n  }\r/*dm*/
\n  .detailPageRoot .attachment-root .el-form-item .el-form-item__content {\r/*dm*/
\n    flex: 1;\r/*dm*/
\n  }\r/*dm*/
\n  .detailPageRoot .el-form:hover a{\r/*dm*/
\n    color: #4871e4;\r/*dm*/
\n  }\r/*dm*/
\n  .detailPageRoot .el-collapse:hover a{\r/*dm*/
\n    color: #4871e4;\r/*dm*/
\n  }\r/*dm*/
\n  .detailPageRoot .el-row {\r/*dm*/
\n    padding-left: 0 !important;\r/*dm*/
\n    padding-right: 0 !important;\r/*dm*/
\n  }\r/*dm*/
\n  .detailPageRoot .el-col-24 {\r/*dm*/
\n    margin-left: 0 !important;\r/*dm*/
\n    margin-right: 0 !important;\r/*dm*/
\n  }\r/*dm*/
\n  .detailPageRoot {\r/*dm*/
\n    padding:16px 24px 0px;\r/*dm*/
\n    position:relative;\r/*dm*/
\n    overflow:auto;\r/*dm*/
\n    height: 100%;\r/*dm*/
\n    box-sizing: border-box;\r/*dm*/
\n    display: flex;\r/*dm*/
\n    flex-direction: column;\r/*dm*/
\n  }\r/*dm*/
\n  .detailPageRoot .detail-tabs {\r/*dm*/
\n    flex: 1;\r/*dm*/
\n    display: flex;\r/*dm*/
\n    flex-direction: column;\r/*dm*/
\n  }\r/*dm*/
\n  .detailPageRoot .sub-model-table .records-joy-table-body{\r/*dm*/
\n    height: 100%;\r/*dm*/
\n    max-height: 400px;\r/*dm*/
\n  }\r/*dm*/
\n  .detailPageRoot .detail-tabs .link-find-table .records-joy-table-body {\r/*dm*/
\n    height: calc(100% - 94px);\r/*dm*/
\n  }\r/*dm*/
\n  .detailPageRoot .detail-tabs .el-tabs__content {\r/*dm*/
\n    flex: 1;\r/*dm*/
\n    height: 1px;\r/*dm*/
\n    min-height: 300px;\r/*dm*/
\n  }\r/*dm*/
\n  .detailPageRoot .link-find-table {\r/*dm*/
\n    width: 100%;\r/*dm*/
\n    position: absolute;\r/*dm*/
\n  }\r/*dm*/
\n  .detailPageRoot .el-collapse-item__content{padding-bottom:0px;}\r/*dm*/
\n  .el-collapse-item__header {\r/*dm*/
\n    width: max-content;\r/*dm*/
\n  }",
		"apiConfigs":[
			{
				"default":"{\"code\":200,\"msg\":\"\",\"data\":{}}",
				"transform":"",
				"isModel":true,
				"method":"GET",
				"isShareData":true,
				"name":"api_getPageInfo",
				"description":"获取详情",
				"type":{
					"label":"detail",
					"value":"detail",
					"key":"detail"
				},
				"url":"/api/v1/model/:modelName/:id"
			},
			{
				"default":"{\"code\":200,\"msg\":\"\",\"data\":{\"records\":[]}}",
				"transform":"function handler(data) {/*dm*/
\n  return data;/*dm*/
\n}",
				"method":"GET",
				"isShareData":true,
				"name":"api_getFieldTrackList",
				"description":"字段追踪记录列表",
				"url":"/api/v1/tracking/:modelName/:id/list"
			},
			{
				"default":"{code:200,msg:'',data:{totalSize:0,records: []}}",
				"transform":"function filter_aeZFJf(data) {/*dm*/
\n        return JSON.parse(JSON.stringify(data));/*dm*/
\n      }",
				"method":"POST",
				"isShareData":true,
				"name":"api_getList",
				"description":"获取表格数据",
				"url":"/api/v1/model/:modelName/list"
			},
			{
				"transform":"function filter_jstRNX(data) {\r/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果\r/*dm*/
\n  return data;\r/*dm*/
\n}",
				"method":"POST",
				"isShareData":true,
				"name":"api_preDownload",
				"description":"预下载接口",
				"url":"/api/v1/attachment/preDownload"
			},
			{
				"transform":"",
				"method":"GET",
				"isShareData":true,
				"name":"api_getViewDetailList",
				"description":"页签-详情页表单设置-列表",
				"global":false,
				"url":"/api/v1/modelview/single/:modelName"
			},
			{
				"transform":"function filter_Y6biFKTj(data) {\r/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果\r/*dm*/
\n  return data;\r/*dm*/
\n}",
				"method":"GET",
				"isShareData":true,
				"name":"get_defaultList",
				"description":"获取模型的一条缺省数据",
				"global":false,
				"url":"/api/v1/dev/model/:modelName/default"
			},
			{
				"transform":"function filter_8YYSanTF(data) {/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果/*dm*/
\n return data.data.reduce((pre, cur) => {/*dm*/
\n    return {/*dm*/
\n  ...pre,    [cur.elementCode]: cur.permission === 1/*dm*/
\n    }/*dm*/
\n  }, {});/*dm*/
\n}",
				"method":"GET",
				"isShareData":true,
				"name":"api_getPageRole",
				"description":"获取页面元素权限",
				"global":false,
				"url":"/api/v1/permission/page/:pageKey/element"
			},
			{
				"transform":"function filter_aeZFJf(data) {return data.data;}",
				"method":"DELETE",
				"isShareData":true,
				"name":"api_deleteDataById",
				"description":"删除行数据",
				"url":"/api/v1/model/:modelName/:id"
			},
			{
				"transform":"function filter_aeZFJf(data) {return data.data;}",
				"method":"DELETE",
				"isShareData":true,
				"name":"api_deleteInBatch",
				"description":"批量删除数据",
				"url":"/api/v1/model/:modelName"
			},
			{
				"default":"{\r/*dm*/
\n  \"tableWidth\": \"small\",\r/*dm*/
\n  \"freeze\": {\r/*dm*/
\n    \"head\": true,\r/*dm*/
\n    \"action\": true\r/*dm*/
\n  }\r/*dm*/
\n}",
				"transform":"function filter_YEcMz73F(data) {/*dm*/
\n  /*dm*/
\n  let planInfo = data/*dm*/
\n  if (data.code == 200) {/*dm*/
\n    planInfo = data.data;/*dm*/
\n  } else {/*dm*/
\n    planInfo = data/*dm*/
\n  }/*dm*/
\n  return {/*dm*/
\n    ...planInfo,/*dm*/
\n    \"tableWidth\": planInfo.tableWidth || 'small',/*dm*/
\n    \"freeze\": planInfo.freeze || {/*dm*/
\n      \"head\": true,/*dm*/
\n      \"action\": true/*dm*/
\n    },/*dm*/
\n    planInfoDtoList: planInfo?.planInfoDtoList?.map(item => {/*dm*/
\n       return {/*dm*/
\n         ...item,/*dm*/
\n         label: item?.modelFieldEntity?.fieldText || item.label,/*dm*/
\n       }/*dm*/
\n    })/*dm*/
\n  }/*dm*/
\n}",
				"method":"GET",
				"isShareData":true,
				"name":"api_tablePlanInfo",
				"description":"列表视图-查询显示视图明细",
				"url":"/api/v1/modelview/planInfo/:modelName/:planId/:system"
			},
			{
				"transform":"function filter_YEcMz73F(data) {/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果/*dm*/
\n /*dm*/
\n  return data.data;/*dm*/
\n}",
				"method":"GET",
				"isShareData":true,
				"name":"api_tablePlanList",
				"description":"列表视图-查询用户自定义视图列表",
				"url":"/api/v1/modelview/plan/:modelName/:planId"
			},
			{
				"transform":"function filter_YEcMz73F(data) {/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果/*dm*/
\n  return data;/*dm*/
\n}",
				"method":"DELETE",
				"isShareData":true,
				"name":"api_tablePlanDelete",
				"description":"列表视图-删除视图",
				"url":"/api/v1/modelview/planInfoDel/:modelName/:planId"
			},
			{
				"transform":"function filter_YEcMz73F(data) {/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果/*dm*/
\n  return data;/*dm*/
\n}",
				"method":"POST",
				"isShareData":true,
				"name":"api_tablePlanAdd",
				"description":"列表视图-新增",
				"url":"/api/v1/modelview/planInfoAdd"
			},
			{
				"transform":"function filter_YEcMz73F(data) {/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果/*dm*/
\n  return data;/*dm*/
\n}",
				"method":"PUT",
				"isShareData":true,
				"name":"api_tablePlanEdit",
				"description":"列表视图-保存",
				"url":"/api/v1/modelview/planInfoEdit"
			},
			{
				"transform":"function filter_YEcMz73F(data) {/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果/*dm*/
\n  return data;/*dm*/
\n}",
				"method":"PUT",
				"isShareData":true,
				"name":"api_tablePlanRename",
				"description":"列表视图-重命名",
				"url":"/api/v1/modelview/reName"
			},
			{
				"transform":"function filter_yacFP332(data) {/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果/*dm*/
\n  return data;/*dm*/
\n}",
				"method":"GET",
				"isShareData":true,
				"name":"api_getDetailView",
				"description":"获取模型视图",
				"url":"/api/v2/views/:modelName/:pageKey/form_view"
			},
			{
				"transform":"",
				"method":"GET",
				"isShareData":true,
				"name":"getBusOrgList",
				"global":false,
				"url":"/api/v2/permission/user/busiOrg/:loginName"
			},
			{
				"transform":"",
				"method":"GET",
				"isShareData":true,
				"name":"getPermissionUserInfo",
				"description":"获取权限领域的用户模型下的用户详情信息",
				"url":"/api/v2/permission/user/:id"
			}
		],
		"key":1724323668302,
		"attrs":{
			"style":{
				"height":"100%"
			},
			"setting":"{\"name\": \"page\"}"
		}
	},
	"editorSettingDsl":"{\"enableTitle\":true,\"enableButtons\":true,\"column\":3,\"title\":\"\",\"group\":{\"default\":{\"enableTitle\":true,\"isUnfold\":true}},\"table\":{\"default\":{\"showPlanView\":true,\"edit\":true,\"detail\":false,\"delete\":true,\"copy\":false}}}",
	"pageInterceptor":"",
	"pageSource":"model",
	"frame":"vue"
}