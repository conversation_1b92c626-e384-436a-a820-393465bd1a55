{
	"usePlatform":"",
	"enableAdvanced":true,
	"componentCode":"ipms@1.0.0",
	"editable":false,
	"pageTemplate":"PC树模版",
	"edition":"v2",
	"pageKey":"840367756792066049",
	"pageSettingDsl":"{\"detailPath\":\"/ipms_business_organization/detail1\",\"formPath\":\"/ipms_business_organization/form1\",\"listPath\":\"/ipms_business_organization/list1\",\"modelName\":\"ipms_business_organization\",\"pageKey\":\"840367756792066049\",\"pagePath\":\"/ipms_business_organization/form1\"}",
	"pageName":"业务组织_树模板_新建/编辑",
	"pageTemplateCode":"PCTree",
	"modelName":"ipms_business_organization",
	"pageType":"form_v5",
	"pageDsl":{
		"components":[
			{
				"children":[
					{
						"componentType":"custom",
						"children":[
							{
								"libraryName":"@jd/joyui",
								"componentType":"UILibrary",
								"children":[
									{
										"componentType":"custom",
										"children":[
											{
												"componentType":"custom",
												"children":[
													{
														"componentType":"custom",
														"children":[
															{
																"componentType":"custom",
																"componentName":"TextWrapper",
																"id":"TextWrapper-H7JsZc5Q",
																"attrs":{
																	"style":{
																		"color":"#909399",
																		"font-weight":"normal",
																		"display":"block",
																		"width":"100%",
																		"font-size":"14px",
																		"line-height":"66px"
																	}
																},
																"props":{
																	"children":" 暂无数据"
																},
																"chosen":false
															}
														],
														"componentName":"DivWrapper",
														"id":"DivWrapper-C22zAAZi",
														"title":"暂无数据",
														"attrs":{
															"style":{
																"background-color":"#ffffff",
																"width":"100%",
																"height":"100%"
															}
														},
														"props":{
															"name":"DivWrapper",
															"slot":"empty"
														},
														"chosen":false
													},
													{
														"componentType":"custom",
														"children":[
															{
																"componentType":"custom",
																"componentName":"TableColumn",
																"id":"TableColumn-8yNm7Sin",
																"props":{
																	"show-overflow-tooltip":"",
																	"reserve-selection":true,
																	":selectable":"${function s(scope){ return scope['$extra']?.dataPermission ==2 }}",
																	"width":"48",
																	"fixed":"left",
																	"type":"selection",
																	"align":"center",
																	"v-slot":"scope"
																}
															}
														],
														"componentName":"VIf",
														"id":"VIf-wsN5FQeS",
														"props":{
															"conditions":"${[ $.shareData.var_tables[viewCode]?.setting?.selectRowType === 'many' || $.shareData.var_tables[viewCode]?.setting?.batchDelFlag ]}"
														}
													},
													{
														"componentType":"custom",
														"children":[
															{
																"componentType":"custom",
																"componentName":"TableColumn",
																"id":"TableColumn81122",
																"props":{
																	"resizable":false,
																	"width":"60",
																	"fixed":"left",
																	"label":"序号",
																	"type":"index",
																	"align":"left",
																	"class-name":"color-text-secondary"
																}
															}
														],
														"componentName":"VIf",
														"id":"VIf-X2BpaeSz",
														"props":{
															"conditions":"${[$.shareData.var_tables[viewCode]?.setting?.showNo]}"
														}
													},
													{
														"children":[
															{
																"children":[
																	{
																		"children":[
																			{
																				"componentType":"custom",
																				"id":"DivWrapper-XSKpMzsa",
																				"componentName":"DivWrapper",
																				"title":"Div",
																				"attrs":{
																					"style":{}
																				},
																				"props":{
																					"name":"DivWrapper"
																				}
																			}
																		],
																		"componentName":"Radio",
																		"id":"Radio-PMw3tCcy",
																		"props":{
																			"label":"${scope.row.id}",
																			"v-model":"${$.shareData.var_tables[viewCode]?.singleSelection}"
																		}
																	}
																],
																"componentName":"TableColumn",
																"id":"TableColumn-JDCCe7Rw",
																"props":{
																	"width":"${'80px'}",
																	"v-slot":"scope"
																}
															}
														],
														"componentName":"VIf",
														"id":"VIf-f4Fzsxcd",
														"props":{
															"conditions":"${[$.shareData.var_tables[viewCode]?.setting?.showRadio]}"
														}
													},
													{
														"componentType":"custom",
														"children":[
															{
																"componentType":"custom",
																"children":[
																	{
																		"libraryName":"@jd/joyui",
																		"componentType":"UILibrary",
																		"action":{
																			"fire":{
																				"navtoDetail":{
																					"arguments":[
																						"field",
																						"modelName"
																					],
																					"actions":[
																						{
																							"handler":"${function Fun27696(field, modelName){if (true) {/*dm*/
\n  // console.error('子模型新建或编辑时， 不支持跳转啊');/*dm*/
\n  return;/*dm*/
\n}}}",
																							"type":"function"
																						}
																					]
																				},
																				"handleDownload":{
																					"arguments":[
																						"file"
																					],
																					"actions":[
																						{
																							"handler":"${function handler(file){/*dm*/
\nawait $.shareData.preDownloadAction({ originFileName: file.originFileName })/*dm*/
\n/*dm*/
\nfetch($.shareData.preDownload.presignedObjectUrl, {/*dm*/
\n  method: 'GET',/*dm*/
\n  responseType: 'blob',/*dm*/
\n}).then(/*dm*/
\n  res => {/*dm*/
\n    return res.blob();/*dm*/
\n  })/*dm*/
\n  .then(blob => {/*dm*/
\n/*dm*/
\n    let bl = new Blob([blob]);/*dm*/
\n/*dm*/
\n    var link = document.createElement('a');/*dm*/
\n/*dm*/
\n    link.href = window.URL.createObjectURL(bl);/*dm*/
\n/*dm*/
\n    link.download = file.name;/*dm*/
\n/*dm*/
\n    link.click();/*dm*/
\n/*dm*/
\n    window.URL.revokeObjectURL(link.href);/*dm*/
\n/*dm*/
\n  }).catch(err => {/*dm*/
\n    console.log('error', error)/*dm*/
\n  });/*dm*/
\n}}",
																							"type":"function"
																						}
																					]
																				}
																			}
																		},
																		"componentName":"ModelField",
																		"id":"ModelField-FcPhjxAd",
																		"title":"模型字段",
																		"version":"1.8.0",
																		"props":{
																			"originProps":{},
																			"formType":"table",
																			"fieldProps":"${{/*dm*/
\n  ...(item.modelField || {}),/*dm*/
\n    jumpOrNot: false/*dm*/
\n}}",
																			"businessType":"${item.modelField.businessType}",
																			"value":"${scope.row[item.name]}"
																		}
																	}
																],
																"componentName":"TableColumn",
																"id":"TableColumn-wjiYERQn",
																"props":{
																	"show-overflow-tooltip":"${$.shareData.var_tables[viewCode]?.setting?.cellEllipsis}",
																	"prop":"${item.name}",
																	"fixed":false,
																	"label":"${$.shareData.var_setting.table?.[viewCode]?.fieldInfo?.[item.name]?.fieldName || item.label}",
																	"sortable":false,
																	"align":"${$.shareData.var_tables[viewCode]?.setting?.textAlign}",
																	"v-slot":"scope",
																	"min-width":"${() => {\r/*dm*/
\n  if (item?.modelField.businessType === 'dateTime') {\r/*dm*/
\n    return item?.displayWidth || 180\r/*dm*/
\n  }\r/*dm*/
\n  return item?.displayWidth || 140\r/*dm*/
\n}}"
																},
																"attrs":{
																	"setting":"${JSON.stringify({/*dm*/
\n  name: 'table-field',/*dm*/
\n  editorViewCode: viewCode,/*dm*/
\n  fieldInfo: {/*dm*/
\n    fieldText: item.label,/*dm*/
\n    fieldName: item.name,/*dm*/
\n    businessType: item.modelField.businessType/*dm*/
\n  }/*dm*/
\n})}"
																}
															}
														],
														"componentName":"VFor",
														"id":"VFor-nst6xHhw",
														"title":"table列循环容器",
														"props":{
															"forEach":"${$.shareData.var_tables[viewCode]?.columns}",
															"forEachKey":"item",
															"index":"$index"
														}
													},
													{
														"componentType":"custom",
														"children":[
															{
																"componentType":"custom",
																"children":[
																	{
																		"libraryName":"@jd/joyui",
																		"componentType":"UILibrary",
																		"action":{
																			"fire":{
																				"tab-click":{
																					"arguments":[
																						"tab"
																					],
																					"actions":[
																						{
																							"handler":"${function handler(tab){const detailId = scope.row?.id;/*dm*/
\nconst addPage = $.functions.fun_getOperatePath(modelName, 'form');/*dm*/
\nconst detailPage = $.functions.fun_getOperatePath(modelName, 'detail')/*dm*/
\nconst tableKey = viewCode;/*dm*/
\nconst subModel = {/*dm*/
\n  modelName,/*dm*/
\n  modelText,/*dm*/
\n  fieldName,/*dm*/
\n};/*dm*/
\nif (tab == 'detail' && detailId) {/*dm*/
\n  $.bom.page?.open(detailPage, {/*dm*/
\n    detailId,/*dm*/
\n    pageType: 'subModel',/*dm*/
\n    subModel/*dm*/
\n  }, async (data) => {/*dm*/
\n  }, {/*dm*/
\n    options: {/*dm*/
\n      width: '960px',/*dm*/
\n      title: '详情',/*dm*/
\n      closeOnClickModal: false,/*dm*/
\n      customClass: 'sub-model-dialog',/*dm*/
\n    },/*dm*/
\n    type: 'dialog'/*dm*/
\n  });/*dm*/
\n}/*dm*/
\nif (tab == 'edit') {/*dm*/
\n  if (detailId) {/*dm*/
\n    $.bom.page?.open(addPage, {/*dm*/
\n      detailId,/*dm*/
\n      pageType: 'subModel',/*dm*/
\n      subModel,/*dm*/
\n      formData: scope.row/*dm*/
\n    }, async (data) => {/*dm*/
\n      if (!data) return;/*dm*/
\n/*dm*/
\n      console.log('编辑子模型回调', data, scope.$index);/*dm*/
\n      // 编辑/*dm*/
\n      const oldRow = scope.row;/*dm*/
\n      const rowIndex = scope.$index;/*dm*/
\n      let transform = await $.global.functions.modelValueFormat(modelName)/*dm*/
\n/*dm*/
\n      /*dm*/
\n      // 显示列表/*dm*/
\n      this.$set($.shareData.var_tables[viewCode]?.tableList, rowIndex, {/*dm*/
\n        ...oldRow,/*dm*/
\n        ...data/*dm*/
\n      });/*dm*/
\n      if (data.id.indexOf('subModel') > -1) {/*dm*/
\n        const [name, addIndex] = data.id.split('_');/*dm*/
\n        const newRow = {/*dm*/
\n          ...oldRow,/*dm*/
\n          ...data/*dm*/
\n        }/*dm*/
\n        delete newRow.id;/*dm*/
\n        $.shareData.var_tables[viewCode].tableData.addDatas[addIndex] = transform.output(newRow);/*dm*/
\n      } else {/*dm*/
\n        // 单独记录修改行/*dm*/
\n        const updateRowKey = $.shareData.var_tables[viewCode]?.tableData?.updateDatas.findIndex(i => i.id == data.id);/*dm*/
\n        if (updateRowKey == -1) {/*dm*/
\n          $.shareData.var_tables[viewCode]?.tableData?.updateDatas.push(transform.output(data));/*dm*/
\n        } else {/*dm*/
\n          $.shareData.var_tables[viewCode].tableData.updateDatas[updateRowKey] = transform.output(data);/*dm*/
\n        }/*dm*/
\n      }/*dm*/
\n    }, {/*dm*/
\n      options: {/*dm*/
\n        width: '960px',/*dm*/
\n        title: '编辑',/*dm*/
\n        closeOnClickModal: false,/*dm*/
\n        customClass: 'sub-model-dialog',/*dm*/
\n      },/*dm*/
\n      type: 'dialog'/*dm*/
\n    });/*dm*/
\n  };/*dm*/
\n}/*dm*/
\nif (tab == 'delete') {/*dm*/
\n  this.$confirm('此操作将永久删除, 是否继续?', '提示', {/*dm*/
\n    confirmButtonText: '确定',/*dm*/
\n    cancelButtonText: '取消',/*dm*/
\n    type: 'warning'/*dm*/
\n  }).then(() => {/*dm*/
\n    // 删除本地缓存数据/*dm*/
\n    const rowKey = scope.$index;/*dm*/
\n/*dm*/
\n    $.shareData.var_tables[viewCode]?.tableList.splice(rowKey, 1);/*dm*/
\n    if (scope.row?.id.indexOf('subModel') > -1) {/*dm*/
\n      const [, addRowKey] = scope.row?.id.split('_');/*dm*/
\n      $.shareData.var_tables[viewCode]?.tableData?.addDatas.splice(addRowKey, 1);/*dm*/
\n    }/*dm*/
\n    // 添加子模型表单数据/*dm*/
\n    $.shareData.var_tables[viewCode]?.tableData?.deleteIds.push(detailId)/*dm*/
\n  });/*dm*/
\n}/*dm*/
\n}}",
																							"type":"function"
																						}
																					]
																				}
																			}
																		},
																		"componentName":"MoreButton",
																		"id":"MoreButton-C8PMfRFj",
																		"version":"1.8.0",
																		"props":{
																			"styleState":"${[]}",
																			"padding":"0px 16px 0px 0px",
																			"data":"${() => {/*dm*/
\n  let list = [];/*dm*/
\n  const setting = $.shareData.var_setting.table;/*dm*/
\n  let isEdit = setting['default'].edit; // false ? $.shareData.permissionData?.edit : true;/*dm*/
\n  // let detail =   false ? $.shareData.permissionData?.detail : true;/*dm*/
\n  let isDelete = setting['default'].delete;// false ? $.shareData.permissionData?.delete : true;/*dm*/
\n  if (setting[viewCode]){/*dm*/
\n    isEdit = setting[viewCode].edit;/*dm*/
\n    isDelete = setting[viewCode].delete;/*dm*/
\n  }/*dm*/
\n  if (isEdit) {/*dm*/
\n    list.push({ \"label\": \"编辑\", \"name\": \"edit\", \"type\": \"text\", \"round\": \"round\", \"size\": \"small\" })/*dm*/
\n  };/*dm*/
\n  // if ( detail) {/*dm*/
\n  //   list.push({ \"label\": \"详情\", \"name\": \"detail\", \"type\": \"text\", \"round\": \"round\", \"size\": \"small\" })/*dm*/
\n  // };/*dm*/
\n  if ( isDelete) {/*dm*/
\n    list.push({ \"label\": \"删除\", \"name\": \"delete\", \"type\": \"text\", \"round\": \"round\", \"size\": \"small\" });/*dm*/
\n  };/*dm*/
\n  return list;/*dm*/
\n}}",
																			"moreRound":"${false}",
																			"max-count":"${3}",
																			"textAlign":"left",
																			"marginLeft":"${0 * 1}"
																		}
																	}
																],
																"componentName":"TableColumn",
																"id":"TableColumn94069",
																"props":{
																	"prop":"action",
																	"fixed":"right",
																	"label":"操作",
																	"v-slot":"scope",
																	"min-width":"172px"
																}
															}
														],
														"componentName":"VIf",
														"id":"VIf-QwGZxScY",
														"props":{
															"conditions":"${[$.shareData.var_tables[viewCode]?.setting?.mode==='normal' && enableOperateColumnu]}"
														}
													}
												],
												"action":{
													"fire":{
														"header-dragend":{
															"arguments":[
																"newWidth",
																"oldWidth",
																"column",
																"event"
															],
															"actions":[
																{
																	"handler":"${function handler(newWidth,oldWidth,column,event){/*dm*/
\nconst genId = $.functions.fun_genTableId(viewCode, modelName);/*dm*/
\n/*dm*/
\nconst { property } = column/*dm*/
\n/*dm*/
\n/*dm*/
\nconst { businessType, fieldWidth, minWidth } = $.functions.getFieldsBusinessType(viewCode, property);/*dm*/
\n/*dm*/
\n/*dm*/
\nconst minNewWidth = Math.max(minWidth, newWidth)/*dm*/
\n/*dm*/
\n/*dm*/
\nconst newColumnWidth = {/*dm*/
\n  ...$.shareData.tableColumnWidth.widths,/*dm*/
\n  [property]: minNewWidth/*dm*/
\n}/*dm*/
\n/*dm*/
\n$.shareData.tableColumnWidth.widths = newColumnWidth;/*dm*/
\nconst refName = viewCode;/*dm*/
\n/*dm*/
\n// 修复数据、重置表格宽度/*dm*/
\n/*dm*/
\nthis.$refs[refName].store.states._columns = this.$refs[refName]?.store.states._columns.map(item => {/*dm*/
\n/*dm*/
\n  if (item.id === column.id && item.minWidth && item.width) {/*dm*/
\n/*dm*/
\n    return {/*dm*/
\n      ...item,/*dm*/
\n      realWidth: minNewWidth,/*dm*/
\n      width: minNewWidth/*dm*/
\n    }/*dm*/
\n/*dm*/
\n  }/*dm*/
\n  return item/*dm*/
\n})/*dm*/
\n/*dm*/
\nthis.$refs[refName]?.store?.scheduleLayout(true);/*dm*/
\n/*dm*/
\nthis.$nextTick(() => {/*dm*/
\n  // 更新列的宽度/*dm*/
\n/*dm*/
\n  this.$refs[refName].layout.updateColumnsWidth();/*dm*/
\n/*dm*/
\n})/*dm*/
\n/*dm*/
\nlocalStorage.setItem(genId, JSON.stringify(newColumnWidth))}}",
																	"type":"function"
																}
															]
														},
														"current-change":{
															"arguments":[
																"value"
															],
															"actions":[
																{
																	"handler":"${function Fun84098(val){console.log(value, 888);/*dm*/
\nif ($.shareData.var_tables[viewCode]?.setting?.selectRowType === 'single') {/*dm*/
\n  $.shareData.var_tablesAction({/*dm*/
\n    [viewCode]: {/*dm*/
\n      singleSelection: value.id/*dm*/
\n    }/*dm*/
\n  })/*dm*/
\n}/*dm*/
\n}}",
																	"type":"function"
																}
															]
														},
														"sort-change":{
															"arguments":[
																"column",
																"prop"
															],
															"actions":[
																{
																	"handler":"${function Fun_47sXNY(column, prop){/*dm*/
\nif ($.shareData.var_tables[viewCode]?.searchQuery?.querySorts.length > 1) {/*dm*/
\n/*dm*/
\n  $.shareData.var_tables[viewCode]?.searchQuery?.querySorts.splice(0, 1);/*dm*/
\n/*dm*/
\n}/*dm*/
\n/*dm*/
\n/*dm*/
\n/*dm*/
\nif (column.order) {/*dm*/
\n/*dm*/
\n  $.shareData.var_tables[viewCode]?.searchQuery?.querySorts.unshift({/*dm*/
\n/*dm*/
\n    fieldName: column.prop,/*dm*/
\n/*dm*/
\n    desc: column.order == \"descending\"/*dm*/
\n/*dm*/
\n  })/*dm*/
\n/*dm*/
\n}/*dm*/
\n/*dm*/
\n/*dm*/
\n/*dm*/
\nawait $.functions.fun_loadTableData(modelName, viewCode);/*dm*/
\n/*dm*/
\n}}",
																	"type":"function"
																}
															]
														},
														"selection-change":{
															"arguments":[
																"selection"
															],
															"actions":[
																{
																	"handler":"${function Fun37467(selection){$.shareData.var_tablesAction({/*dm*/
\n  [viewCode]: {/*dm*/
\n    multipleSelection: selection/*dm*/
\n  }/*dm*/
\n})}}",
																	"type":"function"
																}
															]
														}
													}
												},
												"componentName":"Table",
												"id":"Table-aE3yDr3C",
												"title":"表格区域",
												"attrs":{
													"class":"${$.shareData.var_tables[viewCode]?.setting?.headerEllipsis?'headerEllipsis':''}"
												},
												"props":{
													"border":"${false}",
													"max-height":"360px",
													"headerEllipsis":"${$.shareData.var_tables[viewCode]?.setting?.headerEllipsis}",
													"data":"${$.shareData.var_tables[viewCode]?.tableList}",
													"show-header":"${$.shareData.var_tables[viewCode]?.setting?.showHeader}",
													"ref":"${viewCode}",
													"size":"small",
													"v-loading":"${$.shareData.var_tables[viewCode]?.loading}",
													"tooltip-effect":"dark",
													"tree-props":"${{ hasChildren: \"hasChildren\", children: \"children\" }}",
													"stripe":"${$.shareData.var_tables[viewCode]?.setting?.stripe}",
													"style":"width: 100%",
													"height":"${tableStyle?.height}",
													"row-key":"id",
													"highlight-current-row":"${$.shareData.var_tables[viewCode]?.setting?.selectRowType === 'single'}"
												}
											}
										],
										"componentName":"DivWrapper",
										"id":"DivWrapper-pxDw7byR",
										"title":"表格+操作按钮",
										"props":{
											"name":"DivWrapper"
										},
										"attrs":{
											"class":"records-joy-table-body"
										}
									}
								],
								"action":{
									"fire":{
										"handleSizeChange":{
											"arguments":[
												"value"
											],
											"actions":[
												{
													"handler":"${function Fun63971(val){$.shareData.var_tables[viewCode].params.pageSize = value;/*dm*/
\nlocalStorage.setItem(`var_basePageParams_${viewCode}`, value)/*dm*/
\n$.functions.fun_loadTableData(modelName, viewCode)/*dm*/
\n}}",
													"type":"function"
												}
											]
										},
										"handleCurrentChange":{
											"arguments":[
												"value"
											],
											"actions":[
												{
													"handler":"${function Fun29265(value){$.shareData.var_tables[viewCode].params.pageNum = value;/*dm*/
\n$.functions.fun_loadTableData(modelName, viewCode);/*dm*/
\n}}",
													"type":"function"
												}
											]
										}
									}
								},
								"componentName":"ModelTable",
								"id":"ModelTable-GtkRfNNb",
								"title":"列表组件",
								"version":"1.8.0",
								"props":{
									"pageSizes":"${$.shareData.var_tables[viewCode]?.setting?.pageSizes}",
									"editPage":"undefined",
									"buttonShowIcon":false,
									"showPagination":"${$.shareData.var_tables[viewCode]?.setting?.showPagination}",
									"showRefresh":false,
									"batchDelFlag":false,
									"buttonShowText":true,
									"addFlag":true,
									"mode":"${$.shareData.var_tables[viewCode]?.setting?.mode}",
									"columnShowIcon":false,
									"modelName":"modelName",
									"exportDataFlag":false,
									"ref":"${viewCode}",
									"showPlanView":true,
									"pageParams":"${$.shareData.var_tables[viewCode]?.params}",
									"addPage":"undefined",
									"paginationMode":"${$.shareData.var_tables[viewCode]?.setting?.paginationMode}",
									"detailPage":"undefined",
									"model":"multiple",
									"draftFlag":false,
									"multipleSelection":"${$.shareData.var_tables[viewCode]?.multipleSelection}"
								},
								"attrs":{
									"style":{
										"height":"100%"
									},
									"class":"tableScolle sub-model-table"
								}
							}
						],
						"componentName":"DivWrapper",
						"id":"DivWrapper-GBwaSCNT"
					}
				],
				"componentName":"Export",
				"id":"Export-r6fpdeQd",
				"attrs":{
					"name":"table",
					"style":{},
					"run":false
				},
				"props":{
					"modelName":"",
					"modelText":"${}",
					"fieldName":"${}",
					"enableOperateColumnu":"${}",
					"tableStyle":"${{'height': '106px'}}",
					"viewCode":""
				}
			},
			{
				"componentType":"custom",
				"children":[
					{
						"componentType":"custom",
						"children":[
							{
								"componentType":"custom",
								"children":[
									{
										"componentType":"custom",
										"children":[
											{
												"libraryName":"@jd/joyui",
												"componentType":"UILibrary",
												"__setting":{},
												"children":[
													{
														"libraryName":"@jd/joyui",
														"componentType":"UILibrary",
														"action":{
															"fire":{
																"handleReturn":{
																	"arguments":[
																		""
																	],
																	"actions":[
																		{
																			"handler":"${function handler(){if (history.state) {/*dm*/
\n  $.bom.page.close() || $.bom.route('go', -1);/*dm*/
\n} else {/*dm*/
\n  $.bom.route('push', { path: '/' })/*dm*/
\n}}}",
																			"type":"function"
																		}
																	]
																}
															}
														},
														"componentName":"TitleButtonBreadCrumb",
														"id":"TitleButtonBreadCrumb-zG2nxzkk",
														"title":"按钮面包屑",
														"version":"1.8.0",
														"props":{
															"modelName":"${() => { if($.shareData.var_pageInfo.isEdit) { return `${$.shareData.var_pageInfo.modelText}: ${$.shareData.var_saveForm.name || ''}` } else { return $.shareData.var_pageInfo.title } }}",
															"btnText":"",
															"size":"medium",
															"round":"${false}",
															"plain":"${false}",
															"nativeType":"button",
															"icon":"joy-left",
															"disabled":"${false}",
															"loading":"${false}",
															"autofocus":"${false}",
															"btnType":"text",
															"circle":"${false}"
														}
													}
												],
												"componentName":"TitlePage",
												"id":"TitlePage-RiyGGpHD",
												"title":"标题",
												"version":"1.8.0",
												"props":{}
											}
										],
										"componentName":"VIf",
										"id":"VIf-cFtQ7eFn",
										"props":{
											"conditions":"${[$.shareData.var_pageInfo.showHeader && $.shareData.var_setting.enableTitle]}"
										}
									}
								],
								"componentName":"DivWrapper",
								"id":"DivWrapper-6wKaMnNP",
								"title":"Header"
							},
							{
								"libraryName":"@jd/joyui",
								"componentType":"UILibrary",
								"children":[
									{
										"componentType":"custom",
										"children":[
											{
												"libraryName":"@jd/joyui",
												"componentType":"UILibrary",
												"children":[
													{
														"componentType":"custom",
														"children":[
															{
																"componentType":"custom",
																"children":[
																	{
																		"children":[
																			{
																				"children":[
																					{
																						"componentType":"custom",
																						"children":[
																							{
																								"libraryName":"@jd/joyui",
																								"componentType":"UILibrary",
																								"action":{
																									"fire":{
																										"change":{
																											"arguments":[
																												"value"
																											],
																											"actions":[
																												{
																													"handler":"${function Fun_PxhJyx(value){this.$set($.shareData.var_saveForm, item.fieldName, value);/*dm*/
\n}}",
																													"type":"function"
																												}
																											]
																										},
																										"findRelatedOpen":{
																											"arguments":[
																												"field"
																											],
																											"actions":[
																												{
																													"handler":"${function handler(){const { modelName, modelText } = item.field.relatedModel;/*dm*/
\nconst fieldName = item.field.fieldName;/*dm*/
\nconst viewCode = item.field.viewCode; /*dm*/
\nconst queryConditions = JSON.parse(item.field.queryConditions) || [];/*dm*/
\n/*dm*/
\n$.shareData.var_tablesAction({/*dm*/
\n  [viewCode]: {/*dm*/
\n    singleSelection: $.shareData.var_saveForm[fieldName]?.id,/*dm*/
\n    searchQuery: {/*dm*/
\n      queryConditions,/*dm*/
\n      combinationRule: item.field.combinationRule/*dm*/
\n    }/*dm*/
\n  }/*dm*/
\n})/*dm*/
\n/*dm*/
\n$.functions.fun_loadTableData(modelName, viewCode)/*dm*/
\n$.shareData.var_findRelationInfoAction({/*dm*/
\n  modelName,/*dm*/
\n  modelText,/*dm*/
\n  fieldName,/*dm*/
\n  viewCode,/*dm*/
\n  visible: true/*dm*/
\n})/*dm*/
\n// console.log(JSON.stringify($.shareData.var_findRelationInfo, null, 2), JSON.stringify($.shareData.var_tables[viewCode], null, 2),'xxxxx');}}",
																													"type":"function"
																												}
																											]
																										}
																									}
																								},
																								"componentName":"ModelField",
																								"id":"ModelField-nMky2QE7",
																								"title":"ModelField表单子项",
																								"version":"1.8.0",
																								"props":{
																									"originProps":"${{/*dm*/
\n  column: $.shareData.var_setting?.group?.[items.groupCode]?.columnTotal || $.shareData.var_setting.columnTotal/*dm*/
\n}}",
																									"formType":"save",
																									"fieldName":"${item.field.fieldName}",
																									"elementProps":{
																										"disabled":"${() => {\r/*dm*/
\n\r/*dm*/
\n  // 如果权限里面设置了编辑权限，在编辑的时候disabled\r/*dm*/
\n  if ($.shareData.var_pageInfo.isEdit) {\r/*dm*/
\n    if (item.modelFieldPermission !== 2) {\r/*dm*/
\n      return true\r/*dm*/
\n    }\r/*dm*/
\n  }\r/*dm*/
\n  return item.field.fieldName == $.shareData.var_subModelInfo.fieldName || $.shareData.var_disabledFormField.includes(item.field.fieldName)\r/*dm*/
\n}}"
																									},
																									"isShowField":"${item.field.fieldName == $.shareData.var_subModelInfo.fieldName}",
																									"fieldProps":"${() => {/*dm*/
\n  const res = {/*dm*/
\n    ...item.field/*dm*/
\n  }/*dm*/
\n  if ($.shareData.var_setting?.fieldInfo?.[item.fieldName]) {/*dm*/
\n    res.fieldText = $.shareData.var_setting.fieldInfo[item.fieldName]?.fieldName/*dm*/
\n  }/*dm*/
\n  return res;/*dm*/
\n}}",
																									"businessType":"${item.field?.businessType}",
																									"fieldType":"${item.field.fieldType}",
																									"value":"${$.shareData.var_saveForm[item.fieldName]}",
																									"fieldText":""
																								},
																								"attrs":{
																									"setting":"${JSON.stringify({ name: 'form-field', fieldInfo: { fieldName: item.field.fieldName, fieldText: item.field.fieldText } })}"
																								}
																							}
																						],
																						"componentName":"VFor",
																						"id":"VFor-8JdtMfpF",
																						"title":"Model循环容器",
																						"props":{
																							"forEach":"${items.fields}",
																							"forEachKey":"item",
																							"index":"$index"
																						}
																					},
																					{
																						"componentType":"custom",
																						"children":[
																							{
																								"componentType":"custom",
																								"children":[
																									{
																										"componentType":"custom",
																										"children":[
																											{
																												"componentType":"custom",
																												"componentName":"TextWrapper",
																												"id":"TextWrapper-HETdFC8b",
																												"props":{
																													"children":"${__subModel.modelText}"
																												},
																												"attrs":{
																													"style":{
																														"border-left":"3px solid #4871E4",
																														"display":"inline-block",
																														"padding-left":"4px",
																														"line-height":1,
																														"height":"14px"
																													},
																													"class":""
																												}
																											},
																											{
																												"componentType":"custom",
																												"action":{
																													"fire":{
																														"click":{
																															"arguments":[],
																															"actions":[
																																{
																																	"handler":"${function handler(){/*dm*/
\n/*dm*/
\nconst { modelName, modelText, fieldName, relatedListViewCode } = __subModel;/*dm*/
\nconst addPath = $.shareData.var_setting.table?.[relatedListViewCode]?.routeUrlAdd || $.functions.fun_getOperatePath(modelName, 'form')/*dm*/
\n/*dm*/
\n$.bom.page?.open(addPath, {/*dm*/
\n  pageType: 'subModel',/*dm*/
\n  subModel: {/*dm*/
\n    modelName,/*dm*/
\n    modelText,/*dm*/
\n    fieldName/*dm*/
\n  }/*dm*/
\n}, async (data) => {/*dm*/
\n if (data) {/*dm*/
\n    const tableKey = relatedListViewCode; /*dm*/
\n    let transform = await $.global.functions.modelValueFormat(modelName);/*dm*/
\n    // 模型列表数据/*dm*/
\n    $.shareData.var_tables[tableKey].tableList.push({/*dm*/
\n      ...data,/*dm*/
\n      id: 'subModel_' + $.shareData.var_tables[tableKey].tableData.addDatas.length/*dm*/
\n    });/*dm*/
\n   $.shareData.var_tables[tableKey].tableData.addDatas.push(transform.output(data));/*dm*/
\n  }/*dm*/
\n}, {/*dm*/
\n  options: {/*dm*/
\n    width: '960px',/*dm*/
\n    title: '新建',/*dm*/
\n    closeOnClickModal: false,/*dm*/
\n    customClass: 'sub-model-dialog',/*dm*/
\n  },/*dm*/
\n  type: 'dialog'/*dm*/
\n});/*dm*/
\n/*dm*/
\n/*dm*/
\n/*dm*/
\n}}",
																																	"type":"function"
																																}
																															]
																														}
																													}
																												},
																												"componentName":"Button",
																												"id":"Button-jmMXnhGB",
																												"props":{
																													"size":"small",
																													"children":"新建",
																													"type":"text"
																												}
																											}
																										],
																										"componentName":"DivWrapper",
																										"id":"DivWrapper-nPhYEJFD",
																										"attrs":{
																											"style":{
																												"display":"flex",
																												"justify-content":"space-between",
																												"align-items":"center",
																												"margin-bottom":"8px"
																											}
																										}
																									},
																									{
																										"children":[
																											{
																												"componentName":"Import",
																												"id":"Import-df3ZFXGJ",
																												"attrs":{
																													"name":"table",
																													"style":{}
																												},
																												"props":{
																													"modelName":"${__subModel.modelName}",
																													"modelText":"${__subModel.modelText}",
																													"fieldName":"${__subModel.fieldName}",
																													"enableOperateColumnu":"${true}",
																													"tableStyle":"${{'height': '106px'}}",
																													"viewCode":"${__subModel.relatedListViewCode}"
																												}
																											}
																										],
																										"componentName":"div",
																										"id":"div-xYXj32Mx",
																										"attrs":{
																											"setting":"${JSON.stringify({/*dm*/
\n  name: 'table', id: __subModel.id, editorViewCode: __subModel.relatedListViewCode, modelName: __subModel.modelName,/*dm*/
\n  \"views\": { \"copy\": false, detail: false, \"advanced\": false, \"keyword\": false }/*dm*/
\n})}"
																										}
																									}
																								],
																								"componentName":"DivWrapper",
																								"id":"DivWrapper-zc5SFGKx",
																								"attrs":{
																									"style":{
																										"padding":"0 8px",
																										"clear":"both",
																										"margin-bottom":"16px"
																									},
																									"class":"sub-model-root"
																								}
																							}
																						],
																						"componentName":"VFor",
																						"id":"VFor-GbDPkcaw",
																						"title":"Model循环容器",
																						"props":{
																							"forEach":"${items.subModelList}",
																							"forEachKey":"__subModel",
																							"index":"$index"
																						}
																					}
																				],
																				"componentName":"CollapseItem",
																				"id":"CollapseItem74893",
																				"props":{
																					"name":"${'isUnfold'}",
																					"title":"${items.groupName}"
																				},
																				"attrs":{
																					"style":{}
																				}
																			}
																		],
																		"componentName":"Collapse",
																		"id":"Collapse-csdFrjnh",
																		"props":{
																			"value":"${$.shareData.var_setting.group?.[items.groupCode]?.isUnfold === false ? '' : 'isUnfold'}"
																		},
																		"attrs":{
																			"style":{
																				"flex":"1",
																				"margin-bottom":"16px"
																			},
																			"class":"${}"
																		}
																	}
																],
																"componentName":"Col",
																"id":"Col-kH65H2Wd",
																"attrs":{
																	"style":{
																		"padding":"0px",
																		"width":"100%"
																	},
																	"class":"${$.shareData.var_setting?.group?.[items.groupCode]?.enableTitle === false ? 'system-collapse-hidden-wrapper' : 'xxxxx'}",
																	"setting":"${JSON.stringify({name: 'group', id: items.id, groupCode: items.groupCode,  groupName: items.groupName })}"
																},
																"props":{
																	"span":24
																}
															}
														],
														"componentName":"VFor",
														"id":"VFor-yAHxGMDw",
														"title":"Collapse循环容器",
														"props":{
															"forEach":"${$.shareData.var_viewDetailList}",
															"forEachKey":"items",
															"index":"$index"
														}
													}
												],
												"componentName":"ModelForm",
												"id":"ModelForm-Yc5AxnzA",
												"title":"表单组件",
												"version":"1.8.0",
												"props":{
													"ref":"savesForm",
													"size":"small",
													"labelPosition":"top",
													"formData":"${$.shareData.var_saveForm}"
												},
												"attrs":{
													"class":"editPageForm"
												}
											}
										],
										"componentName":"DivWrapper",
										"id":"DivWrapper-NDkQk6Jp",
										"title":"Main",
										"attrs":{
											"style":{
												"padding":"0"
											}
										}
									}
								],
								"componentName":"ModelDrivenMarkup",
								"id":"ModelDrivenMarkup-s3tX8yp3",
								"title":"模型辅助配置",
								"version":"1.8.0",
								"props":{
									"modelName":"ss",
									"modelText":"全字段",
									"formType":"save",
									"findRelatedTableKey":"findRelated"
								}
							}
						],
						"componentName":"DivWrapper",
						"id":"DivWrapper-R5EfQc3n",
						"title":"Header+Main",
						"attrs":{
							"style":{
								"overflow":"auto",
								"flex":"1",
								"box-sizing":"border-box"
							}
						}
					},
					{
						"toolbar":true,
						"componentType":"custom",
						"children":[
							{
								"componentType":"custom",
								"action":{
									"fire":{
										"click":{
											"actions":[
												{
													"handler":"${function undefined() {/*dm*/
\n                          $.bom.page.close() || $.bom.route('go', -1);/*dm*/
\n                        }}",
													"type":"function"
												}
											]
										}
									}
								},
								"componentName":"Button",
								"id":"Button-XRneykFP",
								"attrs":{
									"class":"bottom-btn"
								},
								"props":{
									"size":"small",
									"children":"取消"
								},
								"chosen":false
							},
							{
								"componentType":"custom",
								"children":[
									{
										"componentType":"custom",
										"action":{
											"fire":{
												"click":{
													"actions":[
														{
															"handler":"${function Fun34975(){/*dm*/
\nlet format = await $.functions.fun_formFormat($.shareData.var_setting.modelName);/*dm*/
\nconst saveForm = { ...format.output($.shareData.var_saveForm) }/*dm*/
\nconst draftId = $.functions.fun_getQueryValue('draftId');/*dm*/
\nconst views = $.shareData.var_viewDetailList;/*dm*/
\nconst subModel = {};/*dm*/
\n/*dm*/
\nviews.forEach(group => {/*dm*/
\n  group.subModelList.forEach(model => {/*dm*/
\n    const tableKey = model.relatedListViewCode;/*dm*/
\n    const tableData = $.shareData.var_tables[tableKey].tableData;/*dm*/
\n     subModel['$subModel$' + model.modelName + '$' + model.fieldName] = {/*dm*/
\n      addDatas: tableData?.addDatas || [],/*dm*/
\n      updateDatas: tableData?.updateDatas || [],/*dm*/
\n      deleteIds: tableData?.deleteIds || [],/*dm*/
\n    }/*dm*/
\n  })/*dm*/
\n})/*dm*/
\nconst formBody = {/*dm*/
\n  \"name\": $.shareData.var_pageInfo.modelText,/*dm*/
\n  \"vn\": 0,/*dm*/
\n  \"data_info\": JSON.stringify({/*dm*/
\n    ...saveForm,/*dm*/
\n    ...subModel,/*dm*/
\n  }),/*dm*/
\n  \"model_name\": $.shareData.var_setting.modelName,/*dm*/
\n  '$__draftId': draftId/*dm*/
\n};/*dm*/
\nawait $.shareData.api_postCommonDraftAction(formBody);/*dm*/
\nif ($.shareData.api_postCommonDraft.code == 200) {/*dm*/
\n  this.$message.success('暂存成功!');/*dm*/
\n  $.bom.page.close() || $.bom.route('go', -1);/*dm*/
\n} else {/*dm*/
\n  this.$message.error($.shareData.api_postCommonDraft.msg);/*dm*/
\n}/*dm*/
\n}}",
															"type":"function"
														}
													]
												}
											}
										},
										"componentName":"Button",
										"id":"Button-zMMDTTpm",
										"props":{
											"size":"small",
											"children":"暂存"
										},
										"chosen":false
									}
								],
								"id":"VIf-39518XX",
								"componentName":"VIf",
								"title":"逻辑容器",
								"props":{
									"conditions":"${[$.shareData.var_pageInfo.openDraft && !$.shareData.var_pageInfo?.isEdit && true]}"
								}
							},
							{
								"componentType":"custom",
								"action":{
									"fire":{
										"click":{
											"actions":[
												{
													"handler":"${function Fun98425(){const modelName = $.shareData.var_setting.modelName;\r/*dm*/
\nthis.$refs['savesForm'].form.validate(async validate => {\r/*dm*/
\n  if (validate) {\r/*dm*/
\n    let format = await $.global.functions.modelValueFormat(modelName, $.shareData.var_pageInfo.isEdit ? 'add' : 'edit');\r/*dm*/
\n    // 如果是子模型，仅提供数据\r/*dm*/
\n    const pageParams = $.bom.page.params()?.data;\r/*dm*/
\n    if (pageParams?.pageType == 'subModel') {\r/*dm*/
\n      const {\r/*dm*/
\n        fieldName\r/*dm*/
\n      } = pageParams.subModel;\r/*dm*/
\n      $.shareData.var_saveForm[fieldName] = undefined;\r/*dm*/
\n      $.bom.page.close($.shareData.var_saveForm);\r/*dm*/
\n      // $.bom.page.close(format.output($.shareData.var_saveForm));\r/*dm*/
\n      return;\r/*dm*/
\n    }\r/*dm*/
\n    let { id, ...saveForm } = format.output($.shareData.var_saveForm);\r/*dm*/
\n    // 父级模型：如果包含子模型，则处理子模型数据\r/*dm*/
\n    const views = $.shareData.var_viewDetailList;\r/*dm*/
\n    const subModel = {};\r/*dm*/
\n   \r/*dm*/
\n    views.forEach(group => {\r/*dm*/
\n      group.subModelList.forEach(model => {\r/*dm*/
\n        const tableKey = model.relatedListViewCode;\r/*dm*/
\n        const tableData = $.shareData.var_tables[tableKey].tableData;\r/*dm*/
\n        subModel['$subModel$' + model.modelName + '$' + model.fieldName] = {\r/*dm*/
\n          addDatas: tableData?.addDatas || [],\r/*dm*/
\n          updateDatas: tableData?.updateDatas || [],\r/*dm*/
\n          deleteIds: tableData?.deleteIds || [],\r/*dm*/
\n        }\r/*dm*/
\n      })\r/*dm*/
\n    })\r/*dm*/
\n    const formData = {\r/*dm*/
\n      modelName,\r/*dm*/
\n      ...saveForm,\r/*dm*/
\n      ...subModel\r/*dm*/
\n    }\r/*dm*/
\n    if ($.shareData.var_pageInfo.isEdit) {\r/*dm*/
\n      formData.id = id;\r/*dm*/
\n    }\r/*dm*/
\n    $.shareData.var_saveLoadingAction(true);\r/*dm*/
\n    await $.shareData.api_saveDataAction(formData);\r/*dm*/
\n    if ($.shareData.api_saveData.code == 200) {\r/*dm*/
\n      this.$message.success($.shareData.var_pageInfo.typeTitle + '成功!');\r/*dm*/
\n      $.bom.page.close({ flag: true, data: $.shareData.api_saveData.data }) || $.bom.route('push', $.shareData.var_setting.listPath);\r/*dm*/
\n      $.shareData.var_saveLoadingAction(false);\r/*dm*/
\n    } else {\r/*dm*/
\n      $.shareData.var_saveLoadingAction(false);\r/*dm*/
\n    }\r/*dm*/
\n  } else {\r/*dm*/
\n    this.$nextTick(() => {\r/*dm*/
\n      var elements = document.querySelectorAll('.el-form-item__error');\r/*dm*/
\n      elements.length && elements[0].closest('.el-form-item').scrollIntoView({ behavior: \"smooth\", block: \"start\" });\r/*dm*/
\n    })\r/*dm*/
\n  }\r/*dm*/
\n});\r/*dm*/
\n}}",
													"type":"function"
												}
											]
										}
									}
								},
								"componentName":"Button",
								"id":"Button-FfRBNpin",
								"props":{
									"size":"small",
									"children":"保存",
									"type":"primary",
									"loading":"${$.shareData.var_saveLoading}"
								},
								"chosen":false
							},
							{
								"componentType":"custom",
								"children":[
									{
										"componentType":"custom",
										"action":{
											"fire":{
												"click":{
													"actions":[
														{
															"handler":"${function Fun75449(){/*dm*/
\nthis.$refs['savesForm'].form.validate(async validate => {/*dm*/
\n  if (validate) {/*dm*/
\n    let format = await $.functions.fun_formFormat($.shareData.var_setting.modelName);/*dm*/
\n    let formData = format.output($.shareData.var_saveForm);/*dm*/
\n    if ($.shareData.var_pageInfo.wfOpen) {/*dm*/
\n      // 流程标识/*dm*/
\n      formData.$__saveAndSubmitWf = true;/*dm*/
\n    }/*dm*/
\n    // 父级模型：如果包含子模型，则处理子模型数据/*dm*/
\n    const views = $.shareData.var_viewDetailList;/*dm*/
\n    const subModel = {};/*dm*/
\n    views.forEach(group => {/*dm*/
\n      group.subModelList.forEach(model => {/*dm*/
\n        const tableKey = model.relatedListViewCode;/*dm*/
\n        const tableData = $.shareData.var_tables[tableKey].tableData;/*dm*/
\n        subModel['$subModel$' + model.modelName + '$' + model.fieldName] = {/*dm*/
\n          addDatas: tableData?.addDatas || [],/*dm*/
\n          updateDatas: tableData?.updateDatas || [],/*dm*/
\n          deleteIds: tableData?.deleteIds || [],/*dm*/
\n        }/*dm*/
\n      })/*dm*/
\n    })/*dm*/
\n    await $.shareData.api_saveDataAction({/*dm*/
\n      ...formData,/*dm*/
\n      ...subModel,/*dm*/
\n      modelName: $.shareData.var_setting.modelName/*dm*/
\n    });/*dm*/
\n    if ($.shareData.api_saveData.code == 200) {/*dm*/
\n      this.$message.success(formData?.id ? '编辑成功!' : '新增成功');/*dm*/
\n      $.bom.page.close() || $.bom.route('go', -1);/*dm*/
\n      await $.shareData.var_saveLoadingAction(false);/*dm*/
\n    } else {/*dm*/
\n      this.$message.error($.shareData.api_saveData.msg);/*dm*/
\n      await $.shareData.var_saveLoadingAction(false);/*dm*/
\n    }/*dm*/
\n  }/*dm*/
\n});/*dm*/
\n}}",
															"type":"function"
														}
													]
												}
											}
										},
										"componentName":"Button",
										"id":"Button-fQGbx4MM",
										"props":{
											"size":"small",
											"children":"保存并提交",
											"type":"primary"
										},
										"chosen":false
									}
								],
								"id":"VIf-39518XX1",
								"componentName":"VIf",
								"title":"逻辑容器",
								"props":{
									"conditions":"${[$.shareData.var_pageInfo.wfOpen && $.shareData.var_pageInfo.wfStatus]}"
								}
							}
						],
						"componentName":"Col",
						"id":"Col-abenQ63p",
						"title":"Footer",
						"props":{
							"style":{
								"flex-shrink":"0"
							},
							"span":24
						},
						"attrs":{
							"class":"editPageFooter"
						}
					}
				],
				"componentName":"DivWrapper",
				"id":"DivWrapper-4JkGE5e8",
				"title":"全字段新建页面",
				"attrs":{
					"style":{
						"flex-direction":"column",
						"display":"flex"
					},
					"class":"editPage"
				}
			},
			{
				"componentType":"custom",
				"children":[
					{
						"componentType":"custom",
						"children":[
							{
								"componentType":"custom",
								"children":[
									{
										"libraryName":"@jd/joyui",
										"componentType":"UILibrary",
										"children":[
											{
												"libraryName":"@jd/joyui",
												"componentType":"UILibrary",
												"children":[
													{
														"children":[
															{
																"libraryName":"@jd/joyui",
																"componentType":"UILibrary",
																"action":{
																	"fire":{
																		"change":{
																			"arguments":[
																				"data"
																			],
																			"actions":[
																				{
																					"handler":"${function handler(){/*dm*/
\n// $.shareData.var_currentPlan.conditionsValue[$index] = data;/*dm*/
\nthis.$set($.shareData.var_currentSearchCondition.conditionsValue, $index, data);/*dm*/
\n}}",
																					"type":"function"
																				}
																			]
																		},
																		"reset":{
																			"arguments":[
																				"data"
																			],
																			"actions":[
																				{
																					"handler":"${function Handler(data){const modelName = $.shareData.var_findRelatedId;/*dm*/
\n$.shareData.var_currentSearchCondition.conditionsValue = JSON.parse(JSON.stringify($.shareData.var_currentSearchCondition.standardQueryConditions))/*dm*/
\n$.shareData.var_baseSearchQuery[\"findRelated\"].queryConditions = [/*dm*/
\n  ...$.shareData.var_stagingfindRelatedConditions,/*dm*/
\n  ...$.shareData.var_currentSearchCondition.conditionsValue,/*dm*/
\n];/*dm*/
\n$.functions.fun_loadTableData(modelName, \"findRelated\");/*dm*/
\n}}",
																					"type":"function"
																				}
																			]
																		}
																	}
																},
																"componentName":"ModelField",
																"id":"ModelField-pEA2AyHQ",
																"title":"ModelField",
																"version":"1.8.3",
																"props":{
																	"originProps":{
																		"span":12
																	},
																	"formType":"search",
																	"fieldName":"${__item.field.fieldName}",
																	"isShowField":"${__item?.value?.isShow}",
																	"fieldProps":"${__item.field}",
																	"businessType":"${__item.field.businessType}",
																	"fieldType":"${__item.field.fieldType}",
																	"value":"${__item.value}",
																	"fieldText":""
																}
															}
														],
														"id":"VFor-8HtK468F",
														"componentName":"VFor",
														"props":{
															"forEach":"${$.shareData.var_currentSearchCondition.conditionsValue.map(item => {/*dm*/
\n  const field = $.global.shareData.modelsInfoData.fieldsMap[$.shareData.var_findRelationInfo.modelName]?.get(item.fieldName.split('.')[0]);/*dm*/
\n    if(field) {/*dm*/
\n      return {/*dm*/
\n        value: item, field: field/*dm*/
\n      }/*dm*/
\n    } else {/*dm*/
\n           return null/*dm*/
\n    }/*dm*/
\n  }/*dm*/
\n).filter(Boolean);}",
															"forEachKey":"__item",
															"index":"$index"
														}
													}
												],
												"action":{
													"fire":{
														"search":{
															"arguments":[
																"data"
															],
															"actions":[
																{
																	"handler":"${function handler(){const { modelName, fieldName, viewCode } = $.shareData.var_findRelationInfo;/*dm*/
\n$.shareData.var_tablesAction(/*dm*/
\n  {/*dm*/
\n    [viewCode]: {/*dm*/
\n      params: {/*dm*/
\n        pageNum: 1/*dm*/
\n      },/*dm*/
\n      searchQuery: {/*dm*/
\n        queryConditions: data.queryConditions/*dm*/
\n      }/*dm*/
\n    }/*dm*/
\n  }/*dm*/
\n)/*dm*/
\n// this.$set($.shareData.var_tables[viewCode].searchQuery, 'queryConditions', {/*dm*/
\n//   conditionType: \"LIKE\",/*dm*/
\n//   fieldName: \"name\",/*dm*/
\n//   value: $.shareData.var_findRelatedForm.name,/*dm*/
\n// });/*dm*/
\n$.functions.fun_loadTableData(modelName, viewCode);/*dm*/
\n}}",
																	"type":"function"
																}
															]
														},
														"reset":{
															"arguments":[],
															"actions":[
																{
																	"handler":"${function Handler(){const { modelName, fieldName, viewCode } = $.shareData.var_findRelationInfo;/*dm*/
\n$.shareData.var_currentSearchCondition.conditionsValue = JSON.parse(JSON.stringify($.shareData.var_currentSearchCondition.standardQueryConditions))/*dm*/
\n$.shareData.var_tablesAction(/*dm*/
\n  {/*dm*/
\n    [viewCode]: {/*dm*/
\n      params: {/*dm*/
\n        pageNum: 1/*dm*/
\n      },/*dm*/
\n      searchQuery: {/*dm*/
\n        queryConditions: $.shareData.var_currentSearchCondition.conditionsValue/*dm*/
\n      }/*dm*/
\n    }/*dm*/
\n  }/*dm*/
\n)/*dm*/
\n// this.$set($.shareData.var_tables[viewCode].searchQuery, 'queryConditions', {/*dm*/
\n//   conditionType: \"LIKE\",/*dm*/
\n//   fieldName: \"name\",/*dm*/
\n//   value: $.shareData.var_findRelatedForm.name,/*dm*/
\n// });/*dm*/
\n$.functions.fun_loadTableData(modelName, viewCode);/*dm*/
\n/*dm*/
\n}}",
																	"type":"function"
																}
															]
														}
													}
												},
												"id":"ModelCondition-xdYcSSG2",
												"componentName":"ModelCondition",
												"version":"1.8.3",
												"props":{
													"mode":"simple",
													"footerConfig":"${{/*dm*/
\n  \"span\": 12/*dm*/
\n}}",
													"ref":"condition",
													"queryConditions":"${$.shareData.var_currentSearchCondition.conditionsValue}",
													"modelData":"${$.global.shareData.modelsInfoData?.fields[$.shareData.var_findRelationInfo.modelName]}",
													"seniorFlag":false,
													"currentPlan":"${JSON.stringify($.shareData.var_currentSearchCondition)}",
													"showSearch":"${true}",
													"searchMode":"${\"base\"}",
													"displaySwitch":"${$.shareData.var_switchFlag}",
													"slot":"search"
												},
												"attrs":{
													"style":{
														"border-radius":"8px",
														"margin-bottom":"8px"
													}
												}
											}
										],
										"componentName":"Page",
										"id":"Page-s8EQpdE2",
										"title":"被关联模型页面盒子组件",
										"version":"1.8.3",
										"props":{
											"show-table":true,
											"show-search":true,
											"searchMethod":"search"
										}
									},
									{
										"componentType":"custom",
										"componentName":"Import",
										"id":"DivWrapper-TZ5TiJFM",
										"attrs":{
											"name":"table",
											"style":{}
										},
										"props":{
											"modelName":"${$.shareData.var_findRelationInfo.modelName}",
											"modelText":"${$.shareData.var_findRelationInfo.modelText}",
											"fieldName":"${$.shareData.var_findRelationInfo.fieldName}",
											"enableOperateColumnu":"${false}",
											"tableStyle":"${{/*dm*/
\n  \"height\": '320px'/*dm*/
\n}}",
											"viewCode":"${$.shareData.var_findRelationInfo.viewCode}"
										}
									}
								],
								"componentName":"DivWrapper",
								"id":"DivWrapper-7pCrhbaM",
								"title":"弹框body",
								"props":{
									"name":"DivWrapper",
									"class":"relationBox"
								},
								"attrs":{
									"style":{
										"height":"100%"
									}
								}
							}
						],
						"componentName":"VIf",
						"id":"VIf-aDxh6Gs3",
						"props":{
							"conditions":"${[$.shareData.var_findRelationInfo.visible]}"
						}
					},
					{
						"componentType":"custom",
						"children":[
							{
								"componentType":"custom",
								"action":{
									"fire":{
										"click":{
											"actions":[
												{
													"handler":"${function handler(){$.shareData.var_findRelationInfoAction({/*dm*/
\n  visible: false/*dm*/
\n})/*dm*/
\n$.shareData.var_findRelatedForm = {};}}",
													"type":"function"
												}
											]
										}
									}
								},
								"componentName":"Button",
								"id":"Button-ndASPahA",
								"attrs":{},
								"props":{
									"size":"small",
									"children":"取消"
								}
							},
							{
								"componentType":"custom",
								"action":{
									"fire":{
										"click":{
											"actions":[
												{
													"handler":"${function Fun24728(){const { modelName, fieldName, viewCode } = $.shareData.var_findRelationInfo;/*dm*/
\nconst tableInfo = $.shareData.var_tables[viewCode]/*dm*/
\nconst newActionValue = tableInfo.singleSelection/*dm*/
\n/*dm*/
\nif (newActionValue) {/*dm*/
\n/*dm*/
\n/*dm*/
\n  // 当前字段关联规则内容请求/*dm*/
\n  let obj = tableInfo.tableList.find(item => item.id === newActionValue);/*dm*/
\n/*dm*/
\n  await $.shareData.api_getReferencedInfoAction({/*dm*/
\n    modelName: $.shareData.var_setting.modelName,/*dm*/
\n    fieldName: fieldName,/*dm*/
\n    id: obj.id/*dm*/
\n  })/*dm*/
\n/*dm*/
\n  let referenced = $.shareData.api_getReferencedInfo;/*dm*/
\n/*dm*/
\n  if (referenced?.data) {/*dm*/
\n    for (let key in referenced.data) {/*dm*/
\n      this.$set($.shareData.var_saveForm, key, referenced?.data[key])/*dm*/
\n    }/*dm*/
\n  }/*dm*/
\n/*dm*/
\n  console.log($.shareData.api_getReferencedInfo, '$.shareData.api_getReferencedInfo')/*dm*/
\n  /*dm*/
\n  // 使用$set追加响应式/*dm*/
\n  this.$set($.shareData.var_saveForm, fieldName, { ...obj })/*dm*/
\n/*dm*/
\n  $.shareData.var_findRelationInfoAction({/*dm*/
\n    visible: false/*dm*/
\n  })/*dm*/
\n  $.shareData.var_findRelatedForm = {};/*dm*/
\n/*dm*/
\n/*dm*/
\n} else {/*dm*/
\n  this.$message.error('请点击表格行选择数据!');/*dm*/
\n}/*dm*/
\n}}",
													"type":"function"
												}
											]
										}
									}
								},
								"componentName":"Button",
								"id":"Button-4WPepGCR",
								"attrs":{},
								"props":{
									"size":"small",
									"children":"确定",
									"type":"primary"
								},
								"chosen":false
							}
						],
						"componentName":"DivWrapper",
						"id":"DivWrapper-7JzQBHtD",
						"props":{
							"name":"footer",
							"slot":"footer"
						}
					}
				],
				"action":{
					"fire":{
						"close":{
							"actions":[
								{
									"handler":"${function Fun50778(){$.shareData.var_findRelationInfoAction({/*dm*/
\n  visible: false/*dm*/
\n})}}",
									"type":"function"
								}
							]
						}
					}
				},
				"componentName":"Dialog",
				"id":"Dialog-DyyBRr6W",
				"title":"被关联模型",
				"key":"beiguanlianmoxing71321save",
				"props":{
					"modal-append-to-body":"${false}",
					"append-to-body":"${true}",
					"destroy-on-close":false,
					"visible":"${$.shareData.var_findRelationInfo.visible}",
					"width":"${\"960px\"}",
					"title":"${() => {/*dm*/
\n/*dm*/
\n  return `请选择【${$.shareData.var_findRelationInfo.modelText}】`/*dm*/
\n}}",
					"close-on-click-modal":false,
					"close-on-press-escape":false,
					"custom-class":"recordsDialogBox"
				}
			}
		],
		"functions":[
			{
				"default":"${function preUpload(file) {/*dm*/
\n        return $.shareData.api_getPreUploadUrlAction({/*dm*/
\n          name: file.name/*dm*/
\n        }).catch(e=>{/*dm*/
\n          console.log('e', e)/*dm*/
\n        })/*dm*/
\n      }}",
				"description":"获取页面id",
				"key":"fun_preUpload"
			},
			{
				"default":"${function getQueryValue(queryName) {/*dm*/
\n  const query = $.bom.page.query() || {};/*dm*/
\n  return query[queryName] || null;/*dm*/
\n}}",
				"description":"获取页面id",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_getQueryValue"
			},
			{
				"default":"${function fun_initFormView(modelName) {/*dm*/
\n/*dm*/
\n  await $.shareData.api_getFormPageInfoAction({/*dm*/
\n    modelName: $.shareData.var_setting.modelName,/*dm*/
\n    pageKey: $.shareData.var_setting.pageKey/*dm*/
\n  })/*dm*/
\n/*dm*/
\n  const initListColumns = (relatedModelName, viewInfoList) => {/*dm*/
\n    let allFieldList = []/*dm*/
\n/*dm*/
\n    viewInfoList.map((value) => {/*dm*/
\n/*dm*/
\n      const fieldInfo = $.global.shareData.modelsInfoData.fieldsMap[relatedModelName]?.get(value.fieldName)/*dm*/
\n/*dm*/
\n      const newData = {/*dm*/
\n        ...value,/*dm*/
\n        name: value.fieldName,/*dm*/
\n        label: fieldInfo.fieldText,/*dm*/
\n        defaultSelectValue: value.defaultSelect,/*dm*/
\n        modelField: fieldInfo,/*dm*/
\n        index: value.showIndex,/*dm*/
\n        fieldWidth: value.displayWidth/*dm*/
\n      }/*dm*/
\n/*dm*/
\n      if (value.isEnable && !value.relatedFieldList?.length) {/*dm*/
\n        allFieldList.push(newData);/*dm*/
\n      }/*dm*/
\n/*dm*/
\n      if (value.relatedFieldList?.length > 0) {/*dm*/
\n        const field = fieldInfo;/*dm*/
\n        value.relatedFieldList.map(item => {/*dm*/
\n          const relatedField = field.relatedModel.modelFields.find(it1 => it1.fieldName === item.relatedFieldName)/*dm*/
\n          // 再添加循环的数据/*dm*/
\n          if (item.isEnable) {/*dm*/
\n            allFieldList.push({/*dm*/
\n              ...item,/*dm*/
\n              name: item.fieldName,/*dm*/
\n              fieldWidth: item.displayWidth,/*dm*/
\n              fieldName: `${item.fieldName}.${item.relatedFieldName}`,/*dm*/
\n              fieldText: relatedField.fieldText,/*dm*/
\n              label: relatedField.fieldText,/*dm*/
\n              businessType: field.businessType,/*dm*/
\n              index: item.showIndex,/*dm*/
\n              modelField: {/*dm*/
\n                ...field,/*dm*/
\n                calculationFieldName: item.relatedFieldName/*dm*/
\n              }/*dm*/
\n            })/*dm*/
\n          }/*dm*/
\n        })/*dm*/
\n      }/*dm*/
\n/*dm*/
\n      return/*dm*/
\n/*dm*/
\n    })/*dm*/
\n    allFieldList = allFieldList.sort((a, b) => a.showIndex - b.showIndex);/*dm*/
\n/*dm*/
\n    return allFieldList;/*dm*/
\n  }/*dm*/
\n/*dm*/
\n  const models = new Set();/*dm*/
\n  // 处理分组中的子模型table视图/*dm*/
\n  $.shareData.api_getFormPageInfo.viewGroupList.forEach(({ groupName, groupCode, groupInfoList, viewRelatedList }) => {/*dm*/
\n    if (viewRelatedList.length) {/*dm*/
\n      viewRelatedList.forEach(({ relatedModelName, relatedListViewCode }) => {/*dm*/
\n        models.add(relatedModelName)/*dm*/
\n      })/*dm*/
\n    }/*dm*/
\n  });/*dm*/
\n  // 处理表单中关联关系的table视图/*dm*/
\n  let hasFindRelation = false, findRelationViewCode = {};/*dm*/
\n  $.shareData.api_getFormPageInfo.viewFindRelationships.forEach(({ relatedListViewCode, fieldName, relatedModelName }) => {/*dm*/
\n    models.add(relatedModelName)/*dm*/
\n    hasFindRelation = true;/*dm*/
\n  });/*dm*/
\n  // 统一调用模型接口/*dm*/
\n  if (models.size) {/*dm*/
\n    await Promise.all([...models].map(modelName => $.global.functions.getModelDictionaryInfo(modelName)));/*dm*/
\n  }/*dm*/
\n  // 处理关联字段/*dm*/
\n  if (hasFindRelation) {/*dm*/
\n    $.shareData.api_getFormPageInfo.viewFindRelationships.forEach(({ listViews: { viewInfoList, viewSortList }, relatedListViewCode, fieldName, relatedModelName, editorViewCode }) => {/*dm*/
\n      const viewCode = relatedListViewCode + fieldName;/*dm*/
\n      findRelationViewCode[fieldName] = viewCode/*dm*/
\n      $.functions.fun_initTableView({/*dm*/
\n        modelText: '',/*dm*/
\n        modelName: relatedModelName,/*dm*/
\n        viewCode,/*dm*/
\n        setting: {/*dm*/
\n          showPagination: true,/*dm*/
\n          showRadio: true,/*dm*/
\n          showNo: false/*dm*/
\n        },/*dm*/
\n        querySorts: viewSortList.map(({ fieldName, sortOrder }) => ({ fieldName, desc: sortOrder == 'asc' ? false : true }))/*dm*/
\n/*dm*/
\n      })/*dm*/
\n      this.$set($.shareData.var_tables[viewCode], 'columns', initListColumns(relatedModelName, viewInfoList))/*dm*/
\n    });/*dm*/
\n  }/*dm*/
\n/*dm*/
\n/*dm*/
\n  // 处理分组数据/*dm*/
\n  const groupData = $.shareData.api_getFormPageInfo.viewGroupList.map(({ id, groupName, groupCode, groupInfoList, viewRelatedList }) => {/*dm*/
\n    return {/*dm*/
\n      groupName,/*dm*/
\n      groupCode,/*dm*/
\n      id,/*dm*/
\n      showDrawer: true || $.shareData.var_setting.group?.[item.groupCode]?.enableTitle,/*dm*/
\n      subModelList: viewRelatedList.map(({ relatedShowName, relatedModelName, relatedListViewCode, id, relatedFieldName, listViews: { viewInfoList, viewSortList } }) => {/*dm*/
\n        $.functions.fun_initTableView({/*dm*/
\n          modelText: relatedShowName,/*dm*/
\n          modelName: relatedModelName,/*dm*/
\n          viewCode: relatedListViewCode,/*dm*/
\n          querySorts: viewSortList.map(({ fieldName, sortOrder }) => ({ fieldName, desc: sortOrder == 'asc' ? false : true }))/*dm*/
\n        })/*dm*/
\n        this.$set($.shareData.var_tables[relatedListViewCode], 'columns', initListColumns(relatedModelName, viewInfoList))/*dm*/
\n        return {/*dm*/
\n          modelText: relatedShowName,/*dm*/
\n          modelName: relatedModelName,/*dm*/
\n          fieldName: relatedFieldName,/*dm*/
\n          relatedListViewCode,/*dm*/
\n          id/*dm*/
\n/*dm*/
\n        }/*dm*/
\n      }),/*dm*/
\n      fields: !groupInfoList ? [] : groupInfoList.reduce((arr, { fieldName, modelFieldPermission }) => {/*dm*/
\n        const field = $.global.shareData.modelsInfoData.fieldsMap[modelName]?.get(fieldName);/*dm*/
\n/*dm*/
\n        field.preUpload = $.functions.fun_preUpload;/*dm*/
\n        if (field.syncFieldData) {/*dm*/
\n          field.disabled = !!field?.syncFieldData/*dm*/
\n        }/*dm*/
\n        // 自动编号、引用字段、公式、汇总字段类型 不展示/*dm*/
\n        const hiddenFields = ['autoNumber', 'formula', 'summary', 'referenced']/*dm*/
\n/*dm*/
\n        if (!hiddenFields.includes(field.businessType) && !field.isReferencedField) {/*dm*/
\n/*dm*/
\n          arr.push({/*dm*/
\n            fieldName,/*dm*/
\n            modelFieldPermission: modelFieldPermission,/*dm*/
\n            field: {/*dm*/
\n              ...(findRelationViewCode[fieldName] ? { viewCode: findRelationViewCode[fieldName] } : {}),/*dm*/
\n              ...field,/*dm*/
\n            }/*dm*/
\n          })/*dm*/
\n/*dm*/
\n          // 新建页过滤掉modelFieldPermission不为2(编辑权限)的/*dm*/
\n          if (!$.shareData.var_pageInfo.isEdit) {/*dm*/
\n            arr = arr.filter(v => v.modelFieldPermission == 2)/*dm*/
\n          }/*dm*/
\n        }/*dm*/
\n        return arr;/*dm*/
\n/*dm*/
\n      }, [])/*dm*/
\n    };/*dm*/
\n  })/*dm*/
\n  $.shareData.var_viewDetailListAction(groupData);/*dm*/
\n}}",
				"description":"初始化视图",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_initFormView"
			},
			{
				"default":"${function getFormEdit(detailId, formData = {}) {/*dm*/
\n  if (detailId) {/*dm*/
\n    let format = await $.functions.fun_formFormat($.shareData.var_setting.modelName);/*dm*/
\n    if (detailId.indexOf('subModel') > -1) {/*dm*/
\n      // 回显子模型新增的数据/*dm*/
\n      const pageParams = $.bom.page.params()?.data;/*dm*/
\n      const obj = pageParams.formData;/*dm*/
\n      for (let key in obj) {/*dm*/
\n        this.$set($.shareData.var_saveForm, key, obj[key])/*dm*/
\n      }/*dm*/
\n      return;/*dm*/
\n    }/*dm*/
\n    await $.shareData.api_getInfoAction({ id: detailId, modelName: $.shareData.var_setting.modelName });/*dm*/
\n    let res = $.shareData.api_getInfo.data;/*dm*/
\n    const obj = format.input(res);/*dm*/
\n    const mergeData = {/*dm*/
\n      ...obj,/*dm*/
\n      ...formData/*dm*/
\n    }/*dm*/
\n    for (let key in obj) {/*dm*/
\n      this.$set($.shareData.var_saveForm, key, mergeData[key])/*dm*/
\n    }/*dm*/
\n    // 流程状态处理/*dm*/
\n    if ($.shareData.var_pageInfo.wfOpen) {/*dm*/
\n      // 待提交 审批不通过显示按钮/*dm*/
\n      $.shareData.var_pageInfo.wfStatus = ['1', '4'].includes($.shareData.var_saveForm.approval_status);/*dm*/
\n    }/*dm*/
\n    // console.log(2222, $.shareData.var_saveForm);/*dm*/
\n  } else {/*dm*/
\n    $.shareData.api_getDefaultDataAction({ modelName: $.shareData.var_setting.modelName }).then(async res => {/*dm*/
\n      let format = await $.functions.fun_formFormat($.shareData.var_setting.modelName);/*dm*/
\n      const obj = format.input(res.data);/*dm*/
\n      for (let key in obj) {/*dm*/
\n        this.$set($.shareData.var_saveForm, key, obj[key])/*dm*/
\n      }/*dm*/
\n      // 流程状态处理/*dm*/
\n      if ($.shareData.var_pageInfo.wfOpen) {/*dm*/
\n        // 待提交 审批不通过显示按钮/*dm*/
\n        $.shareData.var_pageInfo.wfStatus = ['1', '4'].includes($.shareData.var_saveForm.approval_status);/*dm*/
\n      }/*dm*/
\n    });/*dm*/
\n  }/*dm*/
\n}}",
				"description":"获取页面信息",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_getFormEdit"
			},
			{
				"default":"${function fun_initSubModelPage() {/*dm*/
\n  // 当在子模型下页面处理/*dm*/
\n  const addPageParams = $.bom.page.params()?.data;/*dm*/
\n  if (addPageParams?.pageType == 'subModel') {/*dm*/
\n    $.shareData.var_pageInfoAction({/*dm*/
\n      showHeader: false,/*dm*/
\n    })/*dm*/
\n/*dm*/
\n    const { fieldName, modelText, modelName } = addPageParams.subModel;/*dm*/
\n    $.shareData.var_subModelInfoAction({/*dm*/
\n      fieldName,/*dm*/
\n      modelText,/*dm*/
\n      modelName,/*dm*/
\n      fieldId: `${modelName}|{fieldName}`/*dm*/
\n    });/*dm*/
\n    // 如果是子模型表单， 则补充默认数据/*dm*/
\n/*dm*/
\n    $.shareData.var_saveForm[fieldName] = modelText;/*dm*/
\n  }/*dm*/
\n}}",
				"description":"",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_initSubModelPage"
			},
			{
				"default":"${function fun_initDraftInfo() {/*dm*/
\n  // 获取模型信息/*dm*/
\n  // await $.shareData.modelMetaAction();/*dm*/
\n  // if ($.shareData.var_subModelInfo.fieldName) {/*dm*/
\n  //   return;/*dm*/
\n  // }/*dm*/
\n  const draftId = $.functions.fun_getQueryValue('draftId');/*dm*/
\n  if (draftId) {/*dm*/
\n    await $.shareData.api_getCommonDraftInfoAction({ id: draftId });/*dm*/
\n    const draftInfo = $.shareData.api_getCommonDraftInfo;/*dm*/
\n    const formInfo = draftInfo.data;/*dm*/
\n    if (draftInfo.code == 200) {/*dm*/
\n      let format = await $.functions.fun_formFormat($.shareData.var_setting.modelName);/*dm*/
\n      this.$set($.shareData, 'var_saveForm', { ...format.input(formInfo) });/*dm*/
\n      // $.shareData.var_saveFormAction({ ...format.input(formInfo) });/*dm*/
\n    } else {/*dm*/
\n      this.$message.error(draftInfo.msg);/*dm*/
\n    }/*dm*/
\n  }/*dm*/
\n}}",
				"description":"获取暂存信息",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_initDraftInfo"
			},
			{
				"default":"${function findRelatedQueryRestForm() {/*dm*/
\n  const query = $.bom.page.params()?.data || $.bom.page.query() || {};/*dm*/
\n  const {/*dm*/
\n    formId, fieldName, modelName/*dm*/
\n  } = query;/*dm*/
\n  if (formId && fieldName && modelName) {/*dm*/
\n    await $.shareData.api_getInfoAction({ modelName: modelName, id: formId });/*dm*/
\n    let format = await $.functions.fun_formFormat($.shareData.var_setting.modelName);/*dm*/
\n    $.shareData.var_saveFormAction({ [fieldName]: { ...format.input($.shareData.api_getInfo.data) } });/*dm*/
\n  }/*dm*/
\n}}",
				"description":"获取URL的id和name",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_findRelatedQueryRestForm"
			},
			{
				"default":"${function formFormat(modelName) {/*dm*/
\n  const copyId = $.functions.fun_getQueryValue('copyId')/*dm*/
\n  if (copyId) {/*dm*/
\n    await $.shareData.api_getInfoAction({ id: copyId, modelName: $.shareData.var_setting.modelName });/*dm*/
\n    let res = $.shareData.api_getInfo.data;/*dm*/
\n    let format = await $.global.functions.modelValueFormat($.shareData.var_setting.modelName);/*dm*/
\n    this.$set($.shareData, 'var_saveForm', { ...format.input(res) })/*dm*/
\n  }/*dm*/
\n}}",
				"description":"格式化form数据",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_initCopy"
			},
			{
				"default":"${function formFormat(modelName){/*dm*/
\n  return $.global.functions.modelValueFormat(modelName, \"edit\")/*dm*/
\n}}",
				"description":"格式化form数据",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_formFormat"
			},
			{
				"default":"${function fun_loadTableData(modelName, viewCode, searchQuery = {}) {/*dm*/
\n  this.$set($.shareData.var_tables[viewCode], 'loading', true);/*dm*/
\n/*dm*/
\n  const query = {/*dm*/
\n    modelName,/*dm*/
\n    ...($.shareData.var_tables[viewCode]?.searchQuery || {}),/*dm*/
\n    ...($.shareData.var_tables[viewCode]?.params || {}),/*dm*/
\n    ...searchQuery,/*dm*/
\n    attributes: $.shareData.var_tables[viewCode]?.columns?.map(item => item.fieldName) || null/*dm*/
\n  }/*dm*/
\n  // 流程数据条件过滤/*dm*/
\n  const modelInfo = await $.global.functions.getModelDictionaryInfo(modelName);/*dm*/
\n  if (modelInfo?.wfOpen) {/*dm*/
\n    query.queryConditions.push({/*dm*/
\n      \"fieldName\": \"approval_status\",/*dm*/
\n      \"valueType\": 0,/*dm*/
\n      \"conditionType\": \"EQ\",/*dm*/
\n      \"value\": \"3\"/*dm*/
\n    })/*dm*/
\n  }/*dm*/
\n  $.shareData.api_getListAction({/*dm*/
\n    ...query/*dm*/
\n  }).then(async item => {/*dm*/
\n    this.$set($.shareData.var_tables[viewCode], 'loading', false);/*dm*/
\n  /*dm*/
\n    let transform = await $.global.functions.modelValueFormat(modelName)/*dm*/
\n    if ($.shareData.api_getList.code !== 200) {/*dm*/
\n     // this.$message.error('')/*dm*/
\n    } else {/*dm*/
\n      this.$set($.shareData.var_tables[viewCode], 'tableList', $.shareData.api_getList.data.records.map(item => {/*dm*/
\n        let data = transform.input(item);/*dm*/
\n        return data/*dm*/
\n      }))/*dm*/
\n    }/*dm*/
\n    this.$set($.shareData.var_tables[viewCode]?.params, 'totalSize', $.shareData.api_getList.data.totalSize)/*dm*/
\n    this.$refs[viewCode]?.doLayout();/*dm*/
\n  }).catch(() => {/*dm*/
\n    this.$set($.shareData.var_tables[viewCode], 'loading', false);/*dm*/
\n  })/*dm*/
\n}}",
				"description":"加载表格数据",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_loadTableData"
			},
			{
				"default":"${function fun_genTableId(viewCode, modelName){/*dm*/
\n  const userData = $.bom.getHostData('userInfo')/*dm*/
\n  const genId = `${viewCode}_${userData.personId || userData.userName || userData.pin}_${modelName}`/*dm*/
\n  return genId/*dm*/
\n}}",
				"description":"获取表格唯一id",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_genTableId"
			},
			{
				"default":"${function fun_getOperatePath(modelName, operateName){/*dm*/
\n/*dm*/
\n  return  `/ms${$.shareData.var_setting.pagePath}/${modelName}/${operateName}`;/*dm*/
\n/*dm*/
\n}}",
				"description":"获取主子表操作路径",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_getOperatePath"
			},
			{
				"default":"${function fun_initTableView({viewCode, modelName, modelText, setting, querySorts = []}) {/*dm*/
\n  const config = {/*dm*/
\n    modelName,/*dm*/
\n    modelText,/*dm*/
\n    viewCode,/*dm*/
\n    setting: {/*dm*/
\n      'showPagination': false,/*dm*/
\n      'showRadio': false,/*dm*/
\n      'paginationMode': 'complete',/*dm*/
\n      'paginationAlign': 'right',/*dm*/
\n      'pageSizes': [10, 20, 30, 40, 50],/*dm*/
\n      'showHeader': true,/*dm*/
\n      'stripe': false,/*dm*/
\n      'showNo': true,/*dm*/
\n      'selectRowType': 'single',/*dm*/
\n      'border': false,/*dm*/
\n      'mode': 'normal',/*dm*/
\n      'batchDelFlag': false,/*dm*/
\n      'addFlag': true,/*dm*/
\n      'exportDataFlag': false,/*dm*/
\n      'headerEllipsis': true,/*dm*/
\n      'cellEllipsis': true,/*dm*/
\n      'textAlign': 'left',/*dm*/
\n      'showDetail': false,/*dm*/
\n      'showDel': true,/*dm*/
\n      'showEdit': true,/*dm*/
\n      'columnShowIcon': false,/*dm*/
\n      'columnShowText': true,/*dm*/
\n      'resizeColumn': true,/*dm*/
\n      ...setting/*dm*/
\n    },/*dm*/
\n    loading: false,/*dm*/
\n    params: {/*dm*/
\n      pageNum: 1,/*dm*/
\n      pageSize: Number(localStorage.getItem(`var_basePageParams_${viewCode}`)) || 10,/*dm*/
\n      totalPage:0,/*dm*/
\n      totalSize:0/*dm*/
\n    },/*dm*/
\n    searchQuery: {/*dm*/
\n      querySorts,/*dm*/
\n      queryConditions: [],/*dm*/
\n      combinationRule: ''/*dm*/
\n    },/*dm*/
\n    tableData: {/*dm*/
\n      addDatas: [],/*dm*/
\n      updateDatas: [],/*dm*/
\n      deleteIds: [],/*dm*/
\n    },/*dm*/
\n    singleSelection: '',/*dm*/
\n    multipleSelection: [],/*dm*/
\n    columns:  [],/*dm*/
\n    tableList: [],/*dm*/
\n/*dm*/
\n  }/*dm*/
\n  this.$set($.shareData.var_tables, viewCode, config);/*dm*/
\n/*dm*/
\n}}",
				"description":"初始化table",
				"global":false,
				"type":"function",
				"title":"",
				"key":"fun_initTableView"
			}
		],
		"shareData":[
			{
				"default":"{\"modelName\":\"ipms_business_organization\",\"pageKey\":\"840367756792066049\",\"pagePath\":\"/ipms_business_organization/form1\",\"enableTitle\":false,\"columnTotal\":2,\"group\":{},\"table\":{\"default\":{\"showPlanView\":true,\"edit\":true,\"detail\":false,\"delete\":true,\"copy\":false}},\"detailPath\":\"/ipms_business_organization/detail1\",\"formPath\":\"/ipms_business_organization/form1\",\"listPath\":\"/ipms_business_organization/list1\"}",
				"key":"var_setting"
			},
			{
				"default":"{/*dm*/
\n  \"openDraft\": false,/*dm*/
\n  \"typeTitle\": \"新建\",/*dm*/
\n  \"isEdit\": false,/*dm*/
\n  \"title\": \"\",/*dm*/
\n  \"detailId\": \"\",/*dm*/
\n  \"showHeader\": true,/*dm*/
\n  \"isSubModel\": false,/*dm*/
\n  \"modelText\": \"\",/*dm*/
\n  \"wfOpen\": false,/*dm*/
\n  \"wfStatus\": false/*dm*/
\n}",
				"description":"页面信息",
				"global":false,
				"key":"var_pageInfo"
			},
			{
				"default":"{}",
				"description":"表单数据",
				"key":"var_saveForm"
			},
			{
				"default":false,
				"description":"表单是否提交中",
				"key":"var_saveLoading"
			},
			{
				"default":"[]",
				"description":"",
				"key":"var_viewDetailList"
			},
			{
				"default":"{ name: '' }",
				"description":"关联模型form",
				"key":"var_findRelatedForm"
			},
			{
				"default":"{/*dm*/
\n        modelName: \"\",/*dm*/
\n        fieldName: \"\"/*dm*/
\n      }",
				"key":"var_subModelInfo"
			},
			{
				"default":"{/*dm*/
\n  /*dm*/
\n}",
				"description":"所有table信息汇总",
				"global":false,
				"key":"var_tables"
			},
			{
				"default":"{/*dm*/
\n  \"viewCode\": \"\",/*dm*/
\n  \"modelName\": \"\",/*dm*/
\n  \"modelText\": \"\",/*dm*/
\n  \"fieldName\": \"\",/*dm*/
\n  \"visible\": false/*dm*/
\n}",
				"description":"查找关系信息",
				"global":false,
				"key":"var_findRelationInfo"
			},
			{
				"default":"{/*dm*/
\n  \"conditionsValue\": [/*dm*/
\n    {/*dm*/
\n      \"conditionType\": \"LIKE\",/*dm*/
\n      \"fieldName\": \"name\",/*dm*/
\n      \"value\": \"\"/*dm*/
\n    }/*dm*/
\n  ],/*dm*/
\n  \"standardQueryConditions\": [/*dm*/
\n    {/*dm*/
\n      \"conditionType\": \"LIKE\",/*dm*/
\n      \"fieldName\": \"name\",/*dm*/
\n      \"value\": \"\"/*dm*/
\n    }/*dm*/
\n  ],/*dm*/
\n  \"standardCombinationRule\": \"\"/*dm*/
\n}",
				"key":"var_currentSearchCondition"
			},
			{
				"default":"[]",
				"description":"禁止操作的字段",
				"key":"var_disabledFormField"
			}
		],
		"action":{
			"lifecycle":{
				"mounted":{
					"arguments":[
						""
					],
					"actions":[
						{
							"handler":"${function Fun1453(){const bomPage = $.bom?.page;/*dm*/
\nconst pageParams = bomPage?.params()?.data;/*dm*/
\nconst pageQuery = bomPage?.query();/*dm*/
\n// 正常编辑页判断/*dm*/
\nlet detailId = pageParams?.detailId || pageQuery?.detailId;/*dm*/
\n/*dm*/
\nlet isSubModel = false;/*dm*/
\n/*dm*/
\nconst defaultVarSaveForm = pageParams?.defaultVarSaveForm;/*dm*/
\nif (defaultVarSaveForm) {/*dm*/
\n  detailId = pageParams?.detailId/*dm*/
\n  $.shareData.var_saveFormAction(defaultVarSaveForm)/*dm*/
\n}/*dm*/
\n/*dm*/
\nconst disabledFormField = pageParams?.disabledFormField;/*dm*/
\n/*dm*/
\nif (disabledFormField) {/*dm*/
\n  $.shareData.var_disabledFormFieldAction(disabledFormField)/*dm*/
\n}/*dm*/
\n/*dm*/
\n// 子表编辑判断/*dm*/
\nif (pageParams?.pageType == 'subModel') {/*dm*/
\n  detailId = pageParams?.detailId;/*dm*/
\n  isSubModel = true;/*dm*/
\n}/*dm*/
\n$.shareData.var_pageInfoAction({/*dm*/
\n  isSubModel/*dm*/
\n})/*dm*/
\nif (detailId) {/*dm*/
\n  $.shareData.var_pageInfoAction({/*dm*/
\n    detailId,/*dm*/
\n    isEdit: !!detailId,/*dm*/
\n    typeTitle: \"编辑\"/*dm*/
\n  })/*dm*/
\n}/*dm*/
\n/*dm*/
\n// 初始化模型/*dm*/
\nconst res = await $.global.functions.getModelDictionaryInfo($.shareData.var_setting.modelName);/*dm*/
\n$.shareData.var_pageInfoAction({/*dm*/
\n  wfOpen: res?.wfOpen,/*dm*/
\n  wfStatus: true,/*dm*/
\n  openDraft: res.draft,/*dm*/
\n  modelText: res.modelText,/*dm*/
\n  title: $.shareData.var_pageInfo.typeTitle + res.modelText/*dm*/
\n})/*dm*/
\n// 设置标题/*dm*/
\n$.bom.getHostData(\"setTitle\", $.shareData.var_pageInfo.modelText + \"-\" + $.shareData.var_pageInfo.typeTitle);/*dm*/
\n/*dm*/
\n// 该页面是子模型时，需要初始化/*dm*/
\nif ($.shareData.var_pageInfo.isSubModel) {/*dm*/
\n  $.functions.fun_initSubModelPage()/*dm*/
\n}/*dm*/
\n/*dm*/
\n// 编辑页/*dm*/
\nif ($.shareData.var_pageInfo.isEdit) {/*dm*/
\n  await $.functions.fun_getFormEdit(detailId, pageParams?.formData);/*dm*/
\n}/*dm*/
\n/*dm*/
\n// 详情页面或者编辑页面，新增时需要将查找关系数据处理一下/*dm*/
\nawait $.functions.fun_findRelatedQueryRestForm();/*dm*/
\n/*dm*/
\nawait $.functions.fun_initFormView($.shareData.var_setting.modelName);/*dm*/
\n/*dm*/
\nif (!$.shareData.var_pageInfo.isSubModel && !$.shareData.var_pageInfo.isEdit) {/*dm*/
\n  // 支持暂存处理/*dm*/
\n  await $.functions.fun_initDraftInfo();/*dm*/
\n  // 复制处理/*dm*/
\n  await $.functions.fun_initCopy()/*dm*/
\n  return;/*dm*/
\n}/*dm*/
\n/*dm*/
\nif (!$.shareData.var_pageInfo.isEdit) {/*dm*/
\n  return;/*dm*/
\n}/*dm*/
\n/*dm*/
\n/*dm*/
\n// await $.functions.fun_initFormView($.shareData.var_setting.modelName);/*dm*/
\n// 加载子模型数据/*dm*/
\nconst views = $.shareData.var_viewDetailList;/*dm*/
\nviews.map(view => {/*dm*/
\n  view.subModelList.map(model => {/*dm*/
\n    const tableKey = model.relatedListViewCode;/*dm*/
\n    let tableParams = {};/*dm*/
\n    if (detailId) {/*dm*/
\n      tableParams = {/*dm*/
\n        \"pageNum\": 1,/*dm*/
\n        \"pageSize\": 100,/*dm*/
\n        queryConditions: [{/*dm*/
\n          fieldName: model.fieldName,/*dm*/
\n          conditionType: \"EQ\",/*dm*/
\n          value: detailId/*dm*/
\n        }]/*dm*/
\n      }/*dm*/
\n    }/*dm*/
\n    $.functions.fun_loadTableData(model.modelName, tableKey, tableParams);/*dm*/
\n  });/*dm*/
\n});/*dm*/
\n/*dm*/
\n/*dm*/
\n}}",
							"type":"function",
							"tags":[]
						}
					]
				}
			},
			"on":{
				"changeSetting":{
					"arguments":[
						"setting"
					],
					"actions":[
						{
							"handler":"${function Handler(){$.shareData.var_settingAction(setting)}}",
							"type":"function"
						}
					]
				}
			}
		},
		"id":"840367756792066049",
		"styleCode":".sub-model-root {\r/*dm*/
\n    .el-table__empty-text > div {\r/*dm*/
\n      min-height: unset !important;\r/*dm*/
\n      /* padding: 16px 0 0 !important; */\r/*dm*/
\n    }\r/*dm*/
\n  }\r/*dm*/
\n  .sub-model-dialog {\r/*dm*/
\n    .el-dialog__body {\r/*dm*/
\n      height: 70vh;\r/*dm*/
\n      overflow: hidden;\r/*dm*/
\n      padding: 0 !important;\r/*dm*/
\n      .container {\r/*dm*/
\n        height: 100%;\r/*dm*/
\n      }\r/*dm*/
\n    }\r/*dm*/
\n    .editPage {\r/*dm*/
\n      height: 100%;\r/*dm*/
\n    }\r/*dm*/
\n  }\r/*dm*/
\n\r/*dm*/
\n  .editPage {\r/*dm*/
\n    padding:16px 24px 0px;\r/*dm*/
\n    position:relative;\r/*dm*/
\n    box-sizing:border-box;\r/*dm*/
\n    height:100%;\r/*dm*/
\n    display: flex;\r/*dm*/
\n    flex-direction: column;\r/*dm*/
\n  }\r/*dm*/
\n\r/*dm*/
\n  .editPage .el-form-item--small .el-form-item__content { line-height: 1; min-height: 32px }\r/*dm*/
\n  .editPage .editPageForm .el-row { position: static; margin: 0 !important; display:block}\r/*dm*/
\n  .editPage .editPageFooter { flex-shrink: 0;height: 64px; background: #fff; padding: 0 24px !important; display: flex; align-items: center; justify-content: flex-end;border-top: 1px solid #EBEEF5;}\r/*dm*/
\n  .tableScolle .el-table__body-wrapper {\r/*dm*/
\n    /* height: 81.5% !important; */\r/*dm*/
\n  }\r/*dm*/
\n  .editPage .recordsDialogBox .el-dialog__body {\r/*dm*/
\n    height: 460px;\r/*dm*/
\n    max-height: 460px;\r/*dm*/
\n  }\r/*dm*/
\n  .editPage .el-collapse-item__content {\r/*dm*/
\n    padding: 16px 8px;\r/*dm*/
\n  }\r/*dm*/
\n  .el-collapse-item__header {\r/*dm*/
\n    width: max-content;\r/*dm*/
\n  }\r/*dm*/
\n\r/*dm*/
\n    .recordsDialogBox .el-dialog__body {\r/*dm*/
\n    padding-bottom: 0px;\r/*dm*/
\n    max-height: none !important;\r/*dm*/
\n  }\r/*dm*/
\n\r/*dm*/
\n  .joy-condition .searchBase {\r/*dm*/
\n    border-radius: 8px;\r/*dm*/
\n  }",
		"apiConfigs":[
			{
				"default":"{code:200,msg:'',data:''}",
				"transform":"function filter_aeZFJf(data) {return data;}",
				"method":"POST",
				"isShareData":true,
				"name":"api_saveData",
				"description":"新建行数据",
				"global":false,
				"url":"/api/v2/permission/busiOrg"
			},
			{
				"transform":"function filter_ZdrnTZ(data) {\r/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果\r/*dm*/
\n  return data;\r/*dm*/
\n}",
				"method":"POST",
				"isShareData":true,
				"name":"api_getPreUploadUrl",
				"description":"预上传接口",
				"url":"/api/v1/attachment/preUpload"
			},
			{
				"transform":"function filter_jstRNX(data) {\r/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果\r/*dm*/
\n  return data;\r/*dm*/
\n}",
				"method":"GET",
				"isShareData":true,
				"name":"api_getCommonDraftInfo",
				"description":"根据id查询查询暂存想信息",
				"url":"/api/v1/model/common_draft/:id"
			},
			{
				"transform":"function filter_jstRNX(data) {\r/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果\r/*dm*/
\n  return data;\r/*dm*/
\n}",
				"method":"POST",
				"isShareData":true,
				"name":"api_postCommonDraft",
				"description":"暂存",
				"url":"/api/v1/model/common_draft"
			},
			{
				"default":"{\"code\":200,\"msg\":\"\",\"data\":{}}",
				"transform":"",
				"isModel":true,
				"method":"GET",
				"isShareData":true,
				"name":"api_getInfo",
				"description":"查找关系获取详情信息",
				"url":"/api/v1/model/:modelName/:id"
			},
			{
				"transform":"function filter_Y6biFKTj(data) {\r/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果\r/*dm*/
\n  return data;\r/*dm*/
\n}",
				"method":"GET",
				"isShareData":true,
				"name":"api_getDefaultData",
				"description":"获取模型的一条缺省数据",
				"global":false,
				"url":"/api/v1/dev/model/:modelName/default"
			},
			{
				"transform":"function filter_4MQRY8Z2(data) {\r/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果\r/*dm*/
\n  return data;\r/*dm*/
\n}",
				"method":"GET",
				"isShareData":true,
				"name":"api_getReferencedInfo",
				"description":"根据关系获取引用字段信息",
				"global":false,
				"url":"/api/v1/field/:modelName/:fieldName/:id/quote/linkage"
			},
			{
				"default":"{code:200,msg:'',data:{totalSize:0,records: []}}",
				"transform":"function filter_aeZFJf(data) {/*dm*/
\n        return JSON.parse(JSON.stringify(data));/*dm*/
\n      }",
				"method":"POST",
				"isShareData":true,
				"name":"api_getList",
				"description":"获取表格数据",
				"url":"/api/v1/model/:modelName/list"
			},
			{
				"default":"[]",
				"transform":"function filter_i7SH2hSN(data) {/*dm*/
\n  // 入参是经过请求拦截器处理后请求的结果，不是测试结果/*dm*/
\n  return data.data;/*dm*/
\n}",
				"method":"GET",
				"isShareData":true,
				"name":"api_getFormPageInfo",
				"description":"获取页面信息",
				"global":false,
				"url":"/api/v2/views/:modelName/:pageKey/form_view"
			}
		],
		"key":1713753952308,
		"attrs":{
			"style":{
				"height":"100%"
			},
			"setting":"{\"name\": \"page\"}"
		}
	},
	"editorSettingDsl":"{\"enableTitle\":false,\"columnTotal\":2,\"group\":{},\"table\":{\"default\":{\"showPlanView\":true,\"edit\":true,\"detail\":false,\"delete\":true,\"copy\":false}}}",
	"pageInterceptor":"",
	"pageSource":"model",
	"frame":"vue"
}