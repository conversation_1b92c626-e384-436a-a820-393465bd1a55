spring:
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
  application:
    name: 'joycreator'
  profiles:
    active: '@spring.profiles.active@'
  config:
    import: classpath:appconfig/<EMAIL>@.yml
  main:
    allow-bean-definition-overriding: true

metadata:
  trigger:
    path: com.jd.jdt.joycreator.trigger
  restapi:
    path: com.jd.jdt.joycreator.restapi


# mybatis 配置信息
mybatis-plus:
  configuration:
    default-statement-timeout: 15
    call-setters-on-nulls: true
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      update-strategy: NOT_NULL
      logic-delete-field: yn  # 全局逻辑删除的实体字段名(since 3.3.0,配置后可以忽略不配置步骤2)
      logic-delete-value: 0 # 逻辑已删除值(默认为 0)
      logic-not-delete-value: 1 # 逻辑未删除值(默认为 1)
      table-prefix: t_2138_ #表名前缀
  mapper-locations: classpath*:/mappers/*.xml
  type-aliases-package: com.jd.jdt.joycreator.ae.entity
logging:
  config: classpath:log/log4j2.yml

feign:
  okhttp:
    enabled: true
