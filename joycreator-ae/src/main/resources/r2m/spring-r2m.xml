<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">
  <bean id="ccProvider" class="com.wangyin.rediscluster.provider.CCProvider">
    <property name="address" value="${r2m.3cConnectionStr}"/>
    <property name="appName" value="${r2m.appName}"/>
    <property name="token" value="${r2m.3cToken}"/>
    <property name="password" value="${r2m.password}"/>
  </bean>

    <bean id="cacheClusterClient" class="com.wangyin.rediscluster.client.R2mClusterClient">
        <property name="maxRedirections" value="3"/>
        <property name="redisTimeOut" value="500"/>
        <property name="provider" ref="ccProvider"/>
    </bean>
</beans>