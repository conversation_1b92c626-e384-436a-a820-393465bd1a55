## 需求
1. 我是一名Java开发工程师，目前业务需要将word文档转换为html格式文件。
2. 转换后只保留一个.html文件。
3. 转换后的.html文件标签只要<body>体中标签的内容，不要<head>标签和<html>标签，把所有格式都放在<body>体中的标签中。
4. 转换后的格式要和原word文档格式完全一致，不得有任何差异，包含但不限于字体大小、颜色、样式等及表格格式等。
5. 转换后的.html文件中不能有任何注释和标签之间不得有换行。
6. 以下是供你测试的word文件，你可以在写的过程中进行代码转换效果测试，word目录：/Users/<USER>/IdeaProjects/JoyCreator/joycreator-ae/src/main/resources/scripts
7. java实现代码写在这个目录下：/Users/<USER>/IdeaProjects/JoyCreator/joycreator-ae/src/main/test/java/com/jd/jdt/joycreator/ae/wordtohtml，并且命名为WordToThml.java