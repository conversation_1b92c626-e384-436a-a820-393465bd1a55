## 北京及需求:
- 我在做一个AI智能写作相关项目，需要你帮我写一份提示词，我的需求为根据用户的最新历史会话记录结合历史会话上下文提取出所有改写类的和替换类的列表，

## 改写类示例说明：
- 帮我把1.2 合同条款改一下，增加一条xxx/更符合甲方利益/内容更专业更丰富等类似于这种语义。替换类示例：帮我把标题改为xxx/帮我把张三改为李四/帮我把1.2标题改为xxx/帮我把xxx地方的交付日期填充为xxxx年xx月xx日/手机号为xxxxx/等类似意义，

## 输入的会话记录示例为：
user（lave 1）：帮我写一份合同。
assistant（lave 2）：请问您需要什么样的合同，如销售合同，租赁合同，技术服务协议合同等。
user（lave 3）：租赁合同。
assistant（lave 4）：请问提供租赁合同的具体信息，如出租人名称身份证号地址联系方式等钱，租赁房的相关信息，以及其它要求？
user（lave 5）： 租赁人为赵晗，电话11111222222，身份证号码，改一下5.3的条款，使用增加一条违约责任信息，并把标题替换成销售合同。
assistant（lave 6）：好的，请问还有其它补充吗?，如租赁地址，租赁金额等？
user（lave 7）： 地址在北京市通州区,每月3000,押一付三。

## 输出要求json 
- list结构：每个对象要求有类型标注为替换or改写，如果是替换则带替换的内容字段和被改写的字符串内容，如果是改写需要改写的主要关键词比如xxx标题，要用来通过关键词定位，还需要一个要改写的风格方向要求等等。注意历史会话记录优先级为user大于assistant，总体优先级为user中lave中最大的是用户最新意图，assistant是模型对上次user的回复制作为参考，若最新意义于上文会话记录有关联则需要将关联处结合起来，若无关联则单独收集意义
**输出示例:**
```json
[
  {
    "类型": "改写",
    "关键词": "租赁人姓名改为赵晗"
  },
  {
    "类型": "改写",
    "关键词": "租赁人电话改为11111222222"
  },
  {
    "类型": "改写",
    "关键词": "租赁人身份证改为12388383838"
  },
  {
    "类型": "改写",
    "关键词": "5.3 条款",
    "改写方向": "增加一条违约责任信息"
  },
  {
    "类型": "替换",
    "被替换内容": "租赁合同标题销售合同"
  },
  {
    "类型": "替换",
    "被替换内容": "租赁地址改为北京市通州区"
  },
  {
    "类型": "替换",
    "被替换内容": "租赁改为3000/月"
  },
  {
    "类型": "替换",
    "被替换内容": "把租金部分改为押一付三"
  }
]
```