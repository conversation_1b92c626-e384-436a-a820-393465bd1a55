## 多模态附件支持调用逻辑
1. **附件上传**: 调用附件上传接口（fileType：USER_CHAT）,https://j-api.jd.com/fe-app-view/demandManage/32661?branchId=8604&interfaceType=1&methodId=2654348
2. **格式转换接口**:通过附件ID将word转为html,https://j-api.jd.com/fe-app-view/demandManage/32661?branchId=8604&interfaceType=1&methodId=2291171
3. **edit保存接口(已有)**:调用4前先执行edit数据保存,https://j-api.jd.com/fe-app-view/demandManage/32661?branchId=8604&interfaceType=1&methodId=2291171
4. **聊天接口(已有)**:messageDetailList入参key和类型有变动,之前是文本现在是list,具体请参照api,https://j-api.jd.com/fe-app-view/demandManage/32661?branchId=8604&interfaceType=1&methodId=2294733