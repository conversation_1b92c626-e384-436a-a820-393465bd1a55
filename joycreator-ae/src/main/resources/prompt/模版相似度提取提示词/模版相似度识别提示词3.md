## 任务目标

根据用户输入的语义内容，从给定的文档模版数据中，**严格筛选出与用户意图最相似的0～3个模版**，并返回其ID列表。
- 若无任何模版与用户意图相似度达到阈值，则返回空数组[]。
- 若有多个模版匹配，只返回相似度最高的3个，且**ID顺序必须严格按照相似度从高到低排列**，最相似的ID排在最前。

---

## 输入格式

- **用户输入语义**（字符串）：  
  例如：`{{input}}`
- **模版数据**（JSON数组，每个元素为对象）：  
  例如：
  ```json
  {{input}}
  ```
  每个对象包含以下字段：
   - `id`（字符串或数字）：模版唯一标识
   - `tag`（字符串）：文档类型（如“合同类”、“文书类”、“prd类”等）
   - `description`（字符串）：模版描述
      - “合同类”：多级结构，层级用“/”分隔，最后一级优先级最高。例如：`销售合同 / 产品销售 / 甲方立场 / 产品或服务`
      - 非“合同类”：为文档主要应用场景或核心内容的概述。例如：`描述了CRM系统的产品需求文档，涵盖项目概述、用户需求、功能需求等`

---

## 正确示例

### 示例1：有高度相关的合同类模版

**用户输入：**  
`我需要一份关于产品销售的合同`

**模版数据：**  
- 见上方输入示例

**输出：**
```json
[1]
```
> 说明：ID为1的description最后一级“产品或服务”与用户意图高度相关，相似度≥95%。

---

### 示例2：多个模版均高度相关

**用户输入：**  
`我需要一份CRM系统的产品需求文档和一份产品销售合同`

**模版数据：**  
见上方输入示例

**输出：**
```json
[1,2]
```
> 说明：ID为1和2的description均与用户意图不同部分高度相关，且ID顺序按相似度降序排列。

---

### 示例3：无任何模版达到阈值

**用户输入：**  
`我需要一份关于员工考勤的管理制度`

**模版数据：**  
见上方输入示例

**输出：**
```json
[]
```
> 说明：所有模版与用户意图相似度均低于95%，返回空数组。

---

### 示例4：多个合同类模版，优先级分明

**用户输入：**  
`我需要一份采购商品的合同`

**模版数据：**  
见上方输入示例

**输出：**
```json
[3]
```
> 说明：ID为3的description最后一级“采购商品”与用户意图高度相关。

---

## 错误示例（禁止出现）

### 错误1：输出超过3个ID

```json
[1,2,3,4]
```
> 错误：最多只能输出3个ID。

---

### 错误2：ID顺序与相似度不符

```json
[2,1]
```
> 错误：ID为1的相似度高于2，应为[1,2]。

---

### 错误3：输出了注释或解释

```json
[1] // 这是最相关的合同模版
```
> 错误：禁止输出任何注释或解释。

---

### 错误4：未达到阈值仍输出ID

```json
[3]
```
> 错误：若所有模版相似度均低于95%，应输出[]。

---

## 总结

- **严格筛选**：只选最相似的0～3个，且必须达到95%相似度阈值。
- **优先级清晰**：合同类description后级优先，非合同类整体匹配。
- **输出规范**：只输出ID数组，顺序为相似度降序，无任何多余内容。
- **示例充分**：参考正确与错误示例，确保理解无歧义。
