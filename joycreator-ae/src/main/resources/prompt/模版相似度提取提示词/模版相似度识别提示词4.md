## 任务目标

根据用户输入的语义内容，从给定的文档模版数据中，**严格筛选出与用户意图关键词匹配数量最多的0～3个模版**，并返回其ID列表。
- 若无任何模版与用户意图有关键词匹配，则返回空数组[]。
- 若有多个模版匹配，只返回匹配关键词数量最多的3个，且**ID顺序必须严格按照匹配关键词个数从多到少排列**，匹配最多的ID排在最前。

---

## 输入格式

- **用户输入语义**（字符串）：  
  用户语意为：`{{input}}`
- **模版数据**（JSON数组，每个元素为对象）：  
  每个对象包含以下字段：
    - `id`（字符串或数字）：模版唯一标识
    - `tag`（字符串）：文档类型（如“合同类”、“文书类”、“prd类”等）
    - `description`（字符串）：模版描述
        - “合同类”：**由若干标签拼接而成，标签数量不固定**，标签间用“、”分隔，立场标签始终放在最后。例如：`技术服务、合作、2024年度、北京市大兴区、甲方立场`。标签可包括：行业/领域、交易特征、业务特征、自由补齐（补足3-5个标签，每个≤5字）、立场偏向（如甲方立场/乙方立场/中立立场）。
        - 非“合同类”：为文档主要应用场景或核心内容的概述。例如：`描述了CRM系统的产品需求文档，涵盖项目概述、用户需求、功能需求等`
  以下为全量模版数据：
  ```json
  {{input}}
  ```

---

## 正确示例

### 示例1：有高度相关的合同类模版

**用户输入：**  
`我需要一份关于技术服务和合作的合同，最好是甲方立场`

**模版数据：**
- 见上方输入示例

**输出：**
```json
[1]
```
> 说明：ID为1的description中“技术服务”、“合作”、“甲方立场”均与用户意图匹配，匹配关键词数量最多。

---

### 示例2：多个模版均有关键词匹配

**用户输入：**  
`我需要一份CRM系统的产品需求文档和一份合作类合同`

**模版数据：**  
见上方输入示例

**输出：**
```json
[2,1]
```
> 说明：ID为2（非合同类）与“CRM系统”高度相关，ID为1（合同类）与“合作”匹配，ID顺序按匹配关键词数量降序排列。

---

### 示例3：无任何模版有关键词匹配

**用户输入：**  
`我需要一份关于员工考勤的管理制度`

**模版数据：**  
见上方输入示例

**输出：**
```json
[]
```
> 说明：所有模版与用户意图无关键词匹配，返回空数组。

---

### 示例4：多个合同类模版，按匹配关键词数量排序

**用户输入：**  
`我需要一份采购商品和服务的合同，乙方立场`

**模版数据：**  
见上方输入示例

**输出：**
```json
[3,4]
```
> 说明：ID为3的description中“采购商品”、“服务”、“乙方立场”与用户意图匹配3个关键词，ID为4匹配2个，按匹配数量排序。

---

## 错误示例（禁止出现）

### 错误1：输出超过3个ID

```json
[1,2,3,4]
```
> 错误：最多只能输出3个ID。

---

### 错误2：ID顺序与匹配关键词数量不符

```json
[2,1]
```
> 错误：ID为1的匹配关键词数量多于2，应为[1,2]。

---

### 错误3：输出了注释或解释

```json
[1] // 这是最相关的合同模版
```
> 错误：禁止输出任何注释或解释。

---

### 错误4：无关键词匹配仍输出ID

```json
[3]
```
> 错误：若所有模版无关键词匹配，应输出[]。

---

## 总结

- **严格筛选**：只选与用户意图关键词匹配数量最多的0～3个。
- **合同类description为标签拼接，数量不固定**，匹配时只看关键词匹配数量。
- **输出规范**：只输出ID数组，顺序为匹配关键词数量降序，无任何多余内容。
- **示例充分**：参考正确与错误示例，确保理解无歧义。
