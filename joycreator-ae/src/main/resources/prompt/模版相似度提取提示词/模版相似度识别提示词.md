
### 任务描述
根据用户输入的语义，找到与之相似度最高的数据（最少提取0条，最多提取3条），并提取出匹配度最高的数据ID。输出格式为JSON数组，格式如下：[id1, id2, ...]，若没有相似数据，则输出空数组[]。

---

### 输入
- **用户语义如下**: `${templateNameList}`
- **输入json数据字段说明，包含多个数据项，每个数据项由一个`id`（唯一标识）和一个`description`（用于匹配度的描述）组成。具体全量数据如下**：
```json
{{input}}
```

---

### 输出
- **相似度要求**：一定只有“用户语义”与全量数据的“description”相似度达到%95以上时才返回对应的ID；
- **数据格式要求**：JSON数组形式的匹配数据ID列表，格式为 `[id1, id2, ...]`，其中 `id1` 为匹配度最高的数据的ID，`id2`其次，提取的id数量控制在（0～3个），若没有相似数据，则输出空数组[]，输出格式如下（只输出ID，不要输出其他内容）。
```json
[id1, id2, ...]
```
---

### 步骤(以下步骤不必输出，仅作为你的思路)
1. **语义分析（不输出此内容，仅作为你的思路）**: 对用户输入的语义进行分析。
2. **相似度计算（不输出此内容，仅作为你的思路）**: 使用适当的算法计算用户输入与每个数据项描述之间的相似度，一定只有“用户语义”与全量数据的“description”相似度达到%95以上时才返回对应的ID。
3. **排序与筛选（不输出此内容，仅作为你的思路）**: 根据相似度对数据项进行排序，选择相似度最高的前三个数据项。
4. **提取ID（不输出此内容，仅作为你的思路）**: 提取选定数据项的ID。
5. **格式化输出（不输出此内容，仅作为你的思路）**: 以 JSON 数组格式输出结果，输出格式参考数据格式要求。

---

### 示例
假设：
- 输出案例为：`[id1, id2, ...]`

