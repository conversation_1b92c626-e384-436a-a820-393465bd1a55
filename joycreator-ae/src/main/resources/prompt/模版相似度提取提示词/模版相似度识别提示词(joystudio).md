## 任务目标

根据用户输入的语义内容，从给定的文档模版数据中，**严格筛选出与用户意图关键词匹配数量最多且相似度不低于90%的1个模版**，并返回其ID及反馈信息。
- 若无任何模版与用户意图有关键词匹配，或相似度均低于90%，则只返回`taskFeedback`字段（不输出空对象 `{}`）。
- 只返回最相似的1个模版，且**相似度需≥90%**，否则不返回id。
- `taskFeedback`字段为必输出，无论是否找到模版。

---

## 输入格式

- **用户输入语义**（字符串）：  
  用户语意为：`{{input}}`
- **模版名称**（字符串）：  
  name为：`{{name}}`
- **模版数据**（JSON数组，每个元素为对象）：  
  每个对象包含以下字段：
    - `id`（字符串或数字）：模版唯一标识
    - `name`（字符串）：为每一条模板的名称
    - `tag`（字符串）：文档类型（如“合同类”、“文书类”、“prd类”等）
    - `description`（字符串）：模版描述
        - “合同类”：**由若干标签拼接而成，标签数量不固定**，标签间用“、”分隔，立场标签始终放在最后。例如：`技术服务、合作、2024年度、北京市大兴区、甲方立场`。标签可包括：行业/领域、交易特征、业务特征、自由补齐（补足3-5个标签，每个≤5字）、立场偏向（如甲方立场/乙方立场/中立立场）。
        - 非“合同类”：为文档主要应用场景或核心内容的概述。例如：`描述了CRM系统的产品需求文档，涵盖项目概述、用户需求、功能需求等`
          以下为全量模版数据：
  ```json
  {{input}}
  ```

---

## 正确示例

### 示例1：有高度相关的合同类模版

**用户输入：**  
`我需要一份关于技术服务和合作的合同，最好是甲方立场`

**模版名称：**  
`技术服务合同`

**模版数据：**
- 见上方输入示例

**输出：**
```json
{
  "id": 1,
  "taskFeedback": "我已经为您创建了一份完整的技术服务合同模板，如有需要可随时使用。"
}
```
> 说明：ID为1的description中“技术服务”、“合作”、“甲方立场”均与用户意图匹配，且相似度≥90%。

---

### 示例2：无任何模版有关键词匹配或相似度不足

**用户输入：**  
`我需要一份关于员工考勤的管理制度`

**模版名称：**  
`员工考勤管理制度`

**模版数据：**  
见上方输入示例

**输出：**
```json
{
  "taskFeedback": "很抱歉，未能为您找到合适的员工考勤管理制度模板，如有其他需求欢迎随时告知。"
}
```
> 说明：未找到模版时，只输出taskFeedback字段，内容温柔、客气、简短。

---

## 输出格式

- 若找到最相似且相似度≥90%的模版，输出如下对象：
```json
{
  "id": "此处填充相似度最高的输入的ID",
  "taskFeedback": "此处根据是否找到模版自动生成，若没有找到模版，自动简短的生成一条没有找到模版的温馨提示，若找到了则自动生成类似于我已经为您创建了一份完整的xxxxx（输入的模版名称参数）模板，要求：语言简洁且突出重点，但是用词要温柔、客气、有一种被服从回复的语气"
}
```
- 若未找到符合条件的模版，仅输出：
```json
{
  "taskFeedback": "很抱歉，未能为您找到合适的xxxxx模板，如有其他需求欢迎随时告知。"
}
```

---

## 错误示例（禁止出现）

### 错误1：输出空对象

```json
{}
```
> 错误：未找到模版时不能输出空对象，必须输出taskFeedback字段。

---

### 错误2：输出了注释或解释

```json
{"id": 1, "taskFeedback": "我已经为您创建了一份完整的技术服务合同模板。"} // 这是最相关的合同模版
```
> 错误：禁止输出任何注释或解释。

---

### 错误3：无关键词匹配或相似度不足仍输出id

```json
{"id": 3, "taskFeedback": "..."}
```
> 错误：若所有模版无关键词匹配或相似度均低于90%，只输出taskFeedback字段。

---

## 总结

- **严格筛选**：只选与用户意图关键词匹配数量最多且相似度≥90%的1个。
- **合同类description为标签拼接，数量不固定**，匹配时只看关键词匹配数量。
- **输出规范**：无论是否找到模版，taskFeedback字段必输出，未找到时只输出taskFeedback。
- **示例充分**：参考正确与错误示例，确保理解无歧义。
