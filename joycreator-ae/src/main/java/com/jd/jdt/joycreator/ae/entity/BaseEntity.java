package com.jd.jdt.joycreator.ae.entity;

import com.baomidou.mybatisplus.activerecord.Model;
import com.baomidou.mybatisplus.annotation.*;
import com.jd.jdt.app4s.component.common.db.enums.YnEnum;
import lombok.Data;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;

import java.io.Serializable;
import java.util.Date;

@Data
public class BaseEntity<T extends Model> extends Model<T> {
    /**
     * 初始化乐观锁版本
     */
    private static final Integer INIT_MP_VERSION = 0;
    /**
     * 主键
     */
    @TableId(value = "id",type = IdType.AUTO)
    protected Long id;
    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    protected Date createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    protected Date updateTime;
    /**
     * 创建人
     */
    @TableField(value = "created_user", fill = FieldFill.INSERT)
    protected String createdUser;
    /**
     * 最后修改者
     */
    @TableField(value = "modified_user", fill = FieldFill.UPDATE)
    protected String modifiedUser;
    /**
     * 文件名
     */
    protected String name;
    /**
     * 归属人
     */
    protected String ownerId;
    /**
     * 租户id
     */
  //  protected String tenantId;
    /**
     * 是否删除
     */
    @TableField(value = "yn", fill = FieldFill.INSERT)
    @TableLogic(delval = "0", value = "1")
    protected Boolean yn;

    public BaseEntity() {
    }

    /**
     * 新增时初始化基础参数,此方法会将实体中（创建时间、乐观锁版本号、有效标识）为空的字段设置默认值
     * <br/>
     * 创建时间     当前系统时间
     * <br/>
     * 乐观锁版本号  0
     * <br/>
     * 有效标识     "1"
     *
     * @param entity 实体数据
     * @param <E>    实体类型
     */
    public static <E extends BaseEntity> void initBaseFields(E entity) {
        if (entity == null) {
            return;
        }
        if (entity.getCreateTime() == null) {
            entity.setCreateTime(new Date());
        }
        if (entity.getYn() == null) {
            entity.setYn(YnEnum.YES.getValue());
        }
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return ReflectionToStringBuilder.reflectionToString(this);
    }

}
