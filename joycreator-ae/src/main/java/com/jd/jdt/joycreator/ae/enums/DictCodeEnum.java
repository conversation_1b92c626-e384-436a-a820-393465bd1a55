package com.jd.jdt.joycreator.ae.enums;

/**
 * <p>
 * 字典编码枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
public enum DictCodeEnum {

    IP_WHITE("IP_WHITE", "MCP IP白名单"),
    SUGGEST_CATEGORY("SUGGEST_CATEGORY", "推荐分类");

    /**
     * code
     */
    private String code;
    /**
     * 描述
     */
    private String desc;

    DictCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static DictCodeEnum getEnum(String value) {
        for (DictCodeEnum a : DictCodeEnum.values()) {
            if (a.code.equals(value)) {
                return a;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
