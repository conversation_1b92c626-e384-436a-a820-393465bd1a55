package com.jd.jdt.joycreator.ae.service;

import com.jd.jdt.joycreator.ae.cache.RedisHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class LlmStreamCacheService {
    @Autowired
    private RedisHelper redisHelper;

    private static final String KEY_PREFIX = "llm_stream_";

    // 追加一条流式内容
    public void appendChunk(String sessionId, long index, String chunk) {
        String key = KEY_PREFIX + sessionId;
        List<String> list = (List<String>) redisHelper.getObject(key);
        if (list == null) {
            list = new ArrayList<>();
        }
        // 保证顺序
        while (list.size() <= index) list.add(null);
        list.set((int) index, chunk);
        redisHelper.setObject(key, list, true, 3600); // 1小时过期
    }

    // 获取 lastIndex 之后的所有内容
    public List<String> getChunksAfter(String sessionId, int lastIndex) {
        String key = KEY_PREFIX + sessionId;
        List<String> list = (List<String>) redisHelper.getObject(key);
        if (list == null || lastIndex + 1 >= list.size()) return new ArrayList<>();
        return list.subList(lastIndex + 1, list.size());
    }
} 