package com.jd.jdt.joycreator.ae.exception;

/**
 * <p>
 * 全局异常
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
public class BadLockOwnerException extends IllegalStateException {
    private static final long serialVersionUID = 1L;

    public BadLockOwnerException() {
        super();
    }

    public BadLockOwnerException(String message, Throwable cause) {
        super(message, cause);
    }

    public BadLockOwnerException(String s) {
        super(s);
    }

    public BadLockOwnerException(Throwable cause) {
        super(cause);
    }

}
