package com.jd.jdt.joycreator.ae.config;

import lombok.extern.log4j.Log4j2;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ImportResource;

/**
 * <p>
 * r2m配置
 * </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @since 2025-04-22
 */
@Configuration
@ImportResource(locations = {
        "classpath:r2m/spring-r2m.xml"})
@Log4j2
public class R2mConfig {
}






