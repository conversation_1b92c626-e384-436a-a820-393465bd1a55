package com.jd.jdt.joycreator.ae.utils;

import com.aspose.words.Document;
import com.aspose.words.HtmlSaveOptions;
import com.aspose.words.SaveFormat;

/**
 * <p>
 * Word To Html 工具类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
public class WordToHtmlConverterUtil {

    public static void convertDocxToHtml(String inputFilePath, String outputFilePath) throws Exception {
        // Load the document
        Document doc = new Document(inputFilePath);

        // Configure HTML save options with basic settings
        HtmlSaveOptions saveOptions = new HtmlSaveOptions(SaveFormat.HTML);

        // Basic formatting options
        saveOptions.setExportPageSetup(true);
        saveOptions.setExportFontResources(true);
        saveOptions.setPrettyFormat(true); // For readable HTML output
        
        // Disable automatic font weight adjustments
        saveOptions.setExportOriginalUrlForLinkedImages(false);

        // Save the document as HTML
        doc.save(outputFilePath, saveOptions);

        System.out.println("Document converted successfully from " + inputFilePath + " to " + outputFilePath);
        
        // Post-process the HTML to fix font weight issues
        postProcessHtmlFontWeights(outputFilePath);
    }
    
    /**
     * Post-process the generated HTML to fix font weight issues and move head content to body
     */
    private static void postProcessHtmlFontWeights(String htmlFilePath) {
        try {
            java.nio.file.Path path = java.nio.file.Paths.get(htmlFilePath);
            String content = new String(java.nio.file.Files.readAllBytes(path), "UTF-8");
            
            // Extract head content (style and meta tags)
            String headContent = "";
            java.util.regex.Pattern headPattern = java.util.regex.Pattern.compile(
                "<head[^>]*>(.*?)</head>", 
                java.util.regex.Pattern.DOTALL | java.util.regex.Pattern.CASE_INSENSITIVE
            );
            java.util.regex.Matcher headMatcher = headPattern.matcher(content);
            if (headMatcher.find()) {
                headContent = headMatcher.group(1);
            }
            
            // Fix CSS font-face declarations to remove unwanted bold weights
            headContent = headContent.replaceAll(
                "@font-face \\{ font-family:仿宋; font-weight:bold;", 
                "@font-face { font-family:仿宋;"
            );
            headContent = headContent.replaceAll(
                "@font-face \\{ font-family:黑体; font-weight:bold;", 
                "@font-face { font-family:黑体;"
            );
            
            // Remove excessive font-weight:bold from inline styles where it shouldn't be
            content = content.replaceAll(
                "font-family:仿宋; font-weight:bold(?!; background-color:#ffff00)", 
                "font-family:仿宋"
            );
            content = content.replaceAll(
                "font-family:黑体; font-weight:bold(?!; background-color:#ffff00)(?![^<]*</span>\\s*</p>\\s*<p[^>]*text-align:center)", 
                "font-family:黑体"
            );
            
            // Remove the entire HTML structure and keep only body content with embedded styles
            java.util.regex.Pattern bodyPattern = java.util.regex.Pattern.compile(
                "<body[^>]*>(.*?)</body>", 
                java.util.regex.Pattern.DOTALL | java.util.regex.Pattern.CASE_INSENSITIVE
            );
            java.util.regex.Matcher bodyMatcher = bodyPattern.matcher(content);
            
            if (bodyMatcher.find()) {
                String bodyContent = bodyMatcher.group(1);
                
                // Create new content with head styles moved to body
                StringBuilder newContent = new StringBuilder();
                
                // Add the head content (styles, meta tags) at the beginning of body
                if (!headContent.trim().isEmpty()) {
                    newContent.append(headContent);
                }
                
                // Add the original body content
                newContent.append(bodyContent);
                
                content = newContent.toString();
            }
            
            java.nio.file.Files.write(path, content.getBytes("UTF-8"));
            System.out.println("Post-processed HTML: fixed font weights and moved head content to body");
            
        } catch (Exception e) {
            System.err.println("Error post-processing HTML: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        // Example usage:
        // Replace "input.docx" with your Word document path
        // Replace "output.html" with your desired HTML output path
        String inputDocxPath = "/Users/<USER>/IdeaProjects/JoyCreator2/joycreator-ae/src/main/resources/scripts/京东大模型服务协议-客户模版20240927-JD法审 1106.docx";
        String outputHtmlPath = "/Users/<USER>/IdeaProjects/JoyCreator2/joycreator-ae/src/main/resources/scripts/output.html";

        try {
            convertDocxToHtml(inputDocxPath, outputHtmlPath);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
