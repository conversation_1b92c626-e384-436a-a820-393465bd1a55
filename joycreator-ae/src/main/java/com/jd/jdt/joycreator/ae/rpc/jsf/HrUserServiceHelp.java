package com.jd.jdt.joycreator.ae.rpc.jsf;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.cache.LoadingCache;
import com.google.common.reflect.TypeToken;
import com.jd.jdt.joybuilder.adapter.api.response.Page;
import com.jd.jdt.joybuilder.adapter.api.response.User;
import com.jd.jdt.joybuilder.adapter.service.config.ClientProperties;
import com.jd.jdt.joybuilder.adapter.service.response.PageUser;
import com.jd.jdt.joybuilder.adapter.service.response.Response;
import com.jd.jdt.joycreator.ae.pojo.dto.HrResultDTO;
import com.jd.jdt.joycreator.ae.pojo.dto.HrUserBasicDTO;
import com.jd.jsf.gd.util.JsonUtils;
import com.jd.official.omdm.is.hr.HrUserService;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.Type;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <p>
 * 人资接口 服务
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-05
 */
@Log4j2
@Service
public class HrUserServiceHelp {


    @Autowired
    private HrUserService hrUserService;
    @Autowired
    private ClientProperties clientProperties;
    public LoadingCache<String, Optional<HrUserBasicDTO>> cacheUser;


    public HrResultDTO<HrUserBasicDTO> queryUserByErp(String erp) {
        String appCode = clientProperties.getAppCode();
        log.info("模糊查询用户信息请求参数:erp:{} appCode{}", erp, appCode);
        String businessId = String.valueOf(System.currentTimeMillis());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        String requestTimestamp = sdf.format(new Date());
        String sign = this.toSign(businessId, requestTimestamp, erp);
        log.info("queryUser.erp:{}，sign:{}", erp, sign);
        String value = hrUserService.getAllUserTypeByUserName(appCode, businessId, requestTimestamp, sign, "JSON", erp);
        HrResultDTO<HrUserBasicDTO> hrUserResponse = null;

        try {
            String response = URLDecoder.decode(value, "UTF-8");
            log.info("queryUser.erp:{}，result:{}", erp, response);
            Type jsonType = (new TypeToken<HrResultDTO<HrUserBasicDTO>>() {
            }).getType();
            hrUserResponse = JsonUtils.parseObjectByType(response, jsonType);
            hrUserResponse.setIsSuccess("200".equals(hrUserResponse.getResStatus()));
        } catch (UnsupportedEncodingException var10) {
            log.error("人资接口不支持的解码异常", var10);
        }

        return hrUserResponse;
    }


    /**
     * 根据关键字模糊查询用户信息，不包含EXT账户，限定在京东科技范围内。
     *
     * @param keyword 模糊查询关键字
     * @return Page<User> 包含查询结果的分页对象
     */
    public Page<User> listUserByKeyWordNoEXT(String keyword, String bgCode) {
        String appCode = clientProperties.getAppCode();
        log.info("模糊查询用户信息请求参数:keyword:{} appCode{}", keyword, appCode);
        Map<String, Object> params = new HashMap<>(4);
        params.put("pageNo", 1);
        params.put("pageSize", 10);
        params.put("baseFuzzy", keyword);//多字段查询， ERP账号(userName)、真实姓名(realName)、公司邮箱(email)、第二邮箱(secondEmail) 多字段聚合查询，各字段之间关系为 或，检索方式为模糊查询(左右匹配)
        params.put("allAccount", 0); //不包含EXT账户
        params.put("organizationCode", bgCode); //京东科技范围内账户
        params.put("orgIsAllChild", 1); //部门下钻查询
        String businessId = String.valueOf(System.currentTimeMillis());
        String timestamp = LocalDateTime.ofInstant(Instant.now(), ZoneId.systemDefault()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.F"));
        Map<String, Map<String, Object>> finalParams = new HashMap<>();
        finalParams.put("params", params);
        String requestJson = JsonUtils.toJSONString(finalParams);
        String sign = toSign(businessId, timestamp, requestJson);
        String result = hrUserService.findUsers(appCode, businessId, timestamp, sign, "JSON", requestJson);
        Page.PageBuilder<User> builder = Page.builder();
        List<User> users = new ArrayList<>();
        Page<User> emptyPage = builder.pageSize(0).pageNum(0).records(new ArrayList<>()).build();
        if (StringUtils.isNotBlank(result)) {
            String decode = null;
            try {
                decode = URLDecoder.decode(result, "utf-8");
                log.info("模糊查询用户信息响应参数:result:{}", decode);
            } catch (UnsupportedEncodingException var14) {
                log.error("url解码异常!");
            }
            Response<PageUser<HrUserBasicDTO>> response = JSONObject.parseObject(decode, new TypeReference<Response<PageUser<HrUserBasicDTO>>>() {
            });
            if (response == null) {
                log.error("模糊查询用户信息失败!");
                return emptyPage;
            }
            if (!"200".equals(response.getResStatus())) {
                log.error("模糊查询用户信息失败!");
                return emptyPage;
            }
            PageUser<HrUserBasicDTO> responseBody = response.getResponsebody();
            if (responseBody.getUserVoList().isEmpty()) {
                return emptyPage;
            }
            for (HrUserBasicDTO userVoListBean : responseBody.getUserVoList()) {
                User user = new User();
                this.buildUser(user, userVoListBean);
                users.add(user);
            }
            return builder.totalPages(responseBody.getTotalCount()).pageNum(responseBody.getPageNo())
                    .pageSize(responseBody.getPageSize()).records(users).build();
        } else {
            log.error("hrUserServiceConsumer.findUsers返回结果为null");
            return emptyPage;
        }
    }


    /**
     * 生成sign
     *
     * @param requestParam
     * @return
     */
    private String toSign(String bussinessId, String requestTimestamp, String requestParam) {
        String safetyKey = clientProperties.getSafetyKey();
        String appCode = clientProperties.getAppCode();
        return DigestUtils.md5DigestAsHex((appCode + bussinessId + requestTimestamp + safetyKey + requestParam).getBytes(StandardCharsets.UTF_8));
    }

    private void buildUser(User user, HrUserBasicDTO responseBody) {
        BeanUtils.copyProperties(responseBody, user);
        user.setUserId(responseBody.getUserName());
        user.setHeadImage(responseBody.getHeadImg());
        user.setDepartmentCode(responseBody.getOrganizationCode());
        user.setDepartmentName(responseBody.getOrganizationName());
        user.setFullDetpCode(responseBody.getOrganizationFullPath());
        user.setFullDetpName(responseBody.getOrganizationFullName());
        if (StringUtils.isNotBlank(responseBody.getPositionShowName())) {
            user.setPositionName(responseBody.getPositionShowName());
        }
        /*if (Boolean.FALSE.equals(this.showGroupInformation)) {
            this.splitGroupInformation(user);
        }*/

    }


}
