package com.jd.jdt.joycreator.ae.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
/**
 * <AUTHOR>
 * @version 1.0
 * @date
 */
@Data
@Configuration
public class ApplicationConfig {
    /**
     * 适配器中 加解密
     */
    @Value("${app4s.dataSafe.encryption:}")
    private String encryptionUrl;

    /**
     * 适配器中 加解密
     */
    @Value("${app4s.dataSafe.decrypt:}")
    private String decryptUrl;
}
