package com.jd.jdt.joycreator.ae.enums;

import lombok.Getter;

@Getter
public enum TextOperationType {
    SUMMARIZE("SUMMARIZE", "缩写", "请对以下文本进行精炼概括，保持核心意思的同时使其更加简洁：\n" +
            "\n" +
            "-----\n" +
            "[${EXPANDED_WORDS}]\n" +
            "-----\n" +
            "\n" +
            "输出要求：\n" +
            "1. 将文本长度缩减至原文的[X]%（X为30-70之间的整数，根据需求调整）\n" +
            "2. 保持语言通顺自然，确保可读性\n" +
            "3. 保留所有关键信息和主要观点\n" +
            "4. 删除冗余内容、重复信息和次要细节\n" +
            "5. 使用简洁而有力的表达方式\n" +
            "6. 适当使用连接词以保持逻辑流畅\n" +
            "7. 可根据需要调整段落结构，但保持整体连贯性\n" +
            "\n" +
            "注意：\n" +
            "- 直接输出缩写后的内容，无需解释或展示处理过程\n" +
            "- 确保输出的文本在保持原意的同时，更加精炼和易于理解\n" +
            "- 如果原文包含列表或关键术语，请在缩写版本中予以保留\n" +
            "\n" +
            "[可选] 可用性指标：\n" +
            "- 可读性：使用通用可读性指标（如Flesch-Kincaid读取度指数）确保缩写后的文本易于理解\n" +
            "- 信息密度：每句话应包含至少一个关键信息点\n" +
            "- 连贯性：使用恰当的过渡词和连接词，确保各个部分之间的逻辑流畅\n" +
            "\n" +
            "\n" +
            "#特别注意：请直接输出优化后的文本，无需输出任何其他说明或解释。\n"),
    EXPAND("EXPAND", "扩写", "请对以下文本内容进行扩写，保持核心意思的同时使其更加丰富和详细：\n" +
            "\n" +
            "-----\n" +
            "[${EXPANDED_WORDS}]\n" +
            "-----\n" +
            "\n" +
            "输出要求：\n" +
            "1. 扩写方式（根据输入的文本内容按需使用）：\n" +
            "   a) 详细展开：针对具体段落进行深度扩展\n" +
            "   b) 丰富细节：对关键对象和事件进行多维度描述\n" +
            "   c) 引入案例：通过实际案例增强说服力\n" +
            "   d) 增加背景信息：提供必要的上下文支持\n" +
            "   e) 拓展思路：多角度分析观点和论点\n" +
            "   f) 丰富层次：构建清晰的逻辑框架\n" +
            "   g) 增加内容：补充新的内容模块\n" +
            "\n" +
            "2. 字数控制：\n" +
            "   - 扩写后的总字数应为原文的1.5-2倍\n" +
            "   - 扩写后的总字数误差不得超过目标字数的±5%\n" +
            "   - 合理分配各部分字数，确保内容充实且不冗余\n" +
            "3. 注意事项：\n" +
            "   - 保持与原文的相关性和风格一致\n" +
            "   - 确保扩写后的结构完整\n" +
            "   - 增强文本的专业性和说服力\n" +
            "   - 提高整体可读性和表达质量\n" +
            "   - 确保扩写后的文本在保持原意的同时更加丰富和详细\n" +
            "\n" +
            "#特别注意：请直接输出优化后的文本，无需输出任何其他说明或解释。\n"),

    POLISH("POLISH", "润色", "请对以下文本进行润色和优化，提升其表达质量和专业度：\n" +
            "\n" +
            "-----\n" +
            "[${EXPANDED_WORDS}]\n" +
            "-----\n" +
            "\n" +
            "输出要求：\n" +
            "1. 提升语言表达的优雅度和流畅性：\n" +
            "   - 使用更丰富、精准的词汇\n" +
            "   - 优化句子结构，使其更加通顺\n" +
            "   - 适当使用修辞手法，如比喻、排比等，增加文本的生动性\n" +
            "2. 严格保持原文的核心内容、主旨和风格：\n" +
            "   - 不改变原文的基本意思和论点\n" +
            "   - 保留原文的语气和写作风格（如正式、轻松、严肃等）\n" +
            "3. 提高文本的专业性：\n" +
            "   - 根据文本主题，适当增加相关的专业术语或概念\n" +
            "   - 确保使用的术语准确且一致\n" +
            "4. 改善文本结构和逻辑：\n" +
            "   - 优化段落之间的过渡，增强文本的连贯性\n" +
            "   - 调整信息呈现的顺序，使论述更加清晰有力\n" +
            "5. 纠正并改进：\n" +
            "   - 修正任何语法、拼写或标点错误\n" +
            "   - 消除冗余表达和不必要的重复\n" +
            "6. 增强文本的说服力和感染力：\n" +
            "   - 加强关键论点的论证\n" +
            "   - 适当增加例证或数据支持（如果适用）\n" +
            "7. 提升文本的整体可读性：\n" +
            "   - 确保段落长度适中，避免过长或过短\n" +
            "   - 使用适当的小标题或分段，增强文本的结构感\n" +
            "\n" +
            "注意事项：\n" +
            "- 直接输出优化后的文本，无需解释或展示处理过程\n" +
            "- 保持文本长度与原文相近，除非原文存在明显的冗余或不足\n" +
            "- 如果原文包含特定的格式要求（如学术引用、技术规范等），请在优化时保持这些格式\n" +
            "- 根据文本类型（如学术论文、商业报告、文学作品等）调整优化策略\n" +
            "\n" +
            "[可选] 质量控制指标：\n" +
            "- 可读性：确保优化后的文本易于理解，可考虑使用可读性指数进行评估\n" +
            "- 专业度：根据文本主题，适当增加该领域的专业表达和术语使用\n" +
            "- 一致性：确保全文在语气、风格和术语使用上保持一致\n" +
            "- 原创性：在优化过程中避免使用陈词滥调，保持表达的新鲜感\n" +
            "\n" +
            "\n" +
            "#特别注意：请直接输出优化后的文本，无需输出任何其他说明或解释。\n");

    private final String code;
    private final String description;
    private final String promptTemplate;

    TextOperationType(String code, String description, String promptTemplate) {
        this.code = code;
        this.description = description;
        this.promptTemplate = promptTemplate;
    }
}
