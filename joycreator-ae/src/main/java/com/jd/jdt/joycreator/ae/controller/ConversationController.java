package com.jd.jdt.joycreator.ae.controller;

import com.jd.jdt.joycreator.ae.pojo.dto.ConversationMessage;
import com.jd.jdt.joycreator.ae.pojo.dto.MessageContext;
import com.jd.jdt.joycreator.ae.service.ConversationService;
import com.jd.jsf.gd.util.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import java.util.UUID;

/**
 * Controller for handling multi-turn conversations with intent recognition
 */
@Log4j2
@RestController
@RequestMapping("/api/v1/conversation")
public class ConversationController {

    @Autowired
    private ConversationService conversationService;

    /**
     * Handle user messages in a conversation
     * This endpoint supports:
     * 1. Intent detection
     * 2. Multi-turn clarification
     * 3. Tool/plugin integration
     * 4. Context-aware response generation
     */
    @PostMapping(value = "/message", produces = "text/event-stream;charset=UTF-8")
    public Flux<String> handleMessage(@RequestBody ConversationMessage message) {
        log.info("收到用户消息，message：{}", JsonUtils.toJSONString(message));
        
        // Initialize context if not present
        if (message.getContext() == null) {
            message.setContext(new MessageContext());
            message.getContext().setConversationId(UUID.randomUUID().toString());
            message.getContext().setConversationStage("INTENT_DETECTION");
        }
        
        // Set message metadata
        message.setTimestamp(System.currentTimeMillis());
        if (message.getRole() == null) {
            message.setRole("user");
        }
        
        return conversationService.processUserMessage(message)
            .doOnComplete(() -> log.info("消息处理完成，conversationId：{}", message.getContext().getConversationId()));
    }

    private static final String INTENT_DETECTION_PROMPT =
            "你是 JoyEdit 智能写作助手，一位专业的写作任务分析专家。现在我需要你分析用户的写作需求，识别其意图和关键信息。\n\n" +
            // ...现有提示词内容...
            "\n\n请以下面的JSON格式返回你的分析结果：\n" +
            "{\n" +
            "  \"intentType\": \"用户主要意图\",\n" +
            "  \"confidence\": 0.8,\n" +
            "  \"parameters\": { /* 所有识别到的参数 */ },\n" +
            "  \"needsClarification\": true/false,\n" +
            "  \"clarificationPoints\": [\"需要澄清的点1\", \"需要澄清的点2\"],\n" +
            "  \"requiredTools\": [\"需要的工具1\", \"需要的工具2\"]\n" +
            "}\n" +
            "用户输入：%s";
} 
