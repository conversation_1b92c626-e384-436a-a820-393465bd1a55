package com.jd.jdt.joycreator.ae.service;

import com.jd.jdt.joycreator.ae.pojo.dto.*;
import com.jd.jdt.joycreator.ae.pojo.vo.AutofillExtractionVO;
import com.jd.jdt.joycreator.ae.pojo.vo.AutofillVO;
import com.jd.jdt.joycreator.ae.pojo.vo.CreateOutlineVO;
import com.jd.jdt.joycreator.ae.pojo.vo.EditDocumentVO;

import java.io.IOException;
import java.util.List;
import java.util.Queue;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <p>
 * 编辑器文档数据 服务
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-27
 */
public interface EditDocumentService {

    /**
     * 保存编辑器文档数据
     *
     * @param editDocumentDTO
     * @return
     */
    Long save(EditDocumentDTO editDocumentDTO);

    /**
     * 通过sessionId查询聊天记录
     *
     * @param sessionId
     * @return
     */
    EditDocumentVO dtl(String sessionId);

    /**
     * call原文提取tools
     *
     * @param sessionId
     * @param contentBuffer
     * @param chatHistoryDetailDTOQueue
     * @param toolDtlDTO
     */
    void buildDoccontentExtractionBuffer(String sessionId, Queue<Object> contentBuffer, Queue<ChatHistoryDetailDTO> chatHistoryDetailDTOQueue, ToolDtlDTO toolDtlDTO);

    /**
     * call全文重写改写tools
     *
     * @param sessionId
     * @param contentBuffer
     * @param chatHistoryDetailDTOQueue
     * @param toolDtlDTO
     */
    void buildDoccontentRewriteBuffer(String sessionId, Queue<Object> contentBuffer, Queue<ChatHistoryDetailDTO> chatHistoryDetailDTOQueue, ToolDtlDTO toolDtlDTO);

    /**
     * 大纲撰写
     *
     * @param tuserSemantic
     * @return
     */
    CreateOutlineVO listCreateOutlineVO(String tuserSemantic);

    /**
     * 调用关键词替换工具
     *
     * @param sessionId
     * @param contentBuffer
     * @param chatHistoryDetailDTOQueue
     * @param toolDtlDTO
     */
    void callTextReplacement(String sessionId, Queue<Object> contentBuffer, Queue<ChatHistoryDetailDTO> chatHistoryDetailDTOQueue, ToolDtlDTO toolDtlDTO);

    /**
     * word转html
     *
     * @param fileId
     * @return
     */
    String convertHtml(Long fileId) throws IOException;

    /**
     * 关键词提取
     *
     * @param autofillExtractionDTO
     * @return
     */
    AutofillExtractionVO autofillExtraction(AutofillExtractionDTO autofillExtractionDTO);

    /**
     * 获取全部创作文档列表
     *
     * @return
     */
    List<EditDocumentVO> list();

    /**
     * 删除编辑器文档数据
     *
     * @param sessionId
     * @return
     */
    Boolean del(String sessionId);
}
