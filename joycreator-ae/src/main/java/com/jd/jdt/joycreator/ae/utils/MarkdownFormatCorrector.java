package com.jd.jdt.joycreator.ae.utils;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <p>
 * Markdown格式纠正工具类，支持流式输出的实时格式纠正
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Log4j2
@Component
public class MarkdownFormatCorrector {

    // 用于存储跨块的上下文，处理可能被分割的格式标记
    private StringBuilder contextBuffer = new StringBuilder();
    
    // 标题格式正则表达式：匹配 ###1.1 这种格式（标题符号和数字之间没有空格）
    private static final Pattern HEADING_PATTERN = Pattern.compile("(^|\\n)(#{1,6})([0-9])", Pattern.MULTILINE);
    
    // 列表格式正则表达式：匹配 ***1.1.1 这种格式（星号和数字之间没有空格）
    private static final Pattern LIST_PATTERN = Pattern.compile("(^|\\n)(\\*{1,3})([0-9])", Pattern.MULTILINE);
    
    /**
     * 流式纠正Markdown格式
     * 
     * @param chunk 当前文本块
     * @return 纠正后的文本
     */
    public String correctStreamingMarkdown(String chunk) {
        if (StringUtils.isEmpty(chunk)) {
            return chunk;
        }
        
        // 将当前块添加到上下文缓冲区
        contextBuffer.append(chunk);
        
        // 如果缓冲区太大，处理并清空前面部分，保留一定长度用于处理跨块的格式
        String processedText = "";
        if (contextBuffer.length() > 1000) {
            // 保留最后200个字符用于上下文处理
            processedText = contextBuffer.substring(0, contextBuffer.length() - 200);
            String correctedText = correctMarkdownFormat(processedText);
            contextBuffer.delete(0, contextBuffer.length() - 200);
            return correctedText;
        }
        
        // 处理当前块
        String correctedText = correctMarkdownFormat(chunk);
        return correctedText;
    }
    
    /**
     * 处理缓冲区中剩余的内容（在流结束时调用）
     * 
     * @return 纠正后的剩余文本
     */
    public String finalizeBuffer() {
        String correctedText = correctMarkdownFormat(contextBuffer.toString());
        contextBuffer.setLength(0); // 清空缓冲区
        return correctedText;
    }
    
    /**
     * 纠正Markdown格式
     * 
     * @param text 需要纠正的文本
     * @return 纠正后的文本
     */
    public String correctMarkdownFormat(String text) {
        if (StringUtils.isEmpty(text)) {
            return text;
        }
        
        // 纠正标题格式：###1.1 -> ### 1.1
        String correctedText = HEADING_PATTERN.matcher(text).replaceAll("$1$2 $3");
        
        // 纠正列表格式：***1.1.1 -> *** 1.1.1
        correctedText = LIST_PATTERN.matcher(correctedText).replaceAll("$1$2 $3");
        
        return correctedText;
    }
    
    /**
     * 重置上下文缓冲区
     */
    public void resetContextBuffer() {
        contextBuffer.setLength(0);
    }
}
