package com.jd.jdt.joycreator.ae.cache;

import com.jd.jdt.app4s.component.common.api.exception.BussinessBizException;
import com.jd.jdt.joycreator.ae.utils.SpringUtils;
import com.wangyin.r2m.client.jedis.JedisPubSub;
import com.wangyin.rediscluster.client.CacheClusterClient;
import com.wangyin.rediscluster.client.R2mClusterClient;
import lombok.extern.log4j.Log4j2;

import java.util.List;
import java.util.Timer;
import java.util.TimerTask;
import java.util.UUID;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 基于redis的分布式锁
 *
 * <AUTHOR>
 */
@Log4j2
public class RLock {
    private static final Long ONE = 1L;
    private static final String MESSAGE_UNLOCK = "unlock";
    private static final Object LOCK = new Object();
    private static final long INNER_EXPIRE = TimeUnit.SECONDS.toMillis(30);
    private static final String ID = UUID.randomUUID().toString();
    private static volatile LockReleaseSub lockReleaseSub;
    private static Timer timer;
    private static final ConcurrentHashMap<String, TimerTask> tasks = new ConcurrentHashMap<String, TimerTask>();

    private String key;
    private CacheClusterClient r2m;
    private TimerTask keepLockTask;
    private AtomicInteger counter = new AtomicInteger(0);

    private RLock(CacheClusterClient r2m, String key) {
        this.r2m = r2m;
        this.key = key;
        this.keepLockTask = new TimerTask() {
            @Override
            public void run() {
                RLock.this.r2m.pexpire(RLock.this.key, INNER_EXPIRE);
            }
        };
    }

    /**
     * 针对给定key创建一个分布式锁
     */
    public static RLock on(String key) {
        if (lockReleaseSub == null) {
            synchronized (LOCK) {
                if (lockReleaseSub == null) {
                    lockReleaseSub = new LockReleaseSub(SpringUtils.getBean(R2mClusterClient.class));
                    timer = new Timer(true);
                }
            }
        }
        return new RLock(SpringUtils.getBean(R2mClusterClient.class), key);
    }

    private String id() {
        return ID + ":" + Thread.currentThread().getId();
    }

    private String channel() {
        return "lock__channel__" + key;
    }

    private static final String LOCK_SCRIPT =
            "if redis.call('exists', KEYS[1]) == 0 then " +
                    "redis.call('hset', KEYS[1], ARGV[1], 1); " +
                    "redis.call('pexpire', KEYS[1], ARGV[2]); " +
                    "return nil; " +
                    "end; " +
                    "if (redis.call('hexists', KEYS[1], ARGV[1]) == 1) then " +
                    "redis.call('hincrby', KEYS[1], ARGV[1], 1); " +
                    "redis.call('pexpire', KEYS[1], ARGV[2]); " +
                    "return nil; " +
                    "end; " +
                    "return redis.call('pttl', KEYS[1]);";

    private boolean tryLockInner(long expire) {
        Object pttl = r2m.eval(LOCK_SCRIPT, key, id(), String.valueOf(expire));
        if (pttl != null) {
            return false;
        }
        counter.incrementAndGet();
        return true;
    }

    /**
     * 获取锁。
     * 有阻塞，如果锁已经被其他线程占有则阻塞当前线程，直到锁被释放而由当前线程获取到。
     * 长期占有，一旦获取锁，除非线程主动释放锁，否则锁会被长期占有。
     */
    public void lock() throws InterruptedException {
        lock(-1);
    }

    /**
     * 获取锁。
     * 有阻塞，如果锁已经被其他线程占有则阻塞当前线程，直到锁被释放而由当前线程获取到。
     * 非长期占有，即使线程不主动释放锁，超失效时长后，锁也会自动释放。
     */
    public void lock(long expire) throws InterruptedException {

        LockReleaseListener listener = null;
        try {

            if (!tryLockInner(expire == -1 ? INNER_EXPIRE : expire)) {
                Semaphore semaphore = new Semaphore(0);
                listener = new LockReleaseListener(channel()) {
                    @Override
                    void onRelease() {
                        semaphore.release();
                    }
                };
                lockReleaseSub.addListener(listener);

                while (true) {
                    if (tryLockInner(expire == -1 ? INNER_EXPIRE : expire)) {
                        break;
                    }

                    semaphore.acquire();
                }
            }

            if (expire == -1) {
                keepLock();
            }
        } finally {
            if (listener != null) {
                lockReleaseSub.removeListener(listener);
            }
        }
    }

    /**
     * 尝试获取锁。
     * 无阻塞，无论是否成功获取锁都立即返回。
     * 长期占有，一旦获取锁，除非线程主动释放锁，否则锁会被长期占有。
     * 如果锁已经被其他线程占有则立即返回false，如果取得锁则长期占有锁并返回true。
     */
    public boolean tryLock() {
        if (tryLockInner(INNER_EXPIRE)) {
            keepLock();
            return true;
        }
        return false;
    }

    private void keepLock() {
        TimerTask old = tasks.putIfAbsent(key, keepLockTask);
        if (old == null) {
            timer.schedule(keepLockTask, INNER_EXPIRE / 3, INNER_EXPIRE / 3);
        } else {
            keepLockTask = old;
        }
    }

    /**
     * 尝试获取锁。
     * 有阻塞，如果锁已经被其他线程占有则阻塞当前线程，直到给定的超时时间到达或者锁被释放而由当前线程获取到。
     * 长期占有，一旦获取锁，除非线程主动释放锁，否则锁会被长期占有。
     * 如果成功获取锁，则返回true，否则返回false。
     *
     * @param timeout
     * @throws InterruptedException
     */
    public boolean tryLock(long timeout) throws InterruptedException {
        return tryLock(timeout, -1);
    }

    /**
     * 尝试获取锁。
     * 有阻塞，如果锁已经被其他线程占有则阻塞当前线程，直到给定的超时时间到达或者锁被释放而由当前线程获取到。
     * 非长期占有，即使线程不主动释放锁，超失效时长后，锁也会自动释放。
     * 如果成功获取锁，则返回true，否则返回false。
     *
     * @param timeout
     * @param expire
     * @return
     * @throws InterruptedException
     */
    public boolean tryLock(long timeout, final long expire) throws InterruptedException {

        long deadLine = System.nanoTime() + TimeUnit.MILLISECONDS.toNanos(timeout);

        LockReleaseListener listener = null;
        try {

            if (!tryLockInner(expire == -1 ? INNER_EXPIRE : expire)) {
                Semaphore semaphore = new Semaphore(0);
                listener = new LockReleaseListener(channel()) {
                    @Override
                    void onRelease() {
                        semaphore.release();
                    }
                };
                lockReleaseSub.addListener(listener);

                while (true) {
                    if (tryLockInner(expire == -1 ? INNER_EXPIRE : expire)) {
                        break;
                    }

                    if (!semaphore.tryAcquire(deadLine - System.nanoTime(), TimeUnit.NANOSECONDS)) {
                        return false;
                    }
                }
            }

            if (expire == -1) {
                keepLock();
            }

            return true;
        } finally {
            if (listener != null) {
                lockReleaseSub.removeListener(listener);
            }
        }
    }

    private static final String UNLOCK_SCRIPT =
            "if (redis.call('exists', KEYS[1]) == 0) then " +
                    "redis.call('publish', ARGV[1], ARGV[2]); " +
                    "return 1; " +
                    "end;" +
                    "if (redis.call('hexists', KEYS[1], ARGV[4]) == 0) then " +
                    "return nil;" +
                    "end; " +
                    "local counter = redis.call('hincrby', KEYS[1], ARGV[4], -1); " +
                    "if (counter > 0) then " +
                    "redis.call('pexpire', KEYS[1], ARGV[3]); " +
                    "return 0; " +
                    "else " +
                    "redis.call('del', KEYS[1]); " +
                    "redis.call('publish', ARGV[1], ARGV[2]); " +
                    "return 1; " +
                    "end; " +
                    "return nil;";

    /**
     * （主动）释放锁。
     * 如果锁并非由当前线程持有，则抛出BadLockOwnerException异常。
     */
    public void unlock() {
        if (counter.get() == 0) {
            return;
        }
        Object eval = r2m.eval(UNLOCK_SCRIPT, key, channel(), MESSAGE_UNLOCK, String.valueOf(INNER_EXPIRE), id());
        if (eval == null) {
            throw new BussinessBizException("Lock " + key + " holding by other thread.");
        }
        if (ONE.equals(eval)) {
            keepLockTask.cancel();
        }
        counter.decrementAndGet();
    }

    private static final String FORCE_UNLOCK_SCRIPT =
            "if (redis.call('del', KEYS[1]) == 1) then " +
                    "redis.call('publish', ARGV[1], ARGV[2]); " +
                    "return 1 " +
                    "else " +
                    "return 0 " +
                    "end";

    /**
     * 强制释放锁。
     * 即使锁并非当前线程持有，也会强行解锁。
     * 谨慎使用。
     */
    public boolean forceUnlock() {
        keepLockTask.cancel();
        Object eval = r2m.eval(FORCE_UNLOCK_SCRIPT, key, channel(), MESSAGE_UNLOCK);
        return ONE.equals(eval);
    }

    static class LockReleaseSub {
        static String PATTERN = "lock__channel__*";
        List<LockReleaseListener> listeners = new CopyOnWriteArrayList<LockReleaseListener>();

        private JedisPubSub jedisPubSub;

        LockReleaseSub(final CacheClusterClient r2m) {
            this.jedisPubSub = new JedisPubSub() {
                @Override
                public void onPMessage(String pattern, String channel, String message) {
                    if (!MESSAGE_UNLOCK.equals(message)) {
                        return;
                    }
                    for (LockReleaseListener l : listeners) {
                        if (l == null) {
                            continue;
                        }
                        if (!channel.equals(l.channel)) {
                            continue;
                        }
                        l.onRelease();
                    }
                }
            };
            Executors.newSingleThreadExecutor().submit(new Runnable() {
                @Override
                public void run() {
                    try {
                        r2m.psubscribe(jedisPubSub, PATTERN);
                    } catch (Exception e) {
                        log.error("", e);
                        //jedisPubSub.punsubscribe(PATTERN);
                    }
                }
            });
        }

        void addListener(LockReleaseListener listener) {
            if (listeners.contains(listener)) {
                return;
            }
            listeners.add(listener);
        }

        void removeListener(LockReleaseListener listener) {
            listeners.remove(listener);
        }
    }

    static abstract class LockReleaseListener {
        String channel;

        LockReleaseListener(String channel) {
            this.channel = channel;
        }

        abstract void onRelease();
    }
}
