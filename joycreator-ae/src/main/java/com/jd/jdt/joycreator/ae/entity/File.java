package com.jd.jdt.joycreator.ae.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.jd.jdt.joycreator.ae.enums.FileTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 文件-业务附件
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_2943_file")
public class File extends BaseEntity<File> {

    private static final long serialVersionUID = 1L;

    /**
     * 文件后缀
     */
    private String fileSuffix;

    /**
     * 文件类型
     */
    private FileTypeEnum fileType;

    /**
     * 文件大小
     */
    private BigDecimal fileSize;

    /**
     * 外网链接
     */
    private String wanUrl;

    /**
     * 内网链接
     */
    private String lanUrl;


    public static final String FILE_SUFFIX = "file_suffix";

    public static final String FILE_TYPE = "file_type";

    public static final String FILE_SIZE = "file_size";

    public static final String WAN_URL = "wan_url";

    public static final String LAN_URL = "lan_url";

    @Override
    protected Serializable pkVal() {
        return null;
    }

}
