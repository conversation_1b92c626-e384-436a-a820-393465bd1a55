package com.jd.jdt.joycreator.ae.service.impl;

import com.google.common.collect.Maps;
import com.jd.jdt.app4s.component.common.api.exception.BussinessBizException;
import com.jd.jdt.joycreator.ae.enums.ChatRoleEnum;
import com.jd.jdt.joycreator.ae.pojo.dto.ChatHistoryOperationDTO;
import com.jd.jdt.joycreator.ae.pojo.dto.ChatHistoryOperationExpandDTO;
import com.jd.jdt.joycreator.ae.service.ChatHistoryDetailService;
import com.jd.jdt.joycreator.ae.service.ChatHistoryDetailTemplateService;
import com.jd.jdt.joycreator.ae.service.ChatHistoryOperationRules;
import com.jd.jsf.gd.util.JsonUtils;
import com.jd.jsf.gd.util.StringUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;

/**
 * <p>
 * chat区域操作规则实现
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Log4j2
@Service("chatHistoryOperationRulesImpl")
public class ChatHistoryOperationRulesImpl implements ChatHistoryOperationRules<ChatHistoryOperationExpandDTO, Boolean> {
    private Map<ChatRoleEnum, Function<ChatHistoryOperationDTO, Boolean>> operationRuleRunMap = Maps.newHashMap();

    @Autowired
    private ChatHistoryDetailService chatHistoryDetailService;
    @Autowired
    private ChatHistoryDetailTemplateService chatHistoryDetailTemplateService;

    @PostConstruct
    private void operationRuleRunInit() {
        operationRuleRunMap.put(ChatRoleEnum.TEMPLATE_LIBRARY, d -> chatHistoryDetailTemplateService.confirmTemplateLibrary((ChatHistoryOperationExpandDTO) d));
        operationRuleRunMap.put(ChatRoleEnum.DOCCONTENT_REWRITE, d -> chatHistoryDetailService.confirmDoccontentRewrite(d));
        operationRuleRunMap.put(ChatRoleEnum.OUTLINE, d -> chatHistoryDetailService.updateOutline((ChatHistoryOperationExpandDTO) d));
        operationRuleRunMap.put(ChatRoleEnum.TEXT_REWRITE, d -> chatHistoryDetailService.confirmDoccontentRewrite(d));

    }


    @Override
    public Boolean execute(ChatHistoryOperationExpandDTO chatHistoryOperationExpandDTO) {
        log.info("chat区域操作规则实现，chatHistoryOperationExpandDTO：{}", JsonUtils.toJSONString(chatHistoryOperationExpandDTO));
        if (Objects.isNull(chatHistoryOperationExpandDTO) || Objects.isNull(chatHistoryOperationExpandDTO.getChatRole()) || StringUtils.isBlank(chatHistoryOperationExpandDTO.getBusinessNo())) {
            throw new BussinessBizException("请求必要参数为空！");
        }

        Function<ChatHistoryOperationDTO, Boolean> result = operationRuleRunMap.get(chatHistoryOperationExpandDTO.getChatRole());
        if (Objects.isNull(result)) {
            log.error("chat区域操作规则实现，未找到此规则！");
            return null;
        }
        Boolean applyResult = result.apply(chatHistoryOperationExpandDTO);
        log.info("chat区域操作规则实现，applyResult：{}", JsonUtils.toJSONString(applyResult));
        return applyResult;
    }
}
