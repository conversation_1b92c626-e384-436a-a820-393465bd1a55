package com.jd.jdt.joycreator.ae.service.impl;

import com.amazonaws.HttpMethod;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.*;
import com.google.gson.Gson;
import com.jd.jdt.joycreator.ae.config.OssConfig;
import com.jd.jdt.joycreator.ae.pojo.dto.UploadFileDTO;
import com.jd.jdt.joycreator.ae.service.FileService;
import com.jd.jdt.joycreator.ae.utils.Punctuation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.net.URL;
import java.util.Date;

/**
 * <p>
 * 附件相关实现
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Slf4j
@Service
public class FileServiceImpl implements FileService {
    @Autowired
    private OssConfig ossConfig;
    @Autowired
    private AmazonS3 amazonS3Client;


    @Override
    public UploadFileDTO uploadFile(String fileName, InputStream inputStream, String contentType) {
        ObjectMetadata objectMetadata = new ObjectMetadata();
        objectMetadata.setContentType(contentType);
        upload(fileName, inputStream, objectMetadata);
        GeneratePresignedUrlRequest generatePresignedUrlRequest = (new GeneratePresignedUrlRequest(ossConfig.getBucket(), fileName)).withMethod(HttpMethod.GET).withExpiration(new Date(253402214400000L));
        URL url = this.amazonS3Client.generatePresignedUrl(generatePresignedUrlRequest);
        String domain = url.getHost();
        String fileLanUrl = url.toString().replace(ossConfig.getProtocol() + Punctuation.HTTP + domain, ossConfig.getIntranetEndpoint());
        String fileWanUrl = url.toString().replace(ossConfig.getProtocol() + Punctuation.HTTP + domain, ossConfig.getEndpoint());
        UploadFileDTO fileVo = new UploadFileDTO();
        fileVo.setFileLanUrl(fileLanUrl);
        fileVo.setFileWanUrl(fileWanUrl);
        fileVo.setFileName(fileName);
        return fileVo;
    }

    public void upload(String fileName, InputStream inputStream, ObjectMetadata objectMetadata) {
        log.info("附件上传，入参，bucketName：{}，fileName：{}", ossConfig.getBucket(), fileName);
        boolean isExist = this.amazonS3Client.listBuckets().stream().anyMatch((a) -> ossConfig.getBucket().equals(a.getName()));
        if (!isExist) {
            log.info("附件上传，bucketName：{}为空，开始创建bucketName！", ossConfig.getBucket());
            this.amazonS3Client.createBucket(ossConfig.getBucket());
        }
        PutObjectResult putObjectResult = this.amazonS3Client.putObject(ossConfig.getBucket(), fileName, inputStream, objectMetadata);
        log.info("附件上传，当前文件名：{}，附件上传执行完毕，putObjectResult：{}", fileName, new Gson().toJson(putObjectResult));
    }

    public InputStream downLoad(String fileName) {
        log.info("附件下载，入参bucketName：{}，fileName：{}", ossConfig.getBucket(), fileName);
        S3Object object = this.amazonS3Client.getObject(ossConfig.getBucket(), fileName);
        S3ObjectInputStream objectContent = object.getObjectContent();
        return objectContent;
    }
}
