package com.jd.jdt.joycreator.ae.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.jdt.app4s.component.common.api.entity.ResponseResult;
import com.jd.jdt.joycreator.ae.pojo.dto.ChatHistoryOperationExpandDTO;
import com.jd.jdt.joycreator.ae.pojo.dto.CollectionDTO;
import com.jd.jdt.joycreator.ae.pojo.vo.BaseQueryVO;
import com.jd.jdt.joycreator.ae.pojo.vo.ChatHistoryListVO;
import com.jd.jdt.joycreator.ae.pojo.vo.ChatHistoryVO;
import com.jd.jdt.joycreator.ae.service.ChatHistoryDetailService;
import com.jd.jdt.joycreator.ae.service.ChatHistoryOperationRules;
import com.jd.jdt.joycreator.ae.service.ChatHistoryService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 聊天记录 服务接口
 * </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @since 2025-04-22
 */
@Log4j2
@RestController
@RequestMapping("/api/v1/chat-history")
public class ChatHistoryController {

    @Autowired
    private ChatHistoryService chatHistoryService;
    @Autowired
    private ChatHistoryDetailService chatHistoryDetailService;
    @Resource(name = "chatHistoryOperationRulesImpl")
    private ChatHistoryOperationRules chatHistoryOperationRules;


    @PostMapping("/create")
    public ResponseResult<String> create() {
        return ResponseResult.success("success", chatHistoryService.create());
    }

    @GetMapping("/dtl/{sessionId}")
    public ResponseResult<ChatHistoryVO> dtl(@PathVariable("sessionId") String sessionId) {
        return ResponseResult.success(chatHistoryDetailService.dtl(sessionId));
    }

    @PostMapping("/list")
    public ResponseResult<Page<ChatHistoryListVO>> searchChatHistoryListVO(@RequestBody() BaseQueryVO<String> baseQueryVO) {
        return ResponseResult.success(chatHistoryService.searchChatHistoryListVO(baseQueryVO));
    }

    @PostMapping("/collection")
    public ResponseResult<Boolean> updateCollection(@RequestBody() CollectionDTO collectionDTO) {
        return ResponseResult.success(chatHistoryService.updateCollection(collectionDTO));
    }

    @DeleteMapping("/del/{sessionId}")
    public ResponseResult<Boolean> del(@PathVariable("sessionId") String sessionId) {
        return ResponseResult.success(chatHistoryService.del(sessionId));
    }

    @PostMapping("/operation")
    public ResponseResult<Boolean> operation(@RequestBody() ChatHistoryOperationExpandDTO chatHistoryOperationExpandDTO) {
        return ResponseResult.success((Boolean) chatHistoryOperationRules.execute(chatHistoryOperationExpandDTO));
    }


}
