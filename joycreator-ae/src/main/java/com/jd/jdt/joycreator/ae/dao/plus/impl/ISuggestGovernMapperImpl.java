package com.jd.jdt.joycreator.ae.dao.plus.impl;

import com.jd.jdt.joycreator.ae.entity.SuggestGovern;
import com.jd.jdt.joycreator.ae.dao.mapper.SuggestGovernMapper;
import com.jd.jdt.joycreator.ae.dao.plus.ISuggestGovernMapper;
import com.jd.jdt.joycreator.ae.dao.plus.IBaseMapperImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 推荐管理-推荐管理配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@Service
public class ISuggestGovernMapperImpl extends IBaseMapperImpl<SuggestGovernMapper, SuggestGovern> implements ISuggestGovernMapper {

    public List<SuggestGovern> listSuggestGovern() {
        LambdaQueryWrapper<SuggestGovern> queryWrapper = new LambdaQueryWrapper<>();
        // 只查未删除的记录（yn为true）
        queryWrapper.eq(SuggestGovern::getIsEnabled, "Y")
                .orderByAsc(SuggestGovern::getPriority);
        return list(queryWrapper);
    }

}
