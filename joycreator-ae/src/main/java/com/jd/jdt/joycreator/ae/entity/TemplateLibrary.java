package com.jd.jdt.joycreator.ae.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 模板库
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_2943_template_library")
public class TemplateLibrary extends BaseEntity<TemplateLibrary> {

    private static final long serialVersionUID = 1L;

    /**
     * 标签
     */
    private String tag;

    /**
     * 内容
     */
    private String content;

    /**
     * 描述
     */
    private String description;


    public static final String TAG = "tag";

    public static final String CONTENT = "content";

    public static final String DESCRIPTION = "description";

    @Override
    protected Serializable pkVal() {
        return null;
    }

}
