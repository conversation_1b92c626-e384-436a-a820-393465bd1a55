package com.jd.jdt.joycreator.ae.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.jd.jdt.joycreator.ae.enums.ChatRoleEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 聊天记录明细引用
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_2943_chat_history_detail_excerpt")
public class ChatHistoryDetailExcerpt extends BaseEntity<ChatHistoryDetailExcerpt> {

    private static final long serialVersionUID = 1L;

    /**
     * 聊天记录明细
     */
    private Long chatHistoryDetailId;

    /**
     * 附件
     */
    private Long fileId;

    /**
     * 引用类型
     */
    private ChatRoleEnum msgType;

    /**
     * 文档内容
     */
    private String documentContent;

    /**
     * 文档文本内容
     */
    private String textContent;


    public static final String CHAT_HISTORY_DETAIL_ID = "chat_history_detail_id";

    public static final String FILE_ID = "file_id";

    public static final String MSG_TYPE = "msg_type";

    public static final String DOCUMENT_CONTENT = "document_content";

    public static final String TEXT_CONTENT = "text_content";

    @Override
    protected Serializable pkVal() {
        return null;
    }

}
