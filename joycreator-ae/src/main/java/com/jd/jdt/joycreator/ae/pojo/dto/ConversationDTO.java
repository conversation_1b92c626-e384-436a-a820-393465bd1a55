package com.jd.jdt.joycreator.ae.pojo.dto;

import lombok.Data;
import java.util.List;

@Data
public class ConversationDTO {
    private String userInput;
    private String conversationId;
    private List<ConversationMessage> history;
    
    @Data
    public static class ConversationMessage {
        private String role; // user or assistant
        private String content;
        private Long timestamp;
    }
} 