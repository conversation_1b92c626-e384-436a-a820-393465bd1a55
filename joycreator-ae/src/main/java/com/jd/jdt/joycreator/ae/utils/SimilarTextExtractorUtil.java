package com.jd.jdt.joycreator.ae.utils;


import java.util.*;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <p>
 * 字符串原文查找工具-最小编辑距离算法
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
public class SimilarTextExtractorUtil {


    public static void main(String[] args) {
        String A1 = "程）的操作效率与用户满意度。o无障碍访问：遵循WCAG 2.1标准，验证屏幕阅读器兼容性及键盘导航支持。7测试标准结果的验收需满足以下量化指标与定性要求，确保交付质量符合预期：功能测试通过标准o关键路径用例通过率100%，非关键路径通过率≥95%。-所有P0级缺陷（如数据丢失、核心功能失效）必须修复且复测通过，P1级缺陷解决率≥98%。2.性能测试通过标准o响应时间：普通操作2秒，复杂报表生成≤8秒（硬件配置：4核CPU/16GB内存）。o系统稳定性：72小时持续运行下内存泄漏≤3%，CPU平均占用率≤70%。3.测试通过";
        String B1 = ".o无障访问遵循WCAG2.1标准，验证屏幕阅我读器兼容及键盘导航支持。7测试标准结果的验收需满足以下量化指标与定要求";
        String s = extractMostSimilar(A1, B1);
        System.out.println(s);
        System.err.println("匹配内容长度:" + B1.length());
        System.err.println("匹配出的原文长度:" + s.length());
    }


    // q-gram 匹配常量
    private static final int Q_GRAM_SIZE = 3; // 可调整，根据文本特点选择合适的 q 值
    private static final int MIN_Q_GRAM_MATCHES = 5; // 潜在匹配区域所需的最小 q-gram 匹配数，可调整
    private static final int WINDOW_SIZE_MULTIPLIER = 2; // 潜在区域窗口大小是 B 长度的倍数，可调整

    public static String extractMostSimilar(String original, String matchStr) {
        // 标准化输入文本：移除换行符、中英文标点符号，转换为小写，并构建原始索引映射
        NormalizedText normalizedOriginal = normalize(original);
        NormalizedText normalizedMatchStr = normalize(matchStr);

        String processedA = normalizedOriginal.processedText;
        String processedB = normalizedMatchStr.processedText;

        int lenA = processedA.length();
        int lenB = processedB.length();

        // 定义基于 B 长度的候选子串最小/最大长度
        int minLen = Math.max(1, (int) (lenB * 0.8));
        int maxLen = (int) (lenB * 1.2);

        if (lenB == 0 || lenA < Q_GRAM_SIZE) { // 如果 A 长度小于 q-gram 大小，则返回空字符串
            return "";
        }

        // 1. 计算 B 的 q-grams 并建立索引
        Map<String, List<Integer>> bGramMap = new HashMap<>();
        for (int i = 0; i <= lenB - Q_GRAM_SIZE; i++) {
            String gram = processedB.substring(i, i + Q_GRAM_SIZE);
            bGramMap.computeIfAbsent(gram, k -> new ArrayList<>()).add(i);
        }

        // 2. 在 A 中查找匹配 q-gram 并记录匹配对
        List<int[]> matches = new ArrayList<>(); // 格式：int[]{a_start_index, b_start_index}
        for (int i = 0; i <= lenA - Q_GRAM_SIZE; i++) {
            String gram = processedA.substring(i, i + Q_GRAM_SIZE);
            if (bGramMap.containsKey(gram)) {
                for (int bIndex : bGramMap.get(gram)) {
                    matches.add(new int[]{i, bIndex});
                }
            }
        }

        // 如果 q-gram 匹配数不足，则返回空字符串
        // 阈值 MIN_Q_GRAM_MATCHES 可根据预期的匹配密度进行调整
        // 保留此检查，但下面的密度检查将作为区域识别的主要过滤器
        if (matches.size() < MIN_Q_GRAM_MATCHES) { // 如果匹配数小于设定的最小q-gram匹配数，则返回空字符串。
            return "";
        }

        // 3. 基于 q-gram 匹配密度识别潜在匹配区域
        // 根据 q-gram 匹配的起始位置创建 A 的密度数组
        int[] density = new int[lenA];
        for (int[] match : matches) {
            density[match[0]]++;
        }

        List<int[]> potentialRegions = new ArrayList<>(); // 格式：int[]{a_start, a_end}
        int currentRegionStart = -1;
        int densityThreshold = 2; // 调整后的密度阈值，初始允许较低密度区域

        for (int i = 0; i < lenA; i++) {
            if (density[i] > 0) { // 如果在此位置至少有一个匹配开始
                if (currentRegionStart == -1) {
                    currentRegionStart = i;
                }
            } else {
                // 如果密度下降，检查当前累积的区域是否重要
                if (currentRegionStart != -1) { // 如果密度下降，检查当前累积的区域是否重要
                    int regionEnd = i; // 区域结束索引
                    // 计算此潜在区域内的总 q-gram 匹配数
                    int totalRegionMatches = 0;
                    for (int j = currentRegionStart; j < regionEnd; j++) {
                        if (j < lenA) { // 确保索引在边界内
                            totalRegionMatches += density[j];
                        }
                    }

                    // 定义区域边界，围绕密集区域进行少量扩展
                    // 使用固定扩展量或 B 长度的比例
                    int startExpansion = Math.max(Q_GRAM_SIZE, lenB / WINDOW_SIZE_MULTIPLIER); // 起始位置的较大扩展
                    int endExpansion = Math.max(Q_GRAM_SIZE, lenB / (WINDOW_SIZE_MULTIPLIER * 2)); // 结束位置的当前扩展
                    int regionAStart = Math.max(0, currentRegionStart - startExpansion);
                    int regionAEnd = Math.min(lenA, regionEnd + endExpansion);

                    // 只添加总 q-gram 匹配数足够且区域足够大的区域
                    if (totalRegionMatches >= densityThreshold && (regionAEnd - regionAStart) >= minLen) {
                        potentialRegions.add(new int[]{regionAStart, regionAEnd});
                    }
                    currentRegionStart = -1;
                }
            }
        }

        // 如果最后一个潜在区域延伸到A的末尾，则处理该区域
        if (currentRegionStart != -1) {
            int regionEnd = lenA;
            int totalRegionMatches = 0;
            for (int j = currentRegionStart; j < regionEnd; j++) {
                if (j < lenA) { // 确保索引在边界内
                    totalRegionMatches += density[j];
                }
            }
            int startExpansion = Math.max(Q_GRAM_SIZE, lenB / WINDOW_SIZE_MULTIPLIER); // 起始位置的较大扩展
            int endExpansion = Math.max(Q_GRAM_SIZE, lenB / (WINDOW_SIZE_MULTIPLIER * 2)); // 结束位置的当前扩展
            int regionAStart = Math.max(0, currentRegionStart - startExpansion);
            int regionAEnd = Math.min(lenA, regionEnd + endExpansion);
            if (totalRegionMatches >= densityThreshold && (regionAEnd - regionAStart) >= minLen) {
                potentialRegions.add(new int[]{regionAStart, regionAEnd});
            }
        }

        // 如果未找到潜在区域
        if (potentialRegions.isEmpty()) { // 如果未找到潜在区域
            return "";
        }

        // 4. 在潜在区域内执行精确匹配并选择最佳结果
        // 移除候选列表，直接追踪最佳匹配
        // List<CandidateMatch> candidates = new ArrayList<>();

        // 初始化 minDistance 为一个非常大的值
        AtomicInteger minDistance = new AtomicInteger(Integer.MAX_VALUE);
        // 使用线程安全的容器或机制来更新最佳匹配
        // 为简单起见，我们可以同步对共享 CandidateMatch 对象的更新，
        // 或者让每个线程返回其最佳匹配，然后稍后选择全局最佳。
        // 我们将让每个线程返回其最佳匹配。

        // 配置线程池。使用固定大小的线程池，大小等于可用处理器数量。
        int numberOfProcessors = Runtime.getRuntime().availableProcessors();
        ExecutorService executor = Executors.newFixedThreadPool(numberOfProcessors);
        List<Future<CandidateMatch>> futures = new ArrayList<>();

        // 为每个潜在区域提交任务
        for (int[] region : potentialRegions) {
            Callable<CandidateMatch> task = () -> {
                int regionStart = region[0];
                int regionEnd = region[1];

                CandidateMatch localBestMatch = null;
                int localMinDistance = Integer.MAX_VALUE;

                // Iterate through all possible substrings within the region that are within the acceptable length range of B
                List<CandidateMatch> regionalCandidates = new ArrayList<>();
                for (int start = regionStart; start <= regionEnd - minLen; start++) {
                    for (int end = start + minLen - 1; end < Math.min(regionEnd, lenA); end++) {
                        // Ensure the substring length is within the max allowed length
                        if (end - start + 1 <= maxLen) {
                            String subA = processedA.substring(start, end + 1);

                            // 在计算编辑距离之前执行快速 q-gram 密度检查
                            int qGramMatchesInSubA = countQGramMatches(subA, bGramMap);
                            // Define a threshold for preliminary filtering - can be tuned
                            int preliminaryThreshold = MIN_Q_GRAM_MATCHES / 2; // 初步过滤的阈值（示例值，可调整）

                            if (qGramMatchesInSubA >= preliminaryThreshold) {
                                // 如果通过初步检查，添加到区域候选列表
                                regionalCandidates.add(new CandidateMatch(subA, start, end, -1)); // 初始距离设为-1
                            }
                        }
                    }
                }

                // 根据与 B 的长度差异对此区域内的候选进行排序
                Collections.sort(regionalCandidates, Comparator.comparingInt(c -> Math.abs(c.text.length() - lenB)));

                // 现在，计算排序后的候选的编辑距离，并进行剪枝
                for (CandidateMatch candidate : regionalCandidates) {
                    // 使用全局最小距离作为剪枝的阈值
                    int distance = editDistance(candidate.text, processedB, minDistance.get());
                    candidate.distance = distance; // 更新候选对象的距离

                    // 如果此候选优于当前最佳（无论是全局还是局部）
                    if (distance < minDistance.get()) {
                        // 原子地更新全局最小距离
                        minDistance.set(distance);
                        localMinDistance = distance;
                        localBestMatch = candidate; // 更新局部最佳匹配引用
                    } else if (distance == minDistance.get()) {
                        // 如果距离等于全局最小距离，应用位置优先级规则
                        if (localBestMatch == null || candidate.aStart < localBestMatch.aStart ||
                                (candidate.aStart == localBestMatch.aStart && candidate.aEnd < localBestMatch.aEnd)) {
                            localBestMatch = candidate; // 更新局部最佳匹配引用
                        }
                    } else if (distance < localMinDistance) { // 如果优于局部最小但不如全局最小，则更新局部最佳
                        localMinDistance = distance;
                        localBestMatch = candidate; // 更新局部最佳匹配引用在B的长度
                    } else if (distance == localMinDistance) {
                        // 如果距离等于局部最小距离，应用位置优先级规则
                        if (localBestMatch == null || candidate.aStart < localBestMatch.aStart ||
                                (candidate.aStart == localBestMatch.aStart && candidate.aEnd < localBestMatch.aEnd)) {
                            localBestMatch = candidate; // 更新局部最佳匹配引用
                        }
                    }
                }
                return localBestMatch; // 返回在此区域找到的最佳匹配
            };
            futures.add(executor.submit(task));
        }

        // 关闭线程池
        executor.shutdown();
        try {
            // 等待所有任务完成，设置超时以防无限等待
            executor.awaitTermination(1, TimeUnit.HOURS); // 可根据需要调整超时时间
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt(); // 恢复中断状态
            System.err.println("任务被中断: " + e.getMessage());
            return ""; // 如果中断，返回空字符串
        }

        // 从 Future 中收集结果并找到整体最佳匹配
        CandidateMatch overallBestMatch = null;
        int overallMinDistance = Integer.MAX_VALUE;

        for (Future<CandidateMatch> future : futures) {
            try {
                CandidateMatch regionalBestMatch = future.get();
                if (regionalBestMatch != null) {
                    if (regionalBestMatch.distance < overallMinDistance) {
                        overallMinDistance = regionalBestMatch.distance;
                        overallBestMatch = regionalBestMatch;
                    } else if (regionalBestMatch.distance == overallMinDistance) {
                        // 如果距离相等，应用位置优先级规则来选择整体最佳
                        if (overallBestMatch == null || regionalBestMatch.aStart < overallBestMatch.aStart ||
                                (regionalBestMatch.aStart == overallBestMatch.aStart && regionalBestMatch.aEnd < overallBestMatch.aEnd)) {
                            overallBestMatch = regionalBestMatch;
                        }
                    }
                }
            } catch (InterruptedException | ExecutionException e) {
                System.err.println("获取任务结果出错: " + e.getMessage());
                // 处理异常，可以记录日志或决定如何继续
            }
        }

        // --- 新增后处理逻辑 ---
        String result = overallBestMatch != null ?
                original.substring(
                        normalizedOriginal.originalIndices[overallBestMatch.aStart],
                        normalizedOriginal.originalIndices[overallBestMatch.aEnd] + 1
                ) : "";
        if (overallBestMatch == null || result.isEmpty()) {
            return result;
        }
        int end = normalizedOriginal.originalIndices[overallBestMatch.aEnd];
        StringBuilder sb = new StringBuilder(result);
        int origIdx = end + 1;
        // 只追加原文中紧跟的符号或空格（包括【】等所有符号）
        while (origIdx < original.length() && isPunctuationOrSpace(original.charAt(origIdx))) {
            sb.append(original.charAt(origIdx));
            origIdx++;
        }
        return sb.toString();
    }

    // 辅助类，用于存储候选匹配
    private static class CandidateMatch {
        String text; // 匹配的文本内容
        int aStart;
        int aEnd; // 匹配在A中的结束位置
        int distance;

        CandidateMatch(String text, int aStart, int aEnd, int distance) {
            this.text = text;
            this.aStart = aStart;
            this.aEnd = aEnd;
            this.distance = distance;
        }
    }

    // 计算Levenshtein编辑距离（优化空间复杂度，使用滚动数组）
    // 添加了用于提前退出的阈值
    private static int editDistance(String s1, String s2, int threshold) {
        // 使用滚动数组将空间复杂度优化到 O(n)
        int[] prevRow = new int[s2.length() + 1];
        int[] currRow = new int[s2.length() + 1];

        // 初始化第一行
        for (int j = 0; j <= s2.length(); j++) {
            prevRow[j] = j;
        }

        for (int i = 1; i <= s1.length(); i++) {
            currRow[0] = i;
            char c1 = s1.charAt(i - 1);
            int minInCurrRow = Integer.MAX_VALUE; // 跟踪当前行中的最小值以便提前退出

            for (int j = 1; j <= s2.length(); j++) {
                char c2 = s2.charAt(j - 1);
                if (c1 == c2) {
                    currRow[j] = prevRow[j - 1];
                } else {
                    currRow[j] = 1 + Math.min(
                            Math.min(prevRow[j],    // 删除
                                    currRow[j - 1]), // 插入
                            prevRow[j - 1]         // 替换
                    );
                }
                minInCurrRow = Math.min(minInCurrRow, currRow[j]);
            }
            // 如果当前行中的最小距离已经大于阈值，
            // 则不可能获得小于或等于阈值的距离，可以提前退出。
            if (minInCurrRow > threshold) {
                return threshold + 1; // 返回大于阈值的值表示结果更差
            }

            // 滚动数组
            int[] temp = prevRow;
            prevRow = currRow;
            currRow = temp;
        }

        return prevRow[s2.length()];
    }

    // 辅助方法，用于计算子字符串与 B 的 q-gram 映射中的匹配数量
    private static int countQGramMatches(String subA, Map<String, List<Integer>> bGramMap) {
        if (subA.length() < Q_GRAM_SIZE) {
            return 0;
        }
        int count = 0;
        // 使用 Set 计算 subA 中存在于 bGramMap 的唯一 q-grams 数量
        Set<String> matchedQGrams = new HashSet<>();

        for (int i = 0; i <= subA.length() - Q_GRAM_SIZE; i++) {
            String gram = subA.substring(i, i + Q_GRAM_SIZE);
            if (bGramMap.containsKey(gram)) {
                matchedQGrams.add(gram);
                // 或者，计算所有出现次数:
                // count += bGramMap.get(gram).size();
            }
        }
        // 返回唯一匹配 q-grams 的数量
        return matchedQGrams.size();
        // 如果计算所有出现次数:
        // return count;
    }

    // 辅助类，用于存储处理后的文本和原始索引映射
    private static class NormalizedText {
        String processedText;
        int[] originalIndices; // Maps index in processedText to index in original text

        NormalizedText(String processedText, int[] originalIndices) {
            this.processedText = processedText;
            this.originalIndices = originalIndices;
        }
    }

    // 辅助方法，用于标准化文本：移除换行符、中英文标点符号，转换为小写，并构建原始索引映射
    private static NormalizedText normalize(String text) {
        StringBuilder processed = new StringBuilder();
        List<Integer> indicesMap = new ArrayList<>(); // Store original index for each char in processed

        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);

            // 只保留字母和数字进行比较，忽略其他字符（包括换行符、空格、标点符号）
            if (Character.isLetterOrDigit(c)) {
                processed.append(Character.toLowerCase(c));
                indicesMap.add(i);
            }
        }

        int[] originalIndicesArray = new int[indicesMap.size()];
        for (int i = 0; i < indicesMap.size(); i++) {
            originalIndicesArray[i] = indicesMap.get(i);
        }

        return new NormalizedText(processed.toString(), originalIndicesArray);
    }

    // 判断是否为标点或空白字符（包括中文符号如【】）
    private static boolean isPunctuationOrSpace(char c) {
        return Character.isWhitespace(c) || (!Character.isLetterOrDigit(c) && (c <= 0x7E || Character.getType(c) == Character.OTHER_PUNCTUATION || Character.getType(c) == Character.START_PUNCTUATION || Character.getType(c) == Character.END_PUNCTUATION));
    }
}