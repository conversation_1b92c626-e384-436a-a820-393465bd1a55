package com.jd.jdt.joycreator.ae.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 推荐管理-推荐管理配置
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_2943_suggest_govern")
public class SuggestGovern extends BaseEntity<SuggestGovern> {

    private static final long serialVersionUID = 1L;

    /**
     * 推荐分类
     */
    private String category;

    /**
     * 推荐图标
     */
    private String icon;

    /**
     * 是否启用
     */
    private String isEnabled;

    /**
     * 推荐优先级
     */
    private BigDecimal priority;

    /**
     * 预置提示词
     */
    private String presetPrompt;

    /**
     * 图标背景色
     */
    private String background;

    /**
     * 推荐分类枚举
     */
    private String recommendationCategoryEnum;

    /**
     * 推荐描述
     */
    private String recommendationDescription;


    public static final String CATEGORY = "category";

    public static final String ICON = "icon";

    public static final String IS_ENABLED = "is_enabled";

    public static final String PRIORITY = "priority";

    public static final String PRESET_PROMPT = "preset_prompt";

    public static final String BACKGROUND = "background";

    public static final String RECOMMENDATION_CATEGORY_ENUM = "recommendation_category_enum";

    public static final String RECOMMENDATION_DESCRIPTION = "recommendation_description";

    @Override
    protected Serializable pkVal() {
        return null;
    }

}
