package com.jd.jdt.joycreator.ae.generator;

import com.baomidou.mybatisplus.annotation.DbType;

/**
 * 用于生产MBG的代码
 * Created by macro on 2018/4/26.
 */
public class Generator {
    public static void main(String[] args) throws Exception {
        DbType dbType = DbType.MYSQL;
        String dbUrl = "**********************************************************************************************************";
        String username = "c_xingkong_lcKEx";
        String password = "Y7k6o11DO3v7l2NE604ZiLxV";
        String driver = "com.mysql.cj.jdbc.Driver";
        // 表前缀，生成的实体类，不含前缀
        String[] tablePrefixes = {"t_2943_"};
        // 表名，为空，生成所有的表 可以指定 表名字
        String[] tableNames = {"t_2943_prompt_words_config"};
        // 表名，为空，生成所有的表 可以指定 表名字
        //  String[] tableNames = {"lcdp_material_collection","lcdp_material_collection_relation","lcdp_material_store","lcdp_material_version"};
        // 字段前缀
        String[] fieldPrefixes = {""};
        // 基础包名
        String packageName = "com.jd.jdt.joycreator.ae";
        // 模块名
        String module = "joycreator-ae";
        // 作者
        String author = "zhaohan25";
        // 是否生成 Mapper  Service  true 生成
        boolean config = true;
        MBGCommonUtils.execute(dbType, dbUrl, username, password, driver, tablePrefixes, tableNames, packageName, fieldPrefixes, module, config, author);
    }
}
