package com.jd.jdt.joycreator.ae.dao.plus;

import com.jd.jdt.joycreator.ae.entity.ChatHistoryDetailExcerpt;
import com.jd.jdt.joycreator.ae.dao.IBaseDao;

import java.util.List;

/**
 * <p>
 * 聊天记录明细引用 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
public interface IChatHistoryDetailExcerptMapper extends IBaseDao<ChatHistoryDetailExcerpt> {

    List<ChatHistoryDetailExcerpt> listByChatHistoryDetailId(Long chatHistoryDetailId);
}
