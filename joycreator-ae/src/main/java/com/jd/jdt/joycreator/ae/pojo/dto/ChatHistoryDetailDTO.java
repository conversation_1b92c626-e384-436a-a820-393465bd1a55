package com.jd.jdt.joycreator.ae.pojo.dto;

import com.jd.jdt.joycreator.ae.entity.ChatHistoryDetailExcerpt;
import com.jd.jdt.joycreator.ae.entity.ChatHistoryDetailTemplate;
import com.jd.jdt.joycreator.ae.enums.ChatRoleEnum;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 聊天记录明细
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-24
 */
@Data
public class ChatHistoryDetailDTO {


    /**
     * 聊天记录ID
     */
    private Long chatHistoryId;

    /**
     * 业务编号
     */
    private String businessNo;

    /**
     * 角色
     */
    private ChatRoleEnum chatRole;

    /**
     * 内容
     */
    private String chatContent;

    /**
     * 附件ID
     */
    private Long fileId;


    /**
     * 聊天记录明细模版
     */
    private List<ChatHistoryDetailTemplate> chatHistoryDetailTemplateList;

    /**
     * 聊天记录明细引用
     */
    private List<ChatHistoryDetailExcerpt> chatHistoryDetailExcerptList;

}
