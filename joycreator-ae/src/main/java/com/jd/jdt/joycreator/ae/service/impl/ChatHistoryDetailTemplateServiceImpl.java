package com.jd.jdt.joycreator.ae.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jd.jdt.joycreator.ae.dao.plus.IChatHistoryDetailTemplateMapper;
import com.jd.jdt.joycreator.ae.entity.ChatHistoryDetailTemplate;
import com.jd.jdt.joycreator.ae.pojo.dto.ChatHistoryOperationExpandDTO;
import com.jd.jdt.joycreator.ae.service.ChatHistoryDetailTemplateService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 聊天记录明细模版 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-24
 */
@Service
public class ChatHistoryDetailTemplateServiceImpl implements ChatHistoryDetailTemplateService {

    @Resource
    private IChatHistoryDetailTemplateMapper iChatHistoryDetailTemplateMapper;

    @Override
    public List<ChatHistoryDetailTemplate> listChatHistoryDetailTemplateByChatHistoryDetailId(Long chatHistoryDetailId) {
        LambdaQueryWrapper<ChatHistoryDetailTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChatHistoryDetailTemplate::getChatHistoryDetailId, chatHistoryDetailId);
        queryWrapper.orderByAsc(ChatHistoryDetailTemplate::getId);
        return iChatHistoryDetailTemplateMapper.list(queryWrapper);
    }

    @Override
    public Boolean confirmTemplateLibrary(ChatHistoryOperationExpandDTO chatHistoryOperationExpandDTO) {
        return iChatHistoryDetailTemplateMapper.confirmTemplateLibraryByTemplateId(chatHistoryOperationExpandDTO.getBusinessNo(), chatHistoryOperationExpandDTO.getTemplateId());
    }

}
