package com.jd.jdt.joycreator.ae.dao.plus.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jd.jdt.joycreator.ae.entity.IpmsUserRole;
import com.jd.jdt.joycreator.ae.dao.mapper.IpmsUserRoleMapper;
import com.jd.jdt.joycreator.ae.dao.plus.IIpmsUserRoleMapper;
import com.jd.jdt.joycreator.ae.dao.plus.IBaseMapperImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 用户与角色 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Service
public class IIpmsUserRoleMapperImpl extends IBaseMapperImpl<IpmsUserRoleMapper, IpmsUserRole> implements IIpmsUserRoleMapper {

    @Override
    public List<IpmsUserRole> getIpmsUserRoleByErp(String userName) {
        LambdaQueryWrapper<IpmsUserRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IpmsUserRole::getUser, userName);
        return list(queryWrapper);
    }
}
