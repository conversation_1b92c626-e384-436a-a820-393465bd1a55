package com.jd.jdt.joycreator.ae.pojo.dto;

import com.jd.jdt.joycreator.ae.enums.OfficeConvertEnum;
import lombok.Data;

/**
 * <p>
 * OfficeConvertDTO
 * </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @since 2025-06-18
 */
@Data
public class OfficeConvertDTO {

    /**
     * 转换类型
     */
    private OfficeConvertEnum officeConvert;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 源文件
     */
    byte[] byteArray;

}
