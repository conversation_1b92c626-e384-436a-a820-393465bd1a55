package com.jd.jdt.joycreator.ae.dao.plus.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.jd.jdt.joycreator.ae.entity.ChatHistoryDetail;
import com.jd.jdt.joycreator.ae.dao.mapper.ChatHistoryDetailMapper;
import com.jd.jdt.joycreator.ae.dao.plus.IChatHistoryDetailMapper;
import com.jd.jdt.joycreator.ae.dao.plus.IBaseMapperImpl;
import com.jd.jdt.joycreator.ae.enums.ChatRoleEnum;
import com.jd.jdt.joycreator.ae.enums.SelectedEnum;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 聊天记录明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
@Service
public class IChatHistoryDetailMapperImpl extends IBaseMapperImpl<ChatHistoryDetailMapper, ChatHistoryDetail> implements IChatHistoryDetailMapper {


    @Override
    public List<ChatHistoryDetail> listChatHistoryDetailBySessionId(Long chatHistoryId) {
        LambdaQueryWrapper<ChatHistoryDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChatHistoryDetail::getChatHistoryId, chatHistoryId);
        queryWrapper.orderByAsc(ChatHistoryDetail::getId);
        return list(queryWrapper);
    }

    @Override
    public List<ChatHistoryDetail> listChatHistoryDetailByChatHistoryId(Long chatHistoryId) {
        LambdaQueryWrapper<ChatHistoryDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChatHistoryDetail::getChatHistoryId, chatHistoryId);
        queryWrapper.eq(ChatHistoryDetail::getChatRole, ChatRoleEnum.TEMPLATE_LIBRARY);
        queryWrapper.orderByAsc(ChatHistoryDetail::getId);
        return list(queryWrapper);
    }

    @Override
    public ChatHistoryDetail chatHistoryDetailTemplateByLimitOne(Long chatHistoryId) {
        LambdaQueryWrapper<ChatHistoryDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChatHistoryDetail::getChatHistoryId, chatHistoryId);
        queryWrapper.orderByAsc(ChatHistoryDetail::getId);
        queryWrapper.last("LIMIT 1");
        return getOne(queryWrapper);
    }

    @Override
    public Boolean delByChatHistoryId(Long chatHistoryId) {
        LambdaQueryWrapper<ChatHistoryDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChatHistoryDetail::getChatHistoryId, chatHistoryId);
        return remove(queryWrapper);
    }

    @Override
    public Boolean confirmDoccontentRewriteBybusinessNo(String businessNo) {
        LambdaUpdateWrapper<ChatHistoryDetail> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ChatHistoryDetail::getBusinessNo, businessNo)
                .set(ChatHistoryDetail::getSelected, SelectedEnum.Y);
        return update(updateWrapper);
    }

    @Override
    public ChatHistoryDetail getChatHistoryDetailByBusinessNo(String businessNo) {
        LambdaQueryWrapper<ChatHistoryDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChatHistoryDetail::getBusinessNo, businessNo);
        return getOne(queryWrapper);
    }

    @Override
    public Boolean updateChatContent(String businessNo, String chatContent) {
        LambdaUpdateWrapper<ChatHistoryDetail> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ChatHistoryDetail::getBusinessNo, businessNo)
                .set(ChatHistoryDetail::getChatContent, chatContent)
                .set(ChatHistoryDetail::getSelected, SelectedEnum.Y);
        return update(updateWrapper);
    }
}
