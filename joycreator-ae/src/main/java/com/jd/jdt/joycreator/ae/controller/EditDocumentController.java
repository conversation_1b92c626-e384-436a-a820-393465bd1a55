package com.jd.jdt.joycreator.ae.controller;

import com.jd.jdt.app4s.component.common.api.entity.ResponseResult;
import com.jd.jdt.joycreator.ae.pojo.dto.EditDocumentDTO;
import com.jd.jdt.joycreator.ae.pojo.vo.EditDocumentVO;
import com.jd.jdt.joycreator.ae.service.EditDocumentService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;


/**
 * <p>
 * 编辑器文档数据 服务接口
 * </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @since 2025-04-22
 */
@Log4j2
@RestController
@RequestMapping("/api/v1/edit-document")
public class EditDocumentController {

    @Autowired
    private EditDocumentService editDocumentService;


    @PostMapping("/save")
    public ResponseResult<Long> save(@RequestBody() EditDocumentDTO editDocumentDTO) {
        return ResponseResult.success(editDocumentService.save(editDocumentDTO));
    }

    @GetMapping("/dtl/{sessionId}")
    public ResponseResult<EditDocumentVO> dtl(@PathVariable("sessionId") String sessionId) {
        return ResponseResult.success(editDocumentService.dtl(sessionId));
    }

    /**
     * word转html
     *
     * @param fileId 附件ID
     * @return
     * @throws IOException
     */
    @GetMapping("/convert-html/{fileId}")
    public ResponseResult<String> convertHtml(@PathVariable("fileId") Long fileId) throws IOException {
        return ResponseResult.success("success", editDocumentService.convertHtml(fileId));
    }

    /**
     * 获取全部创作文档列表
     *
     * @return
     * @throws IOException
     */
    @GetMapping("/list")
    public ResponseResult<List<EditDocumentVO>> list() throws IOException {
        return ResponseResult.success(editDocumentService.list());
    }

    /**
     * 根据sessionId删除文档
     *
     * @return
     * @throws IOException
     */
    @GetMapping("/del/{sessionId}")
    public ResponseResult<Boolean> del(@PathVariable("sessionId") String sessionId) {
        return ResponseResult.success(editDocumentService.del(sessionId));
    }

}
