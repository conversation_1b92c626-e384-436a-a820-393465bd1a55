package com.jd.jdt.joycreator.ae.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

/**
 * <p>
 * 字符串切分工具类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
public class RandomStringSplitter {

    /**
     * 将大字符串随机切割成1、2、3、4个字符为一组的字符串集合
     *
     * @param str 输入字符串
     * @return 切割后的字符串集合
     */
    public static List<String> splitIntoRandomGroups(String str) {
        // 处理空字符串情况
        if (str == null || str.isEmpty()) {
            return new ArrayList<>(); // 返回空集合
        }

        List<String> result = new ArrayList<>();
        int start = 0;
        int totalLength = str.length();

        while (start < totalLength) {
            int remaining = totalLength - start;
            // 确定当前可切割的最大长度（不超过4且不超过剩余字符数）
            int maxPossibleLength = Math.min(4, remaining);
            // 生成1到maxPossibleLength之间的随机长度（包含两端）
            int currentLength = ThreadLocalRandom.current().nextInt(1, maxPossibleLength + 1);
            // 截取子串并添加到结果集合
            result.add(str.substring(start, start + currentLength));
            // 更新起始位置
            start += currentLength;
        }

        return result; // 直接返回List<String>
    }

    // 测试示例
    public static void main(String[] args) {
        String input = "**2. 功能需求**\n" +
                "-----------\n" +
                "\n" +
                "### **2.1 用户管理**\n" +
                "\n" +
                "#### **2.1.1 用户角色**\n" +
                "\n" +
                "系统应支持多种用户角色，以满足不同层次的项目管理需求。主要角色包括但不限于管理员、项目经理、项目成员及外部协作人员。  \n" +
                "\n" +
                "* **管理员**：负责创建和管理用户账户，分配角色以及设置权限。\n" +
                "\n" +
                "* **项目经理**：负责制定项目计划、分配任务监督项目进度。\n" +
                "\n" +
                "* **项目成员**：负责执行分配的任务，提交进度报告以及项目沟通。\n" +
                "\n" +
                "* **外部协作人员**：包括客户或第三方供应商，主要作用是查看项目信息和反馈。\n" +
                "\n" +
                "#### **2.1.2 权限控制**\n" +
                "\n" +
                "实现基于角色的权限控制模型，确保用户能够根据其角色访问相应的数据和功能。  \n" +
                "\n" +
                "* 各角色权限需预设，并通过管理员进行调。\n" +
                "\n" +
                "* 权限包括但不限于：查看、编辑、删除项目、任务以及文档。"; // 长度15的示例字符串
        List<String> output = splitIntoRandomGroups(input);

        // 打印结果
//        System.out.println("原字符串长度: " + input.length());
        for (String s : output) {
            System.err.println(s);
        }
//        System.out.println("\n总长度验证: " + String.join("", output).length() + "（应等于原字符串长度）");
    }
}
