package com.jd.jdt.joycreator.ae.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jd.jdt.joycreator.ae.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 用户与角色
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_2943_ipms_user_role")
public class IpmsUserRole extends BaseEntity<IpmsUserRole> {

    private static final long serialVersionUID = 1L;

    /**
     * 角色
     */
    private String role;

    /**
     * 用户
     */
    private String user;


    public static final String ROLE = "role";

    public static final String USER = "user";

    @Override
    protected Serializable pkVal() {
        return null;
    }

}
