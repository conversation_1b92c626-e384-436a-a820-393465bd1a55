package com.jd.jdt.joycreator.ae.pojo.dto;

import lombok.Data;

import java.util.List;

/**
 * <p>
 * 字典配置DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Data
public class DictDTO {

    private Long id;

    /**
     * 字典编码
     */
    private String dictCode;

    /**
     * 是否启用
     */
    private String enabled;

    /**
     * 字典描述
     */
    private String dictDesc;

    /**
     * 明细列表
     */
    private List<DictDtlDTO> dictDtlList;

}
