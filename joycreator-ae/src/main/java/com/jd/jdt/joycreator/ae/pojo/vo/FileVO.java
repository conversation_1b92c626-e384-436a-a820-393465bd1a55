package com.jd.jdt.joycreator.ae.pojo.vo;

import com.jd.jdt.joycreator.ae.enums.FileTypeEnum;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 文件-业务附件VO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
@Data
public class FileVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 文件名
     */
    private String name;

    /**
     * 文件后缀
     */
    private String fileSuffix;

    /**
     * 文件类型
     */
    private FileTypeEnum fileType;

    /**
     * 文件大小
     */
    private BigDecimal fileSize;

    /**
     * 外网链接
     */
    private String wanUrl;

    /**
     * 内网链接
     */
    private String lanUrl;

}
