package com.jd.jdt.joycreator.ae.service.impl;

import com.jd.jdt.app4s.component.common.api.exception.BussinessBizException;
import com.jd.jdt.joybuilder.permission.entity.ResponseResult;
import com.jd.jdt.joycreator.ae.config.OssConfig;
import com.jd.jdt.joycreator.ae.pojo.dto.FileOriginDTO;
import com.jd.jdt.joycreator.ae.pojo.dto.FileStrDTO;
import com.jd.jdt.joycreator.ae.pojo.dto.UploadFileDTO;
import com.jd.jdt.joycreator.ae.rpc.feign.OfficeConvertService;
import com.jd.jdt.joycreator.ae.service.FileConvertService;
import com.jd.jdt.joycreator.ae.service.FileService;
import com.jd.jdt.joycreator.ae.utils.JoyCreatorUtil;
import com.jd.jsf.gd.util.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.Objects;
import java.util.UUID;

/**
 * <p>
 * 文件转换服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
@Log4j2
@Service
public class FileConvertServiceImpl implements FileConvertService {

    @Autowired
    private OssConfig ossConfig;
    @Autowired
    private FileService fileService;
    @Autowired
    private OfficeConvertService officeConvertService;
    @Autowired
    private JoyCreatorUtil joyCreatorUtil;


    @Override
    public UploadFileDTO convertByStr(FileStrDTO fileStrDTO) {
        log.info("文件转换，fileStrDTO:{}", JsonUtils.toJSONString(fileStrDTO));
        if (Objects.isNull(fileStrDTO) || StringUtils.isBlank(fileStrDTO.getFileContents()) || Objects.isNull(fileStrDTO.getPanDocConvertType())) {
            throw new BussinessBizException("请求内容为空！");
        }
        byte[] byteArray = fileStrDTO.getFileContents().getBytes();
        String uuidString = UUID.randomUUID().toString();
        String fileName = uuidString + "." + fileStrDTO.getPanDocConvertType().getSourceType();
        FileOriginDTO fileOriginDTO = new FileOriginDTO();
        fileOriginDTO.setPanDocConvertType(fileStrDTO.getPanDocConvertType());
        fileOriginDTO.setByteArray(byteArray);
        fileOriginDTO.setOriginName(fileName);
        ResponseResult<UploadFileDTO> responseResult = officeConvertService.convert(fileOriginDTO);
        log.info("文件转换，responseResult:{}", JsonUtils.toJSONString(responseResult));
        if (Objects.isNull(responseResult) || !responseResult.isSuccess()) {
            throw new BussinessBizException("文件转换失败");
        }
        UploadFileDTO uploadFileNewDTO = responseResult.getData();
        if (Objects.isNull(uploadFileNewDTO)) {
            return uploadFileNewDTO;
        }
        uploadFileNewDTO.setFileLanUrl(joyCreatorUtil.inteProtocol(uploadFileNewDTO.getFileLanUrl()));
        uploadFileNewDTO.setFileWanUrl(joyCreatorUtil.inteProtocol(uploadFileNewDTO.getFileWanUrl()));
        return responseResult.getData();
    }

    public UploadFileDTO convertByStr2(FileStrDTO fileStrDTO) {
        log.info("文件转换，fileStrDTO:{}", JsonUtils.toJSONString(fileStrDTO));
        if (Objects.isNull(fileStrDTO) || StringUtils.isBlank(fileStrDTO.getFileContents()) || Objects.isNull(fileStrDTO.getPanDocConvertType())) {
            throw new BussinessBizException("请求内容为空！");
        }
        byte[] bytes = fileStrDTO.getFileContents().getBytes();
        InputStream inputStream = new ByteArrayInputStream(bytes);
        String uuidString = UUID.randomUUID().toString();
        String fileName = uuidString + "." + fileStrDTO.getPanDocConvertType().getSourceType();
        UploadFileDTO uploadFileDTO = fileService.uploadFile(fileName, inputStream, "application/" + fileStrDTO.getPanDocConvertType().getSourceType());
        log.info("文件转换，uploadFileDTO:{}", JsonUtils.toJSONString(uploadFileDTO));
        FileOriginDTO fileOriginDTO = new FileOriginDTO();
        fileOriginDTO.setOriginName(uploadFileDTO.getFileName());
        fileOriginDTO.setPanDocConvertType(fileStrDTO.getPanDocConvertType());
        fileOriginDTO.setBucket(ossConfig.getBucket());
        ResponseResult<UploadFileDTO> responseResult = officeConvertService.convert(fileOriginDTO);
        log.info("文件转换，responseResult:{}", JsonUtils.toJSONString(responseResult));
        if (Objects.isNull(responseResult) || !responseResult.isSuccess()) {
            throw new BussinessBizException("文件转换失败");
        }
        UploadFileDTO uploadFileNewDTO = responseResult.getData();
        if (Objects.isNull(uploadFileNewDTO)) {
            return uploadFileNewDTO;
        }
        uploadFileNewDTO.setFileLanUrl(joyCreatorUtil.inteProtocol(uploadFileNewDTO.getFileLanUrl()));
        uploadFileNewDTO.setFileWanUrl(joyCreatorUtil.inteProtocol(uploadFileNewDTO.getFileWanUrl()));
        return responseResult.getData();
    }


}
