package com.jd.jdt.joycreator.ae.service;

import com.jd.jdt.joycreator.ae.entity.TemplateLibrary;
import com.jd.jdt.joycreator.ae.enums.OfficeConvertEnum;
import com.jd.jdt.joycreator.ae.pojo.dto.AutofillExtractionDTO;
import com.jd.jdt.joycreator.ae.pojo.dto.ChatHistoryDetailDTO;
import com.jd.jdt.joycreator.ae.pojo.dto.DocumentAnalysisDTO;
import com.jd.jdt.joycreator.ae.pojo.dto.ToolDtlDTO;
import com.jd.jdt.joycreator.ae.pojo.vo.TemplateLibraryDtlVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Queue;

/**
 * <p>
 * 模板库 服务
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
public interface TemplateLibraryService {

    /**
     * 模版库列表查询
     *
     * @param tag
     * @param name
     * @return
     */
    List<TemplateLibrary> listTemplateLibraryByTag(String tag, String name);

    /**
     * d
     * 根据ID查询模版数据
     *
     * @param id
     * @return
     */
    TemplateLibrary getById(Long id);


    Boolean buildTemplateLibraryBuffer(Queue<Object> contentBuffer, Queue<ChatHistoryDetailDTO> chatHistoryQueue, ToolDtlDTO toolDtlDTO, String messagesStr);

    /**
     * 模版上传
     *
     * @param file
     * @param tag
     * @param description
     * @return
     * @throws Exception
     */
    Long updateTemplateLibrary(MultipartFile file, String tag, String description) throws Exception;

    /**
     * 模版上传
     *
     * @param file
     * @param officeConvert
     * @param tag
     * @param description
     * @return
     */
    Long uploadTemplateLibrary(MultipartFile file, OfficeConvertEnum officeConvert, String tag, String description);

    /**
     * 文档分析
     *
     * @param documentAnalysisDTO
     * @return
     */
    TemplateLibraryDtlVO documentAnalysis(DocumentAnalysisDTO documentAnalysisDTO);
}
