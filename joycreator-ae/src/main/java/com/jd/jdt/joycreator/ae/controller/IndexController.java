package com.jd.jdt.joycreator.ae.controller;

import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @version 1.0
 * @date
 */
@Log4j2
@Controller
public class IndexController {
    /**
     * 渲染器的静态资源暴露
     *
     * @return
     */
    @RequestMapping(value = {"/", "/{path:^(?!api|index.js|__static).*$}/**"})
    public String index(HttpServletRequest request, HttpServletResponse httpServletResponse) throws IOException {
        String path = request.getRequestURI();
        if (!path.contains("__static")) {
            return "index";
        }
        String regex = ".*?(__static.*)";
        String replacement = "/$1";
        String newPath = path.replaceAll(regex, replacement);
        return "forward:" + newPath;
    }
}
