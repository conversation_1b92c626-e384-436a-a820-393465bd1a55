package com.jd.jdt.joycreator.ae.utils;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.util.DateUtils;
import com.jd.jdt.joycreator.ae.cache.RedisHelper;
import com.jd.jdt.joycreator.ae.dao.plus.IIpmsUserRoleMapper;
import com.jd.jdt.joycreator.ae.entity.IpmsUserRole;
import com.jd.jdt.joycreator.ae.enums.JoyCreateBusinessTypeEnum;
import com.jd.jdt.joycreator.ae.pojo.dto.DocumentReplaceDTO;
import com.jd.jsf.gd.util.JsonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component
public class JoyCreatorUtil {

    @Autowired
    private RedisHelper redisHelper;
    @Autowired
    private IIpmsUserRoleMapper iIpmsUserRoleMapper;

    //位数
    private static int num = 5;
    /**
     * 只有后两位年月日的日期
     */
    public static final String LAST_YEAR_MONTH_DATE = "yyyyMMdd";

    /**
     * 获取编号-业务前缀+十位数字组合(yyMMdd+5位自增序列)
     *
     * @param createBusinessType
     * @return
     */
    public String getBusinessNo(JoyCreateBusinessTypeEnum createBusinessType) {
        String businessValue = "";
        if (null != createBusinessType) {
            businessValue = createBusinessType.getValue();
        }

        String format = DateUtils.format(new Date(), LAST_YEAR_MONTH_DATE);
        Long incr = redisHelper.getIncr(businessValue + format);
        return new StringBuilder(businessValue).append(format).append(autoGenericCode(incr)).toString();
    }

    /**
     * 不够位数的在前面补0，保留num的长度位数字
     *
     * @param code
     * @return
     */
    public String autoGenericCode(Long code) {
        return String.format("%0" + num + "d", code);
    }

    public String inteProtocol(String fileUrl) {
        if (StringUtils.isEmpty(fileUrl)) {
            return fileUrl;
        }
        String https = "https:";
        String http = "http:";
        if (fileUrl.contains(https)) {
            return fileUrl.replace(https, "");
        }
        if (fileUrl.contains(http)) {
            return fileUrl.replace(http, "");
        }
        return fileUrl;
    }

    /**
     * 解析非标准JSON结构并转换为DocumentReplaceDTO对象
     * 可以处理JSON前后有其他文本的情况
     *
     * @param content 包含JSON结构的文本内容
     * @return DocumentReplaceDTO对象，如果解析失败则返回null
     */
    public DocumentReplaceDTO parseDocumentReplaceJson(String content) {
        if (StringUtils.isEmpty(content)) {
            return null;
        }

        try {
            // 尝试直接从文本中提取JSON对象
            String jsonStr = extractJsonFromText(content);
            if (jsonStr != null) {
                // 使用Fastjson解析JSON
                JSONObject jsonObject = JSON.parseObject(jsonStr);
                
                // 创建并填充DocumentReplaceDTO对象
                DocumentReplaceDTO dto = new DocumentReplaceDTO();
                dto.setType(jsonObject.getString("type"));
                
                // 处理不同格式的JSON结构
                if (jsonObject.containsKey("anchor")) {
                    dto.setAnchor(jsonObject.getString("anchor"));
                }
                
                // 处理oldContent/old字段
                if (jsonObject.containsKey("oldContent")) {
                    dto.setOldContent(jsonObject.getString("oldContent"));
                } else if (jsonObject.containsKey("old")) {
                    dto.setOldContent(jsonObject.getString("old"));
                }
                
                // 处理newContent/new字段
                if (jsonObject.containsKey("newContent")) {
                    dto.setNewContent(jsonObject.getString("newContent"));
                } else if (jsonObject.containsKey("new")) {
                    dto.setNewContent(jsonObject.getString("new"));
                }
                
                return dto;
            }
        } catch (Exception e) {
            // 记录异常信息，但不抛出异常
            System.err.println("解析DocumentReplaceDTO失败: " + e.getMessage());
        }
        
        return null;
    }
    
    /**
     * 从文本中提取JSON对象字符串
     *
     * @param text 包含JSON的文本
     * @return JSON字符串，如果未找到则返回null
     */
    private String extractJsonFromText(String text) {
        try {
            // 使用正则表达式查找JSON对象
            // 匹配以{开始，以}结束的内容，中间包含任意字符
            String regex = "\\{[^\\{\\}]*((\\{[^\\{\\}]*\\})[^\\{\\}]*)*\\}";
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(regex);
            java.util.regex.Matcher matcher = pattern.matcher(text);
            
            if (matcher.find()) {
                return matcher.group();
            }
        } catch (Exception e) {
            System.err.println("提取JSON失败: " + e.getMessage());
        }
        
        return null;
    }

    public boolean isAdmin(String userName) {
        List<IpmsUserRole> ipmsUserRoleList = iIpmsUserRoleMapper.getIpmsUserRoleByErp(userName);
        if (CollectionUtils.isEmpty(ipmsUserRoleList)) {
            return Boolean.FALSE;
        }
        return ipmsUserRoleList.stream().anyMatch(i -> "admin".equals(i.getRole()));
    }
}
