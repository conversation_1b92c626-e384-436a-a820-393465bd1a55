package com.jd.jdt.joycreator.ae.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.jd.jdt.joycreator.ae.enums.ChatRoleEnum;
import com.jd.jdt.joycreator.ae.enums.SelectedEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 聊天记录明细
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_2943_chat_history_detail")
public class ChatHistoryDetail extends BaseEntity<ChatHistoryDetail> {

    private static final long serialVersionUID = 1L;

    /**
     * 聊天记录ID
     */
    private Long chatHistoryId;

    /**
     * 角色
     */
    private ChatRoleEnum chatRole;

    /**
     * 内容
     */
    private String chatContent;

    /**
     * 业务唯一编号
     */
    private String businessNo;

    /**
     * 是否选中
     */
    private SelectedEnum selected;

    /**
     * 附件
     */
    private Long fileId;


    public static final String CHAT_HISTORY_ID = "chat_history_id";

    public static final String CHAT_ROLE = "chat_role";

    public static final String CHAT_CONTENT = "chat_content";

    public static final String BUSINESS_NO = "business_no";

    public static final String SELECTED = "selected";

    public static final String FILE_ID = "file_id";

    @Override
    protected Serializable pkVal() {
        return null;
    }

}
