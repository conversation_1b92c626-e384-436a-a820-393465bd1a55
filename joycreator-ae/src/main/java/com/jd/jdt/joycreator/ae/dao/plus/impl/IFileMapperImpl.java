package com.jd.jdt.joycreator.ae.dao.plus.impl;

import com.jd.jdt.app4s.component.common.api.exception.BussinessBizException;
import com.jd.jdt.joycreator.ae.entity.File;
import com.jd.jdt.joycreator.ae.dao.mapper.FileMapper;
import com.jd.jdt.joycreator.ae.dao.plus.IFileMapper;
import com.jd.jdt.joycreator.ae.dao.plus.IBaseMapperImpl;
import com.jd.jdt.joycreator.ae.enums.FileTypeEnum;
import com.jd.jdt.joycreator.ae.pojo.dto.UploadFileDTO;
import com.jd.jdt.joycreator.ae.pojo.vo.FileVO;
import com.jd.jdt.joycreator.ae.service.FileService;
import com.jd.jdt.joycreator.ae.utils.JoyCreatorUtil;
import com.jd.jsf.gd.util.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;

/**
 * <p>
 * 文件-业务附件 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
@Service
public class IFileMapperImpl extends IBaseMapperImpl<FileMapper, File> implements IFileMapper {

    @Autowired
    private FileService fileService;
    @Autowired
    private JoyCreatorUtil joyCreatorUtil;

    @Override
    public FileVO updateFile(MultipartFile files, FileTypeEnum fileType) throws IOException {
//        List<String> fileTypes = Lists.newArrayList("docx", "doc");
        String originalFilename = files.getOriginalFilename();
        if (StringUtils.isNotBlank(originalFilename) && originalFilename.length() >= 80) {
            throw new BussinessBizException("文件名称不能大于80个汉字");
        }
        String fileSuffix = originalFilename.substring(originalFilename.lastIndexOf(".") + 1).toLowerCase();
//        if (!fileTypes.contains(fileSuffix)) {
//            throw new BussinessBizException("上传文件只支持pdf,docx,doc文件类型");
//        }
        InputStream inputStream = files.getInputStream();
        UploadFileDTO uploadFileDTO = fileService.uploadFile(originalFilename, inputStream, "application/" + fileSuffix);
        File file = new File();
        file.setFileType(fileType);
        file.setName(originalFilename);
        file.setFileSuffix(fileSuffix);
        file.setFileSize(new BigDecimal(files.getSize()));
        file.setWanUrl(uploadFileDTO.getFileWanUrl());
        file.setLanUrl(uploadFileDTO.getFileLanUrl());
        insert(file);
        FileVO fileVO = new FileVO();
        BeanUtils.copyProperties(file, fileVO);
        fileVO.setLanUrl(joyCreatorUtil.inteProtocol(uploadFileDTO.getFileLanUrl()));
        fileVO.setWanUrl(joyCreatorUtil.inteProtocol(uploadFileDTO.getFileWanUrl()));
        return fileVO;
    }
}
