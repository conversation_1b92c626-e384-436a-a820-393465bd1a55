package com.jd.jdt.joycreator.ae.service.impl;

import com.jd.jdt.joycreator.ae.dao.plus.ISuggestGovernMapper;
import com.jd.jdt.joycreator.ae.entity.SuggestGovern;
import com.jd.jdt.joycreator.ae.pojo.vo.SuggestGovernVO;
import com.jd.jdt.joycreator.ae.service.SuggestGovernServe;
import com.jd.jdt.joycreator.ae.service.DictService;
import com.jd.jdt.joycreator.ae.pojo.vo.DictVO;
import com.jd.jdt.joycreator.ae.pojo.vo.DictDtlVO;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 推荐配置实现 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@Log4j2
@Service
public class SuggestGovernServeImpl implements SuggestGovernServe {

    @Autowired
    private ISuggestGovernMapper iSuggestGovernMapper;

    @Autowired
    private DictService dictService;

    @Override
    public List<SuggestGovernVO> listSuggestGovern() {
        List<SuggestGovern> suggestGovernList = iSuggestGovernMapper.listSuggestGovern();
        if (suggestGovernList == null || suggestGovernList.isEmpty()) {
            return Collections.emptyList();
        }
        // 查询推荐分类字典
        DictVO dictVO = dictService.dtl("SUGGEST_CATEGORY");
        List<DictDtlVO> dictDtlList = dictVO != null ? dictVO.getDictDtlList() : Collections.emptyList();
        return suggestGovernList.stream().map(entity -> {
            SuggestGovernVO vo = new SuggestGovernVO();
            BeanUtils.copyProperties(entity, vo);
            vo.setName(entity.getName());
            // 比对category，赋值categoryName
            if (dictDtlList != null) {
                dictDtlList.stream()
                    .filter(d -> d.getValue() != null && d.getValue().equals(vo.getRecommendationCategoryEnum()))
                    .findFirst()
                    .ifPresent(d -> vo.setCategory(d.getName()));
            }
            return vo;
        }).collect(Collectors.toList());
    }
}
