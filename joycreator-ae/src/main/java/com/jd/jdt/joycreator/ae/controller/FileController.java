package com.jd.jdt.joycreator.ae.controller;

import com.google.common.collect.Lists;
import com.jd.jdt.app4s.component.common.api.entity.ResponseResult;
import com.jd.jdt.app4s.component.common.api.exception.BussinessBizException;
import com.jd.jdt.joycreator.ae.dao.plus.IFileMapper;
import com.jd.jdt.joycreator.ae.enums.FileTypeEnum;
import com.jd.jdt.joycreator.ae.enums.OfficeConvertEnum;
import com.jd.jdt.joycreator.ae.pojo.dto.OfficeConvertDTO;
import com.jd.jdt.joycreator.ae.pojo.vo.FileVO;
import com.jd.jdt.joycreator.ae.pojo.vo.OfficeConvertVO;
import com.jd.jdt.joycreator.ae.rpc.feign.OfficeConvertService;
import com.jd.jdt.joycreator.ae.utils.WordToHtmlConverterUtil2;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 文件服务接口
 * </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @since 2025-06-09
 */
@Log4j2
@RestController
@RequestMapping("/api/v1/file")
public class FileController {
    @Autowired
    private IFileMapper iFileMapper;
    @Autowired
    private OfficeConvertService officeConvertService;

    /**
     * 上传文件
     */
    @PostMapping(value = "/upload")
    public ResponseResult<FileVO> updateFile(@RequestParam("file") MultipartFile file, @RequestParam("fileType") FileTypeEnum fileType) throws IOException {
        return ResponseResult.success(iFileMapper.updateFile(file, fileType));
    }

    /**
     * Word To Html
     */
    @PostMapping(value = "/word-to-html")
    public ResponseResult<String> wordToHtml(@RequestParam("file") MultipartFile file) throws Exception {
        List<String> fileTypes = Lists.newArrayList("docx", "doc");
        String originalFilename = file.getOriginalFilename();
        if (StringUtils.isEmpty(originalFilename)) {
            throw new BussinessBizException("文件不能为空");
        }
        String fileSuffix = originalFilename.substring(originalFilename.lastIndexOf(".") + 1).toLowerCase();
        if (!fileTypes.contains(fileSuffix)) {
            throw new BussinessBizException("上传文件只支持pdf,docx,doc文件类型");
        }
        return ResponseResult.success(WordToHtmlConverterUtil2.convertDocxToHtml(file.getInputStream()));
    }


    /**
     * 文件格式转换
     */
    @PostMapping(value = "/office-convert")
    public void officeConvert(HttpServletResponse response, @RequestParam("file") MultipartFile file, @RequestParam("officeConvert") OfficeConvertEnum officeConvert) throws Exception {
        String originalFilename = file.getOriginalFilename();
        if (StringUtils.isEmpty(originalFilename) || Objects.isNull(officeConvert)) {
            throw new BussinessBizException("文件不能为空");
        }

        OfficeConvertDTO officeConvertDTO = new OfficeConvertDTO();
        officeConvertDTO.setFileName(originalFilename);
        officeConvertDTO.setOfficeConvert(officeConvert);
        officeConvertDTO.setByteArray(file.getBytes());
        com.jd.jdt.joybuilder.permission.entity.ResponseResult<OfficeConvertVO> responseResult = officeConvertService.officeConvert(officeConvertDTO);
        if (!responseResult.isSuccess() || Objects.isNull(responseResult.getData())) {
            throw new BussinessBizException("文件转换失败！");
        }
        OfficeConvertVO officeConvertVO = responseResult.getData();
        String fileName = officeConvertVO.getFileName();
        byte[] byteArray = officeConvertVO.getByteArray();

        // 根据文件扩展名设置Content-Type
        String fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
        String contentType = getContentTypeByExtension(fileExtension);

        // 设置响应头信息
        response.setContentType(contentType);
        response.setHeader("Content-Disposition", "attachment; filename=" + new String(fileName.getBytes("UTF-8"), "ISO-8859-1"));
        response.setContentLength(byteArray.length);

        try {
            // 将文件内容写入响应输出流
            response.getOutputStream().write(byteArray);
            response.getOutputStream().flush();
        } catch (IOException e) {
            log.error("文件下载失败", e);
            throw new BussinessBizException("文件下载失败：" + e.getMessage());
        }
    }

    /**
     * 根据文件扩展名获取对应的Content-Type
     *
     * @param extension 文件扩展名
     * @return 对应的Content-Type
     */
    private String getContentTypeByExtension(String extension) {
        switch (extension.toLowerCase()) {
            case "htm":
                return "text/html";
            case "pdf":
                return "application/pdf";
            case "doc":
                return "application/msword";
            case "docx":
                return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case "xls":
                return "application/vnd.ms-excel";
            case "xlsx":
                return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            case "ppt":
                return "application/vnd.ms-powerpoint";
            case "pptx":
                return "application/vnd.openxmlformats-officedocument.presentationml.presentation";
            case "txt":
                return "text/plain";
            case "xml":
                return "application/xml";
            case "json":
                return "application/json";
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            case "png":
                return "image/png";
            case "gif":
                return "image/gif";
            default:
                return "application/octet-stream";
        }
    }

}
