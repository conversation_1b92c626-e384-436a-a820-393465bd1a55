package com.jd.jdt.joycreator.ae.pojo.dto;

import com.jd.jdt.joycreator.ae.enums.ChatRoleEnum;
import com.jd.jdt.joycreator.ae.enums.MsgTypeEnum;
import lombok.Data;

/**
 * <p>
 * 多伦Chat消息明细DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Data
public class DoronChatMessageDetailDTO {

    /**
     * 消息类型
     */
    private ChatRoleEnum chatRole;

    /**
     * 文件ID
     */
    private Long fileId;

    /**
     * 内容
     */
    private String chatContent;

    /**
     * 文档内容
     */
    private String documentContent;

    /**
     * 文档文本内容
     */
    private String textContent;


}
