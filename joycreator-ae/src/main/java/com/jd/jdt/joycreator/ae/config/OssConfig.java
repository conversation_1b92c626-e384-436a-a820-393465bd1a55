package com.jd.jdt.joycreator.ae.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <p>
 * OssConfig
 * </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @since 2025-04-14
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "oss")
public class OssConfig {

    private String accessKey;
    private String secretKey;
    private String endpoint;
    private String intranetEndpoint;
    private String bucket;
    private String protocol;
}
