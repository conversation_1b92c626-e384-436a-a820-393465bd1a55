package com.jd.jdt.joycreator.ae.dao.plus;

import com.jd.jdt.joycreator.ae.entity.File;
import com.jd.jdt.joycreator.ae.dao.IBaseDao;
import com.jd.jdt.joycreator.ae.enums.FileTypeEnum;
import com.jd.jdt.joycreator.ae.pojo.vo.FileVO;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * <p>
 * 文件-业务附件 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
public interface IFileMapper extends IBaseDao<File> {

    /**
     * 文件上传
     *
     * @param file
     * @param fileType
     * @return
     * @throws IOException
     */
    FileVO updateFile(MultipartFile file, FileTypeEnum fileType) throws IOException;
}
