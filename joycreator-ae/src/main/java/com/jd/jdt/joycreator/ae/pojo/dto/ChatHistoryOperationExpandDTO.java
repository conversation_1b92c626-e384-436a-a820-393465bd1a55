package com.jd.jdt.joycreator.ae.pojo.dto;

import com.jd.jdt.joycreator.ae.pojo.vo.CreateOutlineVO;
import lombok.Data;

/**
 * <p>
 * 操作chat拓展入参DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@Data
public class ChatHistoryOperationExpandDTO extends ChatHistoryOperationDTO {

    /**
     * 模版ID（模版选中确认）
     */
    private String templateId;

    /**
     * 大纲列表（更新大纲）
     */
    private CreateOutlineVO createOutlineVO;

}
