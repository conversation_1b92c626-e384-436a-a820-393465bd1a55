package com.jd.jdt.joycreator.ae.exception;


import com.jd.jdt.app4s.component.common.api.entity.ResultVo;
import com.jd.jdt.app4s.component.common.api.enums.ResultCode;
import com.jd.jdt.app4s.component.common.api.exception.BiException;
import com.jd.jdt.app4s.component.common.api.exception.BussinessBizException;
import com.jd.jdt.app4s.component.common.api.exception.ComponentException;
import lombok.extern.log4j.Log4j2;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.Optional;

/**
 * <p>
 * 全局异常 配置
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Log4j2
@RestControllerAdvice
public class BizExceptionHandler {

    /**
     * 业务异常
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(ComponentException.class)
    public ResultVo handleComponentException(ComponentException exception) {
        log.error("Business Error", exception);
        return ResultVo.fail(exception.getCode(), exception.getMessage());
    }

    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(BiException.class)
    public ResultVo handleBiException(BiException exception) {
        log.error("System Error", exception);
        return ResultVo.fail(exception.getCode(), exception.getException().getMessage());
    }

    /**
     * 系统异常
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public ResultVo handleMissingServletRequestParameterException(MissingServletRequestParameterException exception) {
        log.error("parameter Error", exception);
        return ResultVo.fail(ResultCode.PARAM_ERROR.getCode(), exception.getMessage());
    }

    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(IllegalArgumentException.class)
    public ResultVo handleIllegalArgumentException(IllegalArgumentException exception) {
        log.error("parameter Error", exception);
        return ResultVo.fail(ResultCode.PARAM_ERROR.getCode(), exception.getMessage());
    }

    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResultVo handleMethodArgumentNotValidException(MethodArgumentNotValidException exception) {
        log.error("parameter field Error", exception);
        FieldError error = exception.getBindingResult().getFieldError();
        String defaultMessage = Optional.ofNullable(error).map(DefaultMessageSourceResolvable::getDefaultMessage).orElse("参数效验不合法，请检查");
        return ResultVo.fail(ResultCode.PARAM_ERROR.getCode(), defaultMessage);
    }

    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResultVo handleException(HttpMessageNotReadableException exception) {
        log.error("System Error", exception);
        return ResultVo.fail(ResultCode.SERVER_ERROR.getCode(), "请求体错误");
    }

    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(BussinessBizException.class)
    public ResultVo handleException(BussinessBizException exception) {
        log.error("System Error", exception);
        return ResultVo.fail(ResultCode.SERVER_ERROR.getCode(), exception.getMsg());
    }

    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(Exception.class)
    public ResultVo handleException(Exception exception) {
        log.error("System Error", exception);
        return ResultVo.fail(ResultCode.SERVER_ERROR.getCode(), "程序异常，为保障您的使用体验，请稍等片刻后重试");
    }
}
