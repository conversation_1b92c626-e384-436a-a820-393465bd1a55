package com.jd.jdt.joycreator.ae.dao.plus;

import com.jd.jdt.joycreator.ae.entity.PromptWordsConfig;
import com.jd.jdt.joycreator.ae.dao.IBaseDao;
import com.jd.jdt.joycreator.ae.enums.PromptCodeEnum;

/**
 * <p>
 * 提示词管理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
public interface IPromptWordsConfigMapper extends IBaseDao<PromptWordsConfig> {

    /**
     * 根据提示词编码获取提示词
     *
     * @param promptWordCode
     * @return
     */
    PromptWordsConfig getPromptWordsConfigByCode(PromptCodeEnum promptWordCode);
}
