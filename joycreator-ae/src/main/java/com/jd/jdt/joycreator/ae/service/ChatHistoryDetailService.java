package com.jd.jdt.joycreator.ae.service;

import com.jd.jdt.joycreator.ae.enums.ChatRoleEnum;
import com.jd.jdt.joycreator.ae.pojo.dto.ChatHistoryDetailDTO;
import com.jd.jdt.joycreator.ae.pojo.dto.ChatHistoryOperationDTO;
import com.jd.jdt.joycreator.ae.pojo.dto.ChatHistoryOperationExpandDTO;
import com.jd.jdt.joycreator.ae.pojo.vo.ChatHistoryVO;

import java.util.Queue;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <p>
 * 聊天记录明细 服务
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
public interface ChatHistoryDetailService {

    /**
     * 批量保存聊天记录明细
     *
     * @param chatHistoryQueue
     * @param done
     * @param sessionId
     */
    void saveBatch(Queue<ChatHistoryDetailDTO> chatHistoryQueue, AtomicBoolean done, String sessionId);

    /**
     * 通过sessionId查询聊天记录明细
     *
     * @param sessionId
     * @return
     */
    ChatHistoryVO dtl(String sessionId);


    /**
     * 确认重写标记
     *
     * @param chatHistoryOperationDTO
     * @return
     */
    Boolean confirmDoccontentRewrite(ChatHistoryOperationDTO chatHistoryOperationDTO);

    /**
     * 确认改写
     *
     * @param chatHistoryOperationOutlineDTO
     * @return
     */
    Boolean updateOutline(ChatHistoryOperationExpandDTO chatHistoryOperationOutlineDTO);

    void saveAsync(AtomicBoolean done, StringBuffer contentBufferAll, ChatRoleEnum chatRoleEnum, String sessionId, String businessNo);
}
