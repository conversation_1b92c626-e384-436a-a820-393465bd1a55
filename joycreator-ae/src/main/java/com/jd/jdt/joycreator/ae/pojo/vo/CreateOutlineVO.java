package com.jd.jdt.joycreator.ae.pojo.vo;

import lombok.Data;
import org.apache.commons.compress.utils.Lists;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <p>
 * 大纲VO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Data
public class CreateOutlineVO {

    /**
     * 标题
     */
    private String title;

    /**
     * 标题层级
     */
    private int level;

    /**
     * 标题总结
     */
    private String summary;

    /**
     * 一级标题下的二级标题
     */
    private List<CreateOutlineVO> createOutlineList = Lists.newArrayList();

}
