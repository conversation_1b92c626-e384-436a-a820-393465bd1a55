package com.jd.jdt.joycreator.ae.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.jd.jdt.joycreator.ae.dao.plus.*;
import com.jd.jdt.joycreator.ae.entity.*;
import com.jd.jdt.joycreator.ae.enums.ChatRoleEnum;
import com.jd.jdt.joycreator.ae.pojo.dto.ChatHistoryDetailDTO;
import com.jd.jdt.joycreator.ae.pojo.dto.ChatHistoryOperationDTO;
import com.jd.jdt.joycreator.ae.pojo.dto.ChatHistoryOperationExpandDTO;
import com.jd.jdt.joycreator.ae.pojo.vo.*;
import com.jd.jdt.joycreator.ae.service.ChatHistoryDetailService;
import com.jd.jdt.joycreator.ae.service.ChatHistoryDetailTemplateService;
import com.jd.jdt.joycreator.ae.utils.JoyCreatorUtil;
import com.jd.jsf.gd.util.JsonUtils;
import com.jd.jsf.gd.util.StringUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Queue;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * <p>
 * 聊天记录明细 服务
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
@Log4j2
@Service
public class ChatHistoryDetailServiceImpl implements ChatHistoryDetailService {

    @Autowired
    private IChatHistoryMapper iChatHistoryMapper;

    @Autowired
    private IChatHistoryDetailMapper iChatHistoryDetailMapper;

    @Autowired
    private IChatHistoryDetailTemplateMapper iChatHistoryDetailTemplateMapper;

    @Autowired
    private ChatHistoryDetailTemplateService chatHistoryDetailTemplateService;

    @Autowired
    private ITemplateLibraryMapper iTemplateLibraryMapper;

    @Autowired
    private JoyCreatorUtil joyCreatorUtil;

    @Autowired
    private IFileMapper iFileMapper;

    @Autowired
    private IChatHistoryDetailExcerptMapper iChatHistoryDetailExcerptMapper;


    @Override
    public void saveBatch(Queue<ChatHistoryDetailDTO> chatHistoryQueue, AtomicBoolean done, String sessionId) {
        log.info("保存聊天记录，开始执行，sessionId：{}", sessionId);
        new Thread(() -> {
            if (StringUtils.isEmpty(sessionId)) {
                return;
            }
            ChatHistory chatHistory = iChatHistoryMapper.chatHistoryBySessionId(sessionId);
            log.info("保存聊天记录，开始执行，chatHistory：{}", JsonUtils.toJSONString(chatHistory));
            if (Objects.isNull(chatHistory)) {
                return;
            }
            long startTime = System.currentTimeMillis();
            long timeout = 300000;
            while (true) {
                if (System.currentTimeMillis() - startTime > timeout) {
                    log.warn("保存聊天记录，执行超时，done：{}", done.get());
                    break;
                }
                ChatHistoryDetailDTO chatHistoryDetailDTO = chatHistoryQueue.poll();
                if (done.get() && Objects.isNull(chatHistoryDetailDTO)) {
                    break;
                }
                if (Objects.isNull(chatHistoryDetailDTO)) {
                    continue;
                }

                ChatHistoryDetail chatHistoryDetail = new ChatHistoryDetail();
                BeanUtils.copyProperties(chatHistoryDetailDTO, chatHistoryDetail);
                chatHistoryDetail.setChatHistoryId(chatHistory.getId());
                boolean saveChatHistoryDetail = iChatHistoryDetailMapper.save(chatHistoryDetail);
                List<ChatHistoryDetailTemplate> chatHistoryDetailTemplateList = chatHistoryDetailDTO.getChatHistoryDetailTemplateList();
                List<ChatHistoryDetailExcerpt> chatHistoryDetailExcerptList = chatHistoryDetailDTO.getChatHistoryDetailExcerptList();
                if (saveChatHistoryDetail) {
                    if (CollectionUtils.isNotEmpty(chatHistoryDetailTemplateList)) {
                        chatHistoryDetailTemplateList.forEach(c -> c.setChatHistoryDetailId(chatHistoryDetail.getId()));
                        iChatHistoryDetailTemplateMapper.saveBatch(chatHistoryDetailTemplateList);
                    }
                    if (CollectionUtils.isNotEmpty(chatHistoryDetailExcerptList)) {
                        chatHistoryDetailExcerptList.forEach(c -> c.setChatHistoryDetailId(chatHistoryDetail.getId()));
                        iChatHistoryDetailExcerptMapper.saveBatch(chatHistoryDetailExcerptList);
                    }
                }
                log.info("保存聊天记录，执行完成，done：{}，chatHistoryDetail：{}", done.get(), JsonUtils.toJSONString(chatHistoryDetail));
            }
        }).start();
    }

    @Override
    public ChatHistoryVO dtl(String sessionId) {
        ChatHistory chatHistory = iChatHistoryMapper.chatHistoryBySessionId(sessionId);
        if (Objects.isNull(chatHistory)) {
            return null;
        }
        ChatHistoryVO chatHistoryVO = new ChatHistoryVO();
        BeanUtils.copyProperties(chatHistory, chatHistoryVO);
        List<ChatHistoryDetail> chatHistoryDetailList = iChatHistoryDetailMapper.listChatHistoryDetailBySessionId(chatHistory.getId());
        if (CollectionUtils.isEmpty(chatHistoryDetailList)) {
            return chatHistoryVO;
        }
        List<ChatHistoryDetailVO> chatHistoryDetailVOList = Lists.newArrayList();
        List<Object> objectList = Lists.newArrayList();
        for (int i = 0; i < chatHistoryDetailList.size(); i++) {
            ChatHistoryDetail chatHistoryDetail = chatHistoryDetailList.get(i);
            if (ChatRoleEnum.USER == chatHistoryDetail.getChatRole()) {
                List<ChatHistoryDetailExcerptVO> userMainList = Lists.newArrayList();
                List<ChatHistoryDetailExcerpt> chatHistoryDetailExcerptList = iChatHistoryDetailExcerptMapper.listByChatHistoryDetailId(chatHistoryDetail.getId());
                ChatHistoryDetailVO chatHistoryDetailUserVO = new ChatHistoryDetailVO();
                BeanUtils.copyProperties(chatHistoryDetail, chatHistoryDetailUserVO);
                if (CollectionUtils.isNotEmpty(chatHistoryDetailExcerptList)) {
                    List<ChatHistoryDetailExcerptVO> userExcerptList = chatHistoryDetailExcerptList.stream().map(c -> {
                        ChatHistoryDetailExcerptVO chde = new ChatHistoryDetailExcerptVO();
                        BeanUtils.copyProperties(c, chde);
                        if (c.getMsgType() == ChatRoleEnum.FILE) {
                            File file = iFileMapper.getById(c.getFileId());
                            if (Objects.nonNull(file)) {
                                FileVO fileVO = new FileVO();
                                BeanUtils.copyProperties(file, fileVO);
                                fileVO.setLanUrl(joyCreatorUtil.inteProtocol(file.getLanUrl()));
                                fileVO.setWanUrl(joyCreatorUtil.inteProtocol(file.getWanUrl()));
                                chde.setFileVO(fileVO);
                            }
                        }
                        return chde;
                    }).collect(Collectors.toList());
                    userMainList.addAll(userExcerptList);

                    boolean noneMatch = chatHistoryDetailExcerptList.stream().noneMatch(c -> ChatRoleEnum.TEXT == c.getMsgType());
                    if (noneMatch) {
                        ChatHistoryDetailExcerptVO chde = new ChatHistoryDetailExcerptVO();
                        chde.setMsgType(ChatRoleEnum.TEXT);
                        chde.setChatHistoryDetailId(chatHistoryDetail.getChatHistoryId());
                        chde.setChatContent(chatHistoryDetail.getChatContent());
                        userMainList.add(chde);
                    }
                } else {
                    ChatHistoryDetailExcerptVO chde = new ChatHistoryDetailExcerptVO();
                    chde.setMsgType(ChatRoleEnum.TEXT);
                    chde.setChatHistoryDetailId(chatHistoryDetail.getChatHistoryId());
                    chde.setChatContent(chatHistoryDetail.getChatContent());
                    userMainList.add(chde);
                }
                chatHistoryDetailUserVO.setChatContent(userMainList);
                chatHistoryDetailVOList.add(chatHistoryDetailUserVO);
            }
            if (ChatRoleEnum.ASSISTANT == chatHistoryDetail.getChatRole()) {
                /*ChatHistoryDetailVO chatHistoryDetailVO = new ChatHistoryDetailVO();
                BeanUtils.copyProperties(chatHistoryDetail, chatHistoryDetailVO);
                String chatContent = chatHistoryDetail.getChatContent();
                if (StringUtils.isNotBlank(chatContent)) {
                    int index = chatContent.indexOf(ToolLabelsEnum.TEMPLATE_LIBRARY.getType());
                    if (index != -1) {
                        chatContent = chatContent.substring(0, index);
                    }
                }
                chatHistoryDetailVO.setChatContent(chatContent);
                objectList.add(chatHistoryDetailVO);*/
                ChatHistoryDetailVO chatHistoryDetailVO = new ChatHistoryDetailVO();
                BeanUtils.copyProperties(chatHistoryDetail, chatHistoryDetailVO);
                objectList.add(chatHistoryDetailVO);
            } else if (ChatRoleEnum.TOOL == chatHistoryDetail.getChatRole()) {
                ChatHistoryDetailVO chatHistoryDetailVO = new ChatHistoryDetailVO();
                BeanUtils.copyProperties(chatHistoryDetail, chatHistoryDetailVO);
                objectList.add(chatHistoryDetailVO);
            } else if (ChatRoleEnum.TEMPLATE_LIBRARY_QUERY == chatHistoryDetail.getChatRole()) {
                ChatHistoryDetailVO chatHistoryDetailVO = new ChatHistoryDetailVO();
                BeanUtils.copyProperties(chatHistoryDetail, chatHistoryDetailVO);
                ObjectMapper mapper = new ObjectMapper();
                String chatContent = chatHistoryDetail.getChatContent();
                if (StringUtils.isNotBlank(chatContent)) {
                    try {
                        List<TemplateLibrary> templateList = mapper.readValue(chatContent, new TypeReference<List<TemplateLibrary>>() {
                        });
                        chatHistoryDetailVO.setChatContent(templateList);
                    } catch (JsonProcessingException e) {
                        log.error("会话记录详情模板转换失败，chatContent：{}", chatContent, e);
                    }
                }
                objectList.add(chatHistoryDetailVO);
            } else if (ChatRoleEnum.TEMPLATE_LIBRARY == chatHistoryDetail.getChatRole()) {
                ChatHistoryDetailVO chatHistoryDetailVO = new ChatHistoryDetailVO();
                chatHistoryDetailVO.setChatRole(ChatRoleEnum.TEMPLATE_LIBRARY);
                chatHistoryDetailVO.setBusinessNo(chatHistoryDetail.getBusinessNo());
                List<ChatHistoryDetailTemplate> chatHistoryDetailTemplateList = chatHistoryDetailTemplateService.listChatHistoryDetailTemplateByChatHistoryDetailId(chatHistoryDetail.getId());
                if (CollectionUtils.isNotEmpty(chatHistoryDetailTemplateList)) {
                    List<ChatHistoryDetailTemplateVO> chatHistoryDetailTemplateVOList = chatHistoryDetailTemplateList.stream().map(c -> {
                        ChatHistoryDetailTemplateVO chatHistoryDetailTemplateVO = new ChatHistoryDetailTemplateVO();
                        BeanUtils.copyProperties(c, chatHistoryDetailTemplateVO);
                        TemplateLibrary templateLibrary = iTemplateLibraryMapper.getById(c.getTemplateId());
                        chatHistoryDetailTemplateVO.setTemplateName(templateLibrary.getName());
                        chatHistoryDetailTemplateVO.setDescription(templateLibrary.getDescription());
                        chatHistoryDetailTemplateVO.setTemplateId(templateLibrary.getId().toString());
                        return chatHistoryDetailTemplateVO;
                    }).collect(Collectors.toList());
                    chatHistoryDetailVO.setChatContent(chatHistoryDetailTemplateVOList);
                }
                objectList.add(chatHistoryDetailVO);
            } else if (ChatRoleEnum.DOCCONTENT_REWRITE == chatHistoryDetail.getChatRole()) {
                ChatHistoryDetailVO chatHistoryDetailVO = new ChatHistoryDetailVO();
                BeanUtils.copyProperties(chatHistoryDetail, chatHistoryDetailVO);
                objectList.add(chatHistoryDetailVO);
            } else if (ChatRoleEnum.DOCCONTENT_EXTRACTION_QUERY == chatHistoryDetail.getChatRole()) {
                ChatHistoryDetailVO chatHistoryDetailVO = new ChatHistoryDetailVO();
                BeanUtils.copyProperties(chatHistoryDetail, chatHistoryDetailVO);
                objectList.add(chatHistoryDetailVO);
            } else if (ChatRoleEnum.DOCCONTENT_EXTRACTION == chatHistoryDetail.getChatRole()) {
                ChatHistoryDetailVO chatHistoryDetailVO = new ChatHistoryDetailVO();
                BeanUtils.copyProperties(chatHistoryDetail, chatHistoryDetailVO);
                objectList.add(chatHistoryDetailVO);
            } else if (ChatRoleEnum.FREELANCE_WRITING == chatHistoryDetail.getChatRole()) {
                ChatHistoryDetailVO chatHistoryDetailVO = new ChatHistoryDetailVO();
                BeanUtils.copyProperties(chatHistoryDetail, chatHistoryDetailVO);
                objectList.add(chatHistoryDetailVO);
            } else if (ChatRoleEnum.FREELANCE_WRITING_START == chatHistoryDetail.getChatRole()) {
                ChatHistoryDetailVO chatHistoryDetailVO = new ChatHistoryDetailVO();
                BeanUtils.copyProperties(chatHistoryDetail, chatHistoryDetailVO);
                objectList.add(chatHistoryDetailVO);
            } else if (ChatRoleEnum.OUTLINE == chatHistoryDetail.getChatRole()) {
                ChatHistoryDetailVO chatHistoryDetailVO = new ChatHistoryDetailVO();
                BeanUtils.copyProperties(chatHistoryDetail, chatHistoryDetailVO);
                chatHistoryDetailVO.setChatContent(JsonUtils.parseObject(chatHistoryDetail.getChatContent(), CreateOutlineVO.class));
                objectList.add(chatHistoryDetailVO);
            } else if (ChatRoleEnum.TEXT_REPLACEMENT == chatHistoryDetail.getChatRole()) {
                ChatHistoryDetailVO chatHistoryDetailVO = new ChatHistoryDetailVO();
                BeanUtils.copyProperties(chatHistoryDetail, chatHistoryDetailVO);
                chatHistoryDetailVO.setChatContent(JsonUtils.parseObject(chatHistoryDetail.getChatContent(), Map.class));
                objectList.add(chatHistoryDetailVO);
            } else if (ChatRoleEnum.TEXT_REWRITE == chatHistoryDetail.getChatRole()) {
                ChatHistoryDetailVO chatHistoryDetailVO = new ChatHistoryDetailVO();
                BeanUtils.copyProperties(chatHistoryDetail, chatHistoryDetailVO);
                objectList.add(chatHistoryDetailVO);
            } else if (ChatRoleEnum.DOCCONTENT_EXTRACTION_ERROR == chatHistoryDetail.getChatRole()) {
                ChatHistoryDetailVO chatHistoryDetailVO = new ChatHistoryDetailVO();
                BeanUtils.copyProperties(chatHistoryDetail, chatHistoryDetailVO);
                objectList.add(chatHistoryDetailVO);
            } else if (ChatRoleEnum.TASK_FEEDBACK == chatHistoryDetail.getChatRole()) {
                // 处理TASK_FEEDBACK角色：将当前数据和前一条数据添加到list中
                ChatHistoryDetailVO chatHistoryDetailVO2 = new ChatHistoryDetailVO();
                chatHistoryDetailVO2.setChatRole(ChatRoleEnum.ASSISTANT);
                chatHistoryDetailVO2.setChatHistoryId(chatHistoryDetail.getChatHistoryId());

                List<Object> taskFeedbackList = Lists.newArrayList();

                // 添加前一条数据（如果存在且不是USER角色）
                if (i > 0) {
                    ChatHistoryDetail previousDetail = chatHistoryDetailList.get(i - 1);
                    if (previousDetail.getChatRole() != ChatRoleEnum.USER) {
                        ChatHistoryDetailVO previousVO = new ChatHistoryDetailVO();
                        BeanUtils.copyProperties(previousDetail, previousVO);
                        taskFeedbackList.add(previousVO);
                    }
                }

                // 添加当前TASK_FEEDBACK数据
                ChatHistoryDetailVO currentVO = new ChatHistoryDetailVO();
                BeanUtils.copyProperties(chatHistoryDetail, currentVO);
                taskFeedbackList.add(currentVO);

                // 设置businessNo（优先使用当前数据的businessNo）
                chatHistoryDetailVO2.setBusinessNo(chatHistoryDetail.getBusinessNo());

                chatHistoryDetailVO2.setChatContent(taskFeedbackList);
                chatHistoryDetailVOList.add(chatHistoryDetailVO2);
            }


            if (CollectionUtils.isNotEmpty(objectList) && (chatHistoryDetailList.size() == i + 1 || chatHistoryDetailList.get(i + 1).getChatRole() == ChatRoleEnum.USER)) {
                ChatHistoryDetailVO chatHistoryDetailVO2 = new ChatHistoryDetailVO();
                chatHistoryDetailVO2.setChatRole(ChatRoleEnum.ASSISTANT);
                if (CollectionUtils.isNotEmpty(objectList)) {
                    List<Object> assistantList = objectList.stream().filter(o -> o instanceof ChatHistoryDetailVO && ChatRoleEnum.ASSISTANT == ((ChatHistoryDetailVO) o).getChatRole()).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(assistantList)) {
                        ChatHistoryDetailVO assistant = (ChatHistoryDetailVO) assistantList.get(0);
                        chatHistoryDetailVO2.setBusinessNo(assistant.getBusinessNo());
                        chatHistoryDetailVO2.setChatHistoryId(assistant.getChatHistoryId());
                    }
                }
                List<Object> objectNewList = Lists.newArrayList(objectList);
                chatHistoryDetailVO2.setChatContent(objectNewList);
                chatHistoryDetailVOList.add(chatHistoryDetailVO2);
                objectList.clear();
            }
        }
        chatHistoryVO.setChatHistoryDetailVOList(chatHistoryDetailVOList);
        return chatHistoryVO;
    }

    @Override
    public Boolean confirmDoccontentRewrite(ChatHistoryOperationDTO chatHistoryOperationDTO) {
        return iChatHistoryDetailMapper.confirmDoccontentRewriteBybusinessNo(chatHistoryOperationDTO.getBusinessNo());
    }

    @Override
    public Boolean updateOutline(ChatHistoryOperationExpandDTO chatHistoryOperationOutlineDTO) {
        return iChatHistoryDetailMapper.updateChatContent(chatHistoryOperationOutlineDTO.getBusinessNo(), JsonUtils.toJSONString(chatHistoryOperationOutlineDTO.getCreateOutlineVO()));
    }

    @Override
    public void saveAsync(AtomicBoolean done, StringBuffer contentBufferAll, ChatRoleEnum chatRoleEnum, String sessionId, String businessNo) {
        new Thread(() -> {
            while (!done.get()) {
                try {
                    Thread.sleep(10);
                } catch (InterruptedException e) {
                }
            }
            ChatHistory chatHistory = iChatHistoryMapper.chatHistoryBySessionId(sessionId);
            ChatHistoryDetail chatHistoryDetail = new ChatHistoryDetail();
            chatHistoryDetail.setChatHistoryId(chatHistory.getId());
            chatHistoryDetail.setChatRole(chatRoleEnum);
            chatHistoryDetail.setChatContent(contentBufferAll.toString());
            chatHistoryDetail.setBusinessNo(businessNo);
            iChatHistoryDetailMapper.save(chatHistoryDetail);
        }).start();
    }

}
