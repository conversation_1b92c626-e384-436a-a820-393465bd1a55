package com.jd.jdt.joycreator.ae.pojo.dto;

import lombok.Data;
import org.apache.commons.compress.utils.Lists;

import java.util.List;

/**
 * <p>
 * 大纲DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Data
public class CreateOutlineDTO {

    /**
     * 大纲标题
     */
    private String title;

    /**
     * 标题总结
     */
    private String summary;

    /**
     * 大纲层级
     */
    private int level;

    /**
     * 大纲一级标题下的二级标题
     */
    private List<CreateOutlineDTO> createOutlineList = Lists.newArrayList();

}
