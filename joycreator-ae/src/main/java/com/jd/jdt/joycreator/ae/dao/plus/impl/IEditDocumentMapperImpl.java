package com.jd.jdt.joycreator.ae.dao.plus.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jd.jdt.joycreator.ae.entity.EditDocument;
import com.jd.jdt.joycreator.ae.dao.mapper.EditDocumentMapper;
import com.jd.jdt.joycreator.ae.dao.plus.IEditDocumentMapper;
import com.jd.jdt.joycreator.ae.dao.plus.IBaseMapperImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 编辑器文档数据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-27
 */
@Service
public class IEditDocumentMapperImpl extends IBaseMapperImpl<EditDocumentMapper, EditDocument> implements IEditDocumentMapper {

    @Override
    public EditDocument dtlByChatHistoryId(Long id) {
        LambdaQueryWrapper<EditDocument> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EditDocument::getChatHistoryId, id);
        return getOne(queryWrapper);
    }
}
