package com.jd.jdt.joycreator.ae.dao.plus;

import com.jd.jdt.joycreator.ae.entity.Dict;
import com.jd.jdt.joycreator.ae.dao.IBaseDao;
import com.jd.jdt.joycreator.ae.enums.DictCodeEnum;
import com.jd.jdt.joycreator.ae.pojo.dto.DictDTO;

/**
 * <p>
 * 字典-字典配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
public interface IDictMapper extends IBaseDao<Dict> {

    DictDTO getDictByCode(String dictCode);

}
