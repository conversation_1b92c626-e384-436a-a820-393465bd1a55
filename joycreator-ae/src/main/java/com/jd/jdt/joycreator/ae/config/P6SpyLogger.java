package com.jd.jdt.joycreator.ae.config;

import com.p6spy.engine.spy.appender.MessageFormattingStrategy;
import org.apache.commons.lang3.StringUtils;


public class P6SpyLogger implements MessageFormattingStrategy {
    /**
     * 自定义sql日志打印
     *
     * @param connectionId 连接标识
     * @param now          执行时间
     * @param elapsed      执行秒数ms
     * @param category     statement
     * @param prepared     准备sql语句
     * @param sql          sql语句
     * @param s4           数据库url连接
     * @return {@link String}
     */
    @Override
    public String formatMessage(int connectionId, String now, long elapsed, String category, String prepared, String sql, String s4) {
        if(StringUtils.isBlank(sql)){
            return "";
        }

        return "==> 耗时：" + elapsed + " ms " + now +
                "\n  ==> 执行 SQL：" + sql.replaceAll("[\\s]+", " ");
    }
}
