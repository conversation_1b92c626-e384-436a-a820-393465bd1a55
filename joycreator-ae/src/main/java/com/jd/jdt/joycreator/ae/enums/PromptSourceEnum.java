package com.jd.jdt.joycreator.ae.enums;

/**
 * <p>
 * 提示词来源枚举
 * </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @since 2025-07-22
 */
public enum PromptSourceEnum {

    JOY_STUDIO("JOY_STUDIO", "joyStudio"),
    XINGYUN("XINGYUN", "行云"),
    ;

    /**
     * 源文件类型
     */
    private String Type;

    /**
     * 描述
     */
    private String desc;


    PromptSourceEnum(String type, String desc) {
        this.Type = type;
        this.desc = desc;
    }

    public static PromptSourceEnum getEnum(String type) {
        for (PromptSourceEnum a : PromptSourceEnum.values()) {
            if (a.getType().equals(type)) {
                return a;
            }
        }
        return null;
    }

    public String getType() {
        return Type;
    }

    public void setType(String type) {
        Type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
