package com.jd.jdt.joycreator.ae.dao.plus;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jd.jdt.app4s.component.appstarter.service.IdGenerateService;
import com.jd.jdt.app4s.component.archetype.common.holder.LcdpLoginContext;
import com.jd.jdt.app4s.component.archetype.common.holder.LcdpUserInfo;
import com.jd.jdt.app4s.component.common.db.enums.YnEnum;
import com.jd.jdt.joycreator.ae.dao.IBaseDao;
import com.jd.jdt.joycreator.ae.entity.BaseEntity;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;

import java.io.Serializable;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Objects;

public class IBaseMapperImpl<M extends BaseMapper<T>, T extends BaseEntity> extends ServiceImpl<M, T> implements IBaseDao<T> {
    private String system = "system";

    @Autowired
    private IdGenerateService idGenerateService;


    /**``
     * 普通插入方法
     *`
     * @param entity 实体
     * @return 结果
     */
    @Override
    public boolean insert(T entity) {
        return super.save(initBaseFields(entity, Boolean.TRUE));
    }

    @Override
    public boolean save(T entity) {
        return super.save(initBaseFields(entity, Boolean.TRUE));
    }

    /**
     * 普通插入方法
     *
     * @param entity 实体
     * @return 结果
     */
    @Override
    public boolean saveOrUpdate(T entity) {
        if (null == entity.getId()) {
            initBaseFields(entity, Boolean.TRUE);
        } else {
            initBaseFields(entity, Boolean.FALSE);
        }
        return super.saveOrUpdate(entity);
    }

    @Override
    public boolean saveBatchTime(Collection<T> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return false;
        }
        for (BaseEntity entity : entityList) {
//            entity.setId(id(entity));
            initBaseFieldsTime(entity, Boolean.TRUE);
        }
        return super.saveBatch(entityList);
    }


    @Override
    public boolean saveBatch(Collection<T> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return false;
        }
        for (BaseEntity entity : entityList) {
//            entity.setId(id(entity));
            initBaseFields(entity, Boolean.TRUE);
        }
        return super.saveBatch(entityList);
    }


    public boolean saveBatch1(Collection<T> entityList) {
        return super.saveBatch(entityList);
    }


    /**
     * 批量更新
     *
     * @param entityList 实体列表
     * @return 结果
     */
    @Override
    public boolean updateBatchById(Collection<T> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return false;
        }

        for (BaseEntity entity : entityList) {
            initBaseFields(entity, Boolean.FALSE);
        }
        return super.updateBatchById(entityList);
    }

    /**
     * 根据id更新
     *
     * @param entity 实体
     * @return 结果
     */
    @Override
    public boolean updateById(T entity) {
        entity = initBaseFields(entity, Boolean.FALSE);
        return super.updateById(entity);
    }

    /**
     * 根据条件更新
     *
     * @param updateWrapper 条件
     * @return 结果
     */
    @Override
    public boolean update(Wrapper<T> updateWrapper) {
        LcdpUserInfo user = LcdpLoginContext.getUser();
        String userId = system;
        if (user != null) {
            userId = user.getUserName();
        }
        if (updateWrapper instanceof UpdateWrapper) {
            ((LambdaUpdateWrapper<T>) updateWrapper).eq(BaseEntity::getModifiedUser, userId);
        }
        return super.update(updateWrapper);
    }

    /**
     * 根据id删除
     *
     * @param idList ids
     * @return 结果
     */
    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        LcdpUserInfo loginContext = LcdpLoginContext.getUser();
        String userId = system;
        if (loginContext != null) {
            userId = loginContext.getUserName();
        }
        List<T> listByIds = listByIds(idList);
        if (CollectionUtils.isEmpty(listByIds)) {
            return false;
        }

        for (BaseEntity entity : listByIds) {
            entity.setModifiedUser(userId);
        }

        super.updateBatchById(listByIds);

        return super.removeByIds(idList);
    }

    /**
     * 根据id删除
     *
     * @param id id
     * @return 结果
     */
    @Override
    public boolean removeById(Serializable id) {
        LcdpUserInfo loginContext = LcdpLoginContext.getUser();
        String userId = system;
        if (loginContext != null) {
            userId = loginContext.getUserName();
        }
        T byId = getById(id);
        if (ObjectUtils.isEmpty(byId)) {
            return false;
        }

        byId.setModifiedUser(userId);

        super.updateById(byId);

        return super.removeById(id);
    }

    /**
     * 根据条件
     *
     * @param queryWrapper 条件
     * @return 结果
     */
    @Override
    public boolean remove(Wrapper<T> queryWrapper) {
        LcdpUserInfo loginContext = LcdpLoginContext.getUser();
        String userId = system;
        if (loginContext != null) {
            userId = loginContext.getUserName();
        }
        List<T> byQueryWrapper = list(queryWrapper);
        if (CollectionUtils.isEmpty(byQueryWrapper)) {
            return false;
        }

        for (T entity : byQueryWrapper) {
            entity.setModifiedUser(userId);
        }
        updateBatchById(byQueryWrapper);

        return super.remove(queryWrapper);
    }

    /**
     * 新增时初始化基础参数,此方法会将实体中（创建时间、乐观锁版本号、有效标识）为空的字段设置默认值
     * <br/>
     * 创建时间     当前系统时间
     * <br/>
     * 乐观锁版本号  0
     * <br/>
     * 有效标识     "1"
     *
     * @param entity 实体数据
     * @param <T>    实体类型
     */
    private <T extends BaseEntity> T initBaseFields(T entity, Boolean isAdd) {
        LcdpUserInfo loginContext = LcdpLoginContext.getUser();
        String userId = system;
        if (loginContext != null) {
            userId = loginContext.getUserName();
        }
        if (entity == null) {
            return null;
        }
        if (entity.getYn() == null) {
            entity.setYn(YnEnum.YES.getValue());
        }
        if (isAdd) {
//            entity.setId(id(entity));
            entity.setCreateTime(new Date());
            entity.setCreatedUser(StringUtils.isBlank(entity.getCreatedUser())?userId:entity.getCreatedUser());
            entity.setOwnerId(StringUtils.isBlank(entity.getOwnerId())?userId:entity.getOwnerId());
            entity.setModifiedUser(StringUtils.isBlank(entity.getModifiedUser())?userId:entity.getModifiedUser());
        }else{
            entity.setModifiedUser(userId);
        }
        if(Objects.isNull(entity.getUpdateTime())){
            entity.setUpdateTime(new Date());
        }
        return entity;
    }


    private <T extends BaseEntity> T initBaseFieldsTime(T entity, Boolean isAdd) {
        LcdpUserInfo loginContext = LcdpLoginContext.getUser();
        String userId = system;
        if (loginContext != null) {
            userId = loginContext.getUserName();
        }
        if (entity == null) {
            return null;
        }
        if (entity.getYn() == null) {
            entity.setYn(YnEnum.YES.getValue());
        }
        if (isAdd) {
//            entity.setId(id(entity));
            entity.setCreateTime(new Date());
            if (StringUtils.isBlank(entity.getCreatedUser())) {
                entity.setCreatedUser(userId);
            }
            entity.setOwnerId(userId);
        }
        if (StringUtils.isBlank(entity.getModifiedUser())) {
            entity.setModifiedUser(userId);
        }
        if (Objects.isNull(entity.getUpdateTime())) {
            entity.setUpdateTime(new Date());
        }
        return entity;
    }

    public  <T extends BaseEntity> String id(T t) {
        String tableName = t.getClass().getAnnotation(TableName.class).value();
        String modelName = tableName.substring(tableName.lastIndexOf("_") + 1, tableName.length());
        return  idGenerateService.newId(modelName);
    }
}
