package com.jd.jdt.joycreator.ae.service.impl;

import com.jd.jdt.joycreator.ae.dao.plus.IDictDtlMapper;
import com.jd.jdt.joycreator.ae.dao.plus.IDictMapper;
import com.jd.jdt.joycreator.ae.entity.DictDtl;
import com.jd.jdt.joycreator.ae.pojo.dto.DictDTO;
import com.jd.jdt.joycreator.ae.pojo.vo.DictDtlVO;
import com.jd.jdt.joycreator.ae.pojo.vo.DictVO;
import com.jd.jdt.joycreator.ae.service.DictService;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 字典 服务
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@Log4j2
@Service
public class DictServiceImpl implements DictService {


    @Autowired
    private IDictMapper dictMapper;
    @Autowired
    private IDictDtlMapper dictDtlMapper;


    @Override
    public DictVO dtl(String dictCode) {
        DictVO dictVO = new DictVO();
        DictDTO dictDTO = dictMapper.getDictByCode(dictCode);
        if (Objects.isNull(dictDTO)) {
            return null;
        }
        BeanUtils.copyProperties(dictDTO, dictVO);
        List<DictDtl> dictDtls = dictDtlMapper.listDictDtlByDictId(dictDTO.getId());
        if (CollectionUtils.isEmpty(dictDtls)) {
            return dictVO;
        }
        List<DictDtlVO> dictDtlVOList = dictDtls.stream().map(d -> {
            DictDtlVO dictDtlVO = new DictDtlVO();
            BeanUtils.copyProperties(d, dictDtlVO);
            return dictDtlVO;
        }).collect(Collectors.toList());
        dictVO.setDictDtlList(dictDtlVOList);
        return dictVO;
    }
}
