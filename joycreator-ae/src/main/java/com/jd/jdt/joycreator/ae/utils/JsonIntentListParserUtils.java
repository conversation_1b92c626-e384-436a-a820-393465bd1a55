package com.jd.jdt.joycreator.ae.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.jd.jdt.joycreator.ae.pojo.vo.AutofillDtlVO;
import com.jd.jdt.joycreator.ae.pojo.vo.AutofillVO;
import com.jd.jdt.joycreator.ae.pojo.vo.TemplateLibraryDtlVO;
import com.jd.jsf.gd.util.JsonUtils;
import com.jd.jsf.gd.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <p>
 * JsonIntentListParserUtils - 支持泛型的JSON解析工具类
 * </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @since 2025-07-08
 */
public class JsonIntentListParserUtils {
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    /**
     * 泛型方法：解析JSON数组为指定类型的List
     * 兼容前后有非json字符，提取第一个合法的json array
     * 
     * @param input 输入字符串
     * @param clazz 目标类型
     * @param <T> 泛型类型
     * @return 解析后的List
     */
    public static <T> List<T> parseList(String input, Class<T> clazz) {
        if (StringUtils.isEmpty(input)) {
            return new ArrayList<>();
        }
        List<T> result = new ArrayList<>();
        // 匹配第一个json array
        Pattern pattern = Pattern.compile("\\[.*?\\]", Pattern.DOTALL);
        Matcher matcher = pattern.matcher(input);
        if (matcher.find()) {
            String jsonArray = matcher.group();
            try {
                CollectionType listType = OBJECT_MAPPER.getTypeFactory().constructCollectionType(List.class, clazz);
                result = OBJECT_MAPPER.readValue(jsonArray, listType);
            } catch (JsonProcessingException e) {
                // ignore, return empty
            }
        }
        return result;
    }

    /**
     * 泛型方法：解析JSON对象为指定类型
     * 兼容前后有非json字符，提取第一个合法的json object
     * 
     * @param input 输入字符串
     * @param clazz 目标类型
     * @param <T> 泛型类型
     * @return 解析后的对象
     */
   /* public static <T> T parseObject(String input, Class<T> clazz) {
        if (StringUtils.isEmpty(input)) {
            return null;
        }
        // 匹配第一个json object
        Pattern pattern = Pattern.compile("\\{.*?\\}", Pattern.DOTALL);
        Matcher matcher = pattern.matcher(input);
        if (matcher.find()) {
            String jsonObject = matcher.group();
            try {
                JavaType javaType = OBJECT_MAPPER.getTypeFactory().constructType(clazz);
                return OBJECT_MAPPER.readValue(jsonObject, javaType);
            } catch (JsonProcessingException e) {
                // ignore, return null
            }
        }
        return null;
    }*/

    /**
     * 泛型方法：解析JSON对象为指定类型（支持递归自动映射所有字段，包括嵌套List和自定义对象）
     * 兼容前后有非json字符，提取第一个合法的json object（支持多行、markdown包裹等）
     *
     * @param input 输入字符串
     * @param clazz 目标类型
     * @param <T> 泛型类型
     * @return 解析后的对象
     */
    public static <T> T parseObject(String input, Class<T> clazz) {
        if (StringUtils.isEmpty(input)) {
            return null;
        }
        String jsonObject = extractFirstJsonObject(input);
        if (jsonObject != null) {
            try {
                return OBJECT_MAPPER.readValue(jsonObject, clazz);
            } catch (JsonProcessingException e) {
                // ignore, return null
            }
        }
        return null;
    }

    /**
     * 从混杂内容中提取第一个完整的JSON对象字符串（括号计数法，兼容多行、markdown等）
     */
    private static String extractFirstJsonObject(String input) {
        int start = -1, count = 0;
        boolean inString = false;
        char stringChar = 0;
        for (int i = 0; i < input.length(); i++) {
            char c = input.charAt(i);
            if (inString) {
                if (c == stringChar && (i == 0 || input.charAt(i - 1) != '\\')) {
                    inString = false;
                }
                continue;
            }
            if (c == '"' || c == '\'') {
                inString = true;
                stringChar = c;
                continue;
            }
            if (c == '{') {
                if (count == 0) start = i;
                count++;
            } else if (c == '}') {
                count--;
                if (count == 0 && start != -1) {
                    return input.substring(start, i + 1);
                }
            }
        }
        return null;
    }

    public static void main(String[] args) {
        String str = "(不确定前边还有啥字符串，也可能啥也没有）```json\n" +
                "{\n" +
                "    \"taskFeedback\": \"已收到您的需求，我将依次为您修改标题、更新联系人信息，丰富1.1商机管理部分内容，并润色2.1客户管理部分。\",\n" +
                "    \"taskList\": [\n" +
                "        {\n" +
                "            \"rewriteType\": \"REPLACE\",\n" +
                "            \"keyword\": \"将标题替换为公司考勤制度管理\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"rewriteType\": \"REPLACE\",\n" +
                "            \"keyword\": \"将联系人改为张三\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"rewriteType\": \"REWRITE\",\n" +
                "            \"keyword\": \"1.1商机管理部分\",\n" +
                "            \"rewriteDirection\": \"使内容更加丰富\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"rewriteType\": \"REWRITE\",\n" +
                "            \"keyword\": \"2.1客户管理部分\",\n" +
                "            \"rewriteDirection\": \"润色内容\"\n" +
                "        }\n" +
                "    ]\n" +
                "}\n" +
                "```（不确定后边还有啥字符串，也可能啥也没有）";

        AutofillVO autofillVO = parseObject(str, AutofillVO.class);
        System.err.println(JsonUtils.toJSONString(autofillVO));

        String str2 = "ccc{[\n" +
                "  {\n" +
                "    \"rewriteType\": \"REPLACE\",\n" +
                "    \"intent\": \"将租赁人姓名改为张三\"\n" +
                "  },\n" +
                "  {\n" +
                "    \"rewriteType\": \"REPLACE\",\n" +
                "    \"intent\": \"将租赁人电话改为11111222222\"\n" +
                "  },\n" +
                "  {\n" +
                "    \"rewriteType\": \"REPLACE\",\n" +
                "    \"intent\": \"将租赁人身份证号码改为12388383838\"\n" +
                "  },\n" +
                "  {\n" +
                "    \"rewriteType\": \"REWRITE\",\n" +
                "    \"keyword\": \"5.3 条款\",\n" +
                "    \"rewriteDirection\": \"增加一条违约责任信息，并使这部分内容更专业\"\n" +
                "  },\n" +
                "  {\n" +
                "    \"rewriteType\": \"REPLACE\",\n" +
                "    \"intent\": \"将合同标题改为销售合同\"\n" +
                "  },\n" +
                "  {\n" +
                "    \"rewriteType\": \"REPLACE\",\n" +
                "    \"intent\": \"将租赁地址改为北京市通州区\"\n" +
                "  },\n" +
                "  {\n" +
                "    \"rewriteType\": \"REPLACE\",\n" +
                "    \"intent\": \"将每月租金改为3000\"\n" +
                "  },\n" +
                "  {\n" +
                "    \"rewriteType\": \"REPLACE\",\n" +
                "    \"intent\": \"将付款方式改为押一付三\"\n" +
                "  }\n" +
                "]213213哇俄大使";


        List<AutofillDtlVO> autofillDtlVOS = JsonIntentListParserUtils.parseList(str2, AutofillDtlVO.class);
        System.err.println("泛型解析结果: " + JsonUtils.toJSONString(autofillDtlVOS));


        String strObj = "分析完毕，我将根据要求提取文档类型及概述：\n" +
                "\n" +
                "```json\n" +
                "{\"templateName\":\"合同类\",\"tag\":\"技术服务合同 / 技术服务 / 甲方立场 / 技术服务项目\"}\n" +
                "```";

        TemplateLibraryDtlVO templateLibraryDtlVO = parseObject(strObj, TemplateLibraryDtlVO.class);
        System.err.println(JsonUtils.toJSONString(templateLibraryDtlVO));
    }
}
