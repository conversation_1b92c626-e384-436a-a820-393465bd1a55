package com.jd.jdt.joycreator.ae.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 字典明细
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_2943_dict_dtl")
public class DictDtl extends BaseEntity<DictDtl> {

    private static final long serialVersionUID = 1L;

    /**
     * 字典id
     */
    private String dictId;

    /**
     * 字典值
     */
    private String value;

    /**
     * 字典排序
     */
    private Integer sort;

    /**
     * 字典描述
     */
    @TableField("`desc`")
    private String desc;


    public static final String DICT_ID = "dict_id";

    public static final String VALUE = "value";

    public static final String SORT = "sort";

    public static final String DESC = "desc";

    @Override
    protected Serializable pkVal() {
        return null;
    }

}
