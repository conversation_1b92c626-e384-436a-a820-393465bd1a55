package com.jd.jdt.joycreator.ae.pojo.vo;

import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 编辑器文档数据VO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-27
 */
@Data
public class EditDocumentVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 附件ID
     */
    private Long fileId;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 文件名
     */
    private String name;

    /**
     * 聊天记录id
     */
    private Long chatHistoryId;

    /**
     * 文档内容- html
     */
    private String documentContent;

    /**
     * 文本文档内容- text
     */
    private String textContent;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createdUser;

    /**
     * 最后修改者
     */
    private String modifiedUser;


   /* public String getTextContent() {
        if (StringUtils.isNoneBlank(textContent)) {
            return textContent = textContent.replaceAll(" ", " ");
        }
        return textContent;
    }*/
}
