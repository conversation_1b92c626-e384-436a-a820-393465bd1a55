package com.jd.jdt.joycreator.ae.controller;

import com.jd.jdt.app4s.component.common.api.entity.ResponseResult;
import com.jd.jdt.joycreator.ae.pojo.vo.DictVO;
import com.jd.jdt.joycreator.ae.service.DictService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 字典 服务接口
 * </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @since 2025-06-19
 */
@Log4j2
@RestController
@RequestMapping("/api/v1/dict")
public class DictController {

    @Autowired
    private DictService dictService;


    @GetMapping("/dtl/{dictCode}")
    public ResponseResult<DictVO> dtl(@PathVariable("dictCode") String dictCode) {
        return ResponseResult.success(dictService.dtl(dictCode));
    }

}
