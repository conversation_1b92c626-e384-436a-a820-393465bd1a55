package com.jd.jdt.joycreator.ae.rpc.feign;

import com.jd.jdt.joybuilder.permission.entity.ResponseResult;
import com.jd.jdt.joycreator.ae.config.FeignConfig;
import com.jd.jdt.joycreator.ae.pojo.dto.FileOriginDTO;
import com.jd.jdt.joycreator.ae.pojo.dto.OfficeConvertDTO;
import com.jd.jdt.joycreator.ae.pojo.dto.UploadFileDTO;
import com.jd.jdt.joycreator.ae.pojo.vo.OfficeConvertVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <p>
 * PanDoc文件转换接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
@FeignClient(name = "office-service", url = "${rpc.office.url}", configuration = FeignConfig.class)
public interface OfficeConvertService {


    @PostMapping(value = "/api/v1/pandoc/convert", consumes = "application/json", produces = "application/json")
    ResponseResult<UploadFileDTO> convert(@RequestBody() FileOriginDTO fileOriginDTO);

    @PostMapping(value = "/api/v1/office/convert", consumes = "application/json", produces = "application/json")
    ResponseResult<OfficeConvertVO> officeConvert(@RequestBody() OfficeConvertDTO officeConvertDTO);


}
