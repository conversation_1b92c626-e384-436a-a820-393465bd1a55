package com.jd.jdt.joycreator.ae.dao.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.jdt.joycreator.ae.entity.ChatHistory;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jd.jdt.joycreator.ae.pojo.vo.ChatHistoryListVO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 聊天记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-23
 */
public interface ChatHistoryMapper extends BaseMapper<ChatHistory> {

    /**
     * 分页查询聊天历史记录列表
     *
     * @param page         分页参数对象，包含当前页码、每页条数等信息
     * @param queryWrapper 查询条件构造器，用于构建SQL查询条件
     * @return 包含分页数据和聊天历史记录VO列表的分页对象
     */
    Page<ChatHistoryListVO> searchChatHistoryListVO(Page page, @Param(Constants.WRAPPER) QueryWrapper queryWrapper);
}
