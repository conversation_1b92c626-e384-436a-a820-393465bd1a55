package com.jd.jdt.joycreator.ae.rpc.feign;

import com.jd.jdt.joycreator.ae.config.FeignConfig;
import com.jd.jdt.joycreator.ae.pojo.dto.ChatCompletionResponseDTO;
import com.jd.jdt.joycreator.ae.pojo.dto.EmbeddingResponseDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import feign.Response;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Map;

/**
 * <p>
 * 大模型网关接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-09
 */
@FeignClient(name = "llm-gateway-service", url = "${rpc.llm.url}", configuration = FeignConfig.class)
public interface LLMGatewayService {


    /**
     * 见文档：https://joyspace.jd.com/pages/hQTBswr7k3AusgP2k2XZ
     * 聊天完成接口/流式
     *
     * @param params
     * @return
     */
    @PostMapping(value = "/v1/chat/completions", consumes = "application/json", produces = "text/event-stream")
    Response chatCompletionsStream(@RequestHeader("Authorization") String apiKey, @RequestBody Map<String, ?> params);

    /**
     * 见文档：https://joyspace.jd.com/pages/hQTBswr7k3AusgP2k2XZ
     * 聊天完成接口/非流式
     *
     * @param params
     * @return
     */
    @PostMapping(value = "/v1/chat/completions", consumes = "application/json", produces = "application/json")
    ChatCompletionResponseDTO chatCompletions(@RequestHeader("Authorization") String apiKey, @RequestBody Map<String, ?> params);

    @PostMapping(value = "/api/v1/provision-bank/search-char-stream", consumes = "application/json", produces = "text/event-stream")
    Response videosResult2(@RequestHeader("Cookie") String sso, @RequestBody Map<String, ?> params);

    /**
     * 向量查询
     *
     * @param apiKey
     * @param requestId
     * @param params
     * @return
     */
    @PostMapping(value = "/v1/embeddings", consumes = "application/json", produces = "application/json")
    EmbeddingResponseDTO embeddings(@RequestHeader("Authorization") String apiKey, @RequestHeader("x-ms-client-request-id") String requestId, @RequestBody Map<String, Object> params);
}
