package com.jd.jdt.joycreator.ae.pojo.dto;

import lombok.Data;

/**
 * 会话消息实体类
 * 表示会话中的单条消息
 */
@Data
public class ConversationMessage {
    
    /**
     * 消息发送者的角色（system、user、assistant）
     */
    private String role;
    
    /**
     * 消息内容
     */
    private String content;
    
    /**
     * 消息发送的时间戳
     */
    private Long timestamp;
    
    /**
     * 从消息中检测到的意图
     */
    private MessageIntent intent;
    
    /**
     * 消息的额外上下文/元数据
     */
    private MessageContext context;
} 