package com.jd.jdt.joycreator.ae.controller;

import com.jd.jdt.joybuilder.permission.entity.ResponseResult;
import com.jd.jdt.joycreator.ae.pojo.dto.*;
import com.jd.jdt.joycreator.ae.pojo.vo.AutofillExtractionVO;
import com.jd.jdt.joycreator.ae.pojo.vo.AutofillVO;
import com.jd.jdt.joycreator.ae.service.EditService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import java.util.List;

/**
 * <p>
 * 大模型网关对外接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-11
 */
@Log4j2
@RestController
@RequestMapping("/api/v1/llm")
public class EditController {

    @Autowired
    private EditService editService;

    /**
     * 聊天
     *
     * @param doronChatDTO
     * @return
     */
    @PostMapping(value = "/chat/doron", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Object doronChat(@RequestBody DoronChatDTO doronChatDTO) {
        return editService.doronChat(doronChatDTO);
    }

    /**
     * 文档起草
     *
     * @param documentWritingDTO
     * @return
     */
    @PostMapping(value = "/chat/create", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> chatCreate(@RequestBody DocumentWritingDTO documentWritingDTO) {
        return editService.chatCreate(documentWritingDTO);
    }

    /**
     * 改写
     *
     * @param chatRewriteDocumentDTO
     * @return
     */
    @PostMapping(value = "/chat/rewrite", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> chatRewrite(@RequestBody ChatRewriteDocumentDTO chatRewriteDocumentDTO) {
        return editService.chatRewrite(chatRewriteDocumentDTO);
    }

    /**
     * 文本处理
     *
     * @param textProcessingDTO
     * @return
     */
    @PostMapping(value = "/text/tools", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> processText(@RequestBody TextProcessingDTO textProcessingDTO) {
        return editService.processText(textProcessingDTO);
    }

    /**
     * 模版填充意图识别
     *
     * @param autofillDTO
     * @return
     */
    @PostMapping(value = "/chat/autofill")
    public ResponseResult<List<AutofillVO>> autofill(@RequestBody AutofillDTO autofillDTO) {
        return ResponseResult.success(editService.autofill(autofillDTO));
    }

    /**
     * 替换/改写抽取原文
     *
     * @param autofillExtractionDTO
     * @return
     */
    @PostMapping(value = "/chat/autofill-extraction")
    public ResponseResult<AutofillExtractionVO> autofillExtraction(@RequestBody AutofillExtractionDTO autofillExtractionDTO) {
        return ResponseResult.success(editService.autofillExtraction(autofillExtractionDTO));
    }

}
