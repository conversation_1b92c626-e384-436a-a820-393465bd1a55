package com.jd.jdt.joycreator.ae.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.jdt.joycreator.ae.entity.ChatHistory;
import com.jd.jdt.joycreator.ae.pojo.dto.ChatHistoryOperationDTO;
import com.jd.jdt.joycreator.ae.pojo.dto.CollectionDTO;
import com.jd.jdt.joycreator.ae.pojo.vo.BaseQueryVO;
import com.jd.jdt.joycreator.ae.pojo.vo.ChatHistoryListVO;

import java.util.List;

/**
 * <p>
 * 聊天记录 服务
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
public interface ChatHistoryService {

    /**
     * 创建会话
     *
     * @return
     */
    String create();

    /**
     * 查询聊天记录列表
     *
     * @return
     */
    List<ChatHistory> list();

    /**
     * 分页查询会话记录列表
     *
     * @return
     */
    Page<ChatHistoryListVO> searchChatHistoryListVO(BaseQueryVO<String> baseQueryVO);

    /**
     * 收藏
     *
     * @param collectionDTO
     * @return
     */
    Boolean updateCollection(CollectionDTO collectionDTO);

    /**
     * 删除会话记录
     *
     * @param sessionId
     * @return
     */
    Boolean del(String sessionId);
}
