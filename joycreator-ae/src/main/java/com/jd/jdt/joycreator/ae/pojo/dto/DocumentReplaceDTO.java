package com.jd.jdt.joycreator.ae.pojo.dto;

import lombok.Data;

/**
 * <p>
 * 文档替换数据传输对象
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Data
public class DocumentReplaceDTO {

    /**
     * 替换类型
     */
    private String type;

    /**
     * 锚点
     */
    private String anchor;

    /**
     * 原始内容
     */
    private String oldContent;

    /**
     * 新内容
     */
    private String newContent;

    /**
     * 任务反馈
     */
    private String taskFeedback;

}
