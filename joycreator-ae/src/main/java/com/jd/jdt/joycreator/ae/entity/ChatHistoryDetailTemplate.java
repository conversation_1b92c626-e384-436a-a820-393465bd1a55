package com.jd.jdt.joycreator.ae.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.jd.jdt.joycreator.ae.enums.SelectedEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 聊天记录明细模版
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_2943_chat_history_detail_template")
public class ChatHistoryDetailTemplate extends BaseEntity<ChatHistoryDetailTemplate> {

    private static final long serialVersionUID = 1L;

    /**
     * 聊天记录明细
     */
    private Long chatHistoryDetailId;

    /**
     * 模版
     */
    private Long templateId;

    /**
     * 是否选中
     */
    private SelectedEnum selected;


    public static final String CHAT_HISTORY_DETAIL_ID = "chat_history_detail_id";

    public static final String TEMPLATE_ID = "template_id";

    public static final String SELECTED = "selected";

    @Override
    protected Serializable pkVal() {
        return null;
    }

}
