package com.jd.jdt.joycreator.ae.config;

import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

/**
 * <p>
 * Feign响应日志/支持流式日志
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-10
 */
@Log4j2
public class LoggingInputStream extends InputStream {
    private final InputStream original;
    private final String configKey;
    private final long elapsedTime;
    private final StringBuilder buffer = new StringBuilder();

    public LoggingInputStream(InputStream original, String configKey, long elapsedTime) {
        this.original = original;
        this.configKey = configKey;
        this.elapsedTime = elapsedTime;
    }

    @Override
    public int read() throws IOException {
        int byteData = original.read();
        if (byteData != -1) {
            buffer.append((char) byteData);
            if (byteData == '\n') {
                logLine();
            }
        } else {
            if (buffer.length() > 0) {
                logLine();  // Log any remaining data
            }
        }
        return byteData;
    }

    private void logLine() {
        String line = new String(buffer.toString().getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8);
        log.info("CustomFeignLogger.logAndRebufferResponse，configKey：{}，响应时间：{}ms，响应出参：{}", configKey, elapsedTime, line);
        buffer.setLength(0);  // Clear the buffer
    }

    @Override
    public void close() throws IOException {
        original.close();
    }
}
