package com.jd.jdt.joycreator.ae.dao.plus;

import com.jd.jdt.joycreator.ae.entity.ChatHistoryDetail;
import com.jd.jdt.joycreator.ae.dao.IBaseDao;

import java.util.List;

/**
 * <p>
 * 聊天记录明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
public interface IChatHistoryDetailMapper extends IBaseDao<ChatHistoryDetail> {


    List<ChatHistoryDetail> listChatHistoryDetailBySessionId(Long chatHistoryId);

    List<ChatHistoryDetail> listChatHistoryDetailByChatHistoryId(Long chatHistoryId);

    ChatHistoryDetail chatHistoryDetailTemplateByLimitOne(Long chatHistoryId);

    Boolean delByChatHistoryId(Long chatHistoryId);

    Boolean confirmDoccontentRewriteBybusinessNo(String businessNo);

    ChatHistoryDetail getChatHistoryDetailByBusinessNo(String businessNo);

    Boolean updateChatContent(String businessNo, String chatContent);
}
