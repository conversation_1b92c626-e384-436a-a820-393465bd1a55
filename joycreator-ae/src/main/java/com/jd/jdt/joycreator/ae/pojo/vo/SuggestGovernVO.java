package com.jd.jdt.joycreator.ae.pojo.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 推荐管理-推荐管理配置VO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@Data
public class SuggestGovernVO {

    /**
     * 推荐名称
     */
    private String name;

    /**
     * 推荐分类枚举
     */
    private String recommendationCategoryEnum;

    /**
     * 推荐分类名称
     */
    private String category;

    /**
     * 推荐图标
     */
    private String icon;

    /**
     * 是否启用
     */
    private String isEnabled;

    /**
     * 推荐优先级
     */
    private BigDecimal priority;

    /**
     * 预置提示词
     */
    private String presetPrompt;


    /**
     * 图标背景色
     */
    private String background;

    /**
     * 推荐描述
     */
    private String recommendationDescription;


}
