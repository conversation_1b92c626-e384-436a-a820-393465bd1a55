package com.jd.jdt.joycreator.ae.dao.plus.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jd.jdt.joycreator.ae.entity.DictDtl;
import com.jd.jdt.joycreator.ae.dao.mapper.DictDtlMapper;
import com.jd.jdt.joycreator.ae.dao.plus.IDictDtlMapper;
import com.jd.jdt.joycreator.ae.dao.plus.IBaseMapperImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 字典明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Service
public class IDictDtlMapperImpl extends IBaseMapperImpl<DictDtlMapper, DictDtl> implements IDictDtlMapper {

    @Override
    public List<DictDtl> listDictDtlByDictId(Long dictId) {
        LambdaQueryWrapper<DictDtl> dictDtlLambdaQueryWrapper = new LambdaQueryWrapper<>();
        dictDtlLambdaQueryWrapper.eq(DictDtl::getDictId, dictId);
        return list(dictDtlLambdaQueryWrapper);
    }
}
