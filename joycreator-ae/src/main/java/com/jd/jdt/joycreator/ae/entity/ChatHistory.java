package com.jd.jdt.joycreator.ae.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jd.jdt.joycreator.ae.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 聊天记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_2943_chat_history")
public class ChatHistory extends BaseEntity<ChatHistory> {

    private static final long serialVersionUID = 1L;

    /**
     * 发起人erp
     */
    private String erp;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 收藏
     */
    private Boolean collection;


    public static final String ERP = "erp";

    public static final String SESSION_ID = "session_id";

    public static final String COLLECTION = "collection";

    @Override
    protected Serializable pkVal() {
        return null;
    }

}
