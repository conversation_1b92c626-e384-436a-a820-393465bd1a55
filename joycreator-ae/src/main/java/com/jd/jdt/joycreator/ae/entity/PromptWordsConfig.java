package com.jd.jdt.joycreator.ae.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.math.BigDecimal;

import com.jd.jdt.joycreator.ae.enums.PromptCodeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 提示词管理
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_2943_prompt_words_config")
public class PromptWordsConfig extends BaseEntity<PromptWordsConfig> {

    private static final long serialVersionUID = 1L;

    /**
     * 提示词编码
     */
    private PromptCodeEnum promptWordCode;

    /**
     * 模型
     */
    private String model;

    /**
     * 提示词来源
     */
    private String promptSource;

    /**
     * 提示词
     */
    private String promptWord;

    /**
     * 描述/说明
     */
    private String description;

    /**
     * 采样温度
     */
    private BigDecimal temperature;

    /**
     * 核取样
     */
    private BigDecimal topK;

    /**
     * 输出最大token数
     */
    private BigDecimal maxTokens;


    public static final String PROMPT_WORD_CODE = "prompt_word_code";

    public static final String MODEL = "model";

    public static final String PROMPT_SOURCE = "prompt_source";

    public static final String PROMPT_WORD = "prompt_word";

    public static final String DESCRIPTION = "description";

    public static final String TEMPERATURE = "temperature";

    public static final String TOP_K = "top_k";

    public static final String MAX_TOKENS = "max_tokens";

    @Override
    protected Serializable pkVal() {
        return null;
    }

}
