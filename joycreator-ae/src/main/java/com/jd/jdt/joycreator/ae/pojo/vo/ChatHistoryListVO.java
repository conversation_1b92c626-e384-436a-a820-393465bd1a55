package com.jd.jdt.joycreator.ae.pojo.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 聊天记录VO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Data
public class ChatHistoryListVO {

    private Long id;

    /**
     * nname
     */
    private String name;

    /**
     * 发起人erp
     */
    private String erp;

    /**
     * 发起人信息
     */
    private JdrUserVO jdrUserVO;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 收藏
     */
    private Boolean collection;

    /**
     * 记录明细
     */
    private List<ChatHistoryDetailTemplateVO> chatHistoryDetailTemplateList;

    protected Date createTime;
}
