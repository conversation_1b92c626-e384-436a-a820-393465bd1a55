package com.jd.jdt.joycreator.ae.service;

import com.jd.jdt.joycreator.ae.pojo.dto.ConversationMessage;
import com.jd.jdt.joycreator.ae.pojo.dto.MessageContext;
import com.jd.jdt.joycreator.ae.pojo.dto.MessageIntent;
import reactor.core.publisher.Flux;

import java.util.Map;

/**
 * 会话服务接口
 * 定义了处理用户消息、意图检测、生成澄清问题、执行工具和生成响应的核心功能
 */
public interface ConversationService {

    /**
     * 处理用户消息并生成适当的响应
     * 包括意图检测、需要时进行澄清，以及执行必要的工具
     * 
     * @param userMessage 用户发送的消息
     * @return 响应内容流
     */
    Flux<String> processUserMessage(ConversationMessage userMessage);
    
    /**
     * 从用户消息中检测意图
     * 分析用户输入，识别主要意图、参数和是否需要澄清
     * 
     * @param messageContent 用户消息内容
     * @return 结构化的意图对象
     */
    MessageIntent detectIntent(String messageContent);
    
    /**
     * 基于检测到的意图生成澄清问题
     * 当用户输入不够清晰或缺少关键信息时，生成相应的澄清问题
     * 
     * @param intent 检测到的意图
     * @return 包含澄清问题的会话消息
     */
    ConversationMessage generateClarifyingQuestions(MessageIntent intent);
    
    /**
     * 根据意图执行必要的工具或插件
     * 例如查询模板、检索参考资料等
     * 
     * @param intent 用户意图
     * @param context 消息上下文
     * @return 工具执行结果
     */
    Map<String, Object> executeTools(MessageIntent intent, MessageContext context);
    
    /**
     * 基于意图和上下文生成最终响应
     * 整合工具执行结果和用户需求，生成满足要求的写作内容
     * 
     * @param intent 用户意图
     * @param context 消息上下文
     * @return 响应内容流
     */
    Flux<String> generateResponse(MessageIntent intent, MessageContext context);
} 