package com.jd.jdt.joycreator.ae.service.impl;

import com.jd.jdt.joycreator.ae.dao.plus.IPromptWordsConfigMapper;
import com.jd.jdt.joycreator.ae.entity.PromptWordsConfig;
import com.jd.jdt.joycreator.ae.enums.PromptCodeEnum;
import com.jd.jdt.joycreator.ae.service.PromptWordsConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <p>
 * 提示词管理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Slf4j
@Service
public class PromptWordsConfigServiceImpl implements PromptWordsConfigService {

    @Autowired
    private IPromptWordsConfigMapper iPromptWordsConfigMapper;

    @Override
    public PromptWordsConfig buildPromptWordsConfig(PromptCodeEnum promptWordCode, String... params) {
        PromptWordsConfig promptWordsConfig = iPromptWordsConfigMapper.getPromptWordsConfigByCode(promptWordCode);
        if (Objects.isNull(promptWordsConfig)) {
            throw new RuntimeException("未找到对应的提示词!");
        }
        String promptWord = promptWordsConfig.getPromptWord();
        for (String param : params) {
            // 使用 Matcher.quoteReplacement() 来转义替换字符串中的特殊字符
            promptWord = promptWord.replaceFirst("\\{\\{input\\}\\}", java.util.regex.Matcher.quoteReplacement(param));
        }
        promptWordsConfig.setPromptWord(promptWord);
        return promptWordsConfig;
    }

}
