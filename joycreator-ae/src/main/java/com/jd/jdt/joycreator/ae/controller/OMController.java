package com.jd.jdt.joycreator.ae.controller;

import com.jd.jdt.app4s.component.common.api.entity.ResponseResult;
import com.jd.jdt.app4s.component.common.api.exception.BussinessBizException;
import com.jd.jdt.app4s.component.common.db.jdbc.InnerJdbcTemplate;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

/**
 * <AUTHOR>
 * @version 1.0
 * @date
 */
@Log4j2
@RestController()
@RequestMapping("/api/v1/om")
public class OMController {
    @Autowired
    private InnerJdbcTemplate innerJdbcTemplate;

    @PostMapping("/sql")
    @ResponseBody
    public ResponseResult sql(@RequestParam String sql) {
        String decode = null;
        try {
            decode = URLDecoder.decode(sql, "UTF-8");
            int execute = innerJdbcTemplate.execute(decode, null);
            return ResponseResult.success(execute);
        } catch (UnsupportedEncodingException e) {
            throw new BussinessBizException(e);
        }
    }

}
