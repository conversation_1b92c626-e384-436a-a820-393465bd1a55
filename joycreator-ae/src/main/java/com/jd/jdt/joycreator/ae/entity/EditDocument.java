package com.jd.jdt.joycreator.ae.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.jd.jdt.joycreator.ae.utils.MarkdownFormatCorrector;
import com.jd.jdt.joycreator.ae.utils.MarkdownParsingUtils;
import com.jd.jsf.gd.util.JsonUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 编辑器文档数据
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_2943_edit_document")
public class EditDocument extends BaseEntity<EditDocument> {

    private static final long serialVersionUID = 1L;

    /**
     * 文档内容
     */
    private String documentContent;

    /**
     * 文档文本内容
     */
    private String textContent;

    /**
     * 聊天记录id
     */
    private Long chatHistoryId;


    public static final String DOCUMENT_CONTENT = "document_content";

    public static final String TEXT_CONTENT = "text_content";

    public static final String CHAT_HISTORY_ID = "chat_history_id";

    @Override
    protected Serializable pkVal() {
        return null;
    }

    public static void main(String[] args) {
        String str = "<p style=\"line-height: 21.45pt; text-align: left\"><span style=\"font-size: 12pt; color: rgb(64, 64, 64)\"><b>男方</b>：<br>姓名：________ 身份证号：____________________<br>住址：____________________________________</span></p><p style=\"line-height: 21.45pt; text-align: left\"><span style=\"font-size: 12pt; color: rgb(64, 64, 64)\"><b>女方</b>：<br>姓名：________ 身份证号：____________________<br>住址：____________________________________</span></p>";
        String originalMd = new MarkdownParsingUtils().convertHtmlToMarkdown(str);
        System.err.println(originalMd);
        String correctedMd = "**男方** ：  \n姓名：张三 身份证号：111111111111111  \n住址：____________________________________\n\n**女方** ：  \n姓名：李四 身份证号：222222222222222222222  \n住址：____________________________________";
        System.err.println(correctedMd);
    }
}
