package com.jd.jdt.joycreator.ae.utils;

import com.jd.jdt.joycreator.ae.pojo.vo.CreateOutlineVO;
import com.vladsch.flexmark.html2md.converter.FlexmarkHtmlConverter;
import com.vladsch.flexmark.util.ast.Node;
import com.vladsch.flexmark.util.data.MutableDataSet;
import com.vladsch.flexmark.parser.Parser;
import com.vladsch.flexmark.ast.Heading;
import com.vladsch.flexmark.util.sequence.BasedSequence;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <p>
 * Markdown解析工具类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-09
 */
@Log4j2
@Component
public class MarkdownParsingUtils {

    private static final Pattern TITLE_PATTERN = Pattern.compile("^(#+)\\s*(.*)$");
    private static final Pattern SUMMARY_PATTERN = Pattern.compile("^summary：(.*)$");

    public static void main(String[] args) {
        String html = "<h3>4.2 验收标准</h3><p>买方应在交付完成后____个工作日内完成验收，验收条件包括但不限于：</p><ol class=\"tight\" data-tight=\"true\" type=\"decimal\" style=\"list-style-type: decimal\"><li><p>货物外观无破损、变形或污染；</p></li><li><p>规格型号与合同附录《技术参数表》完全一致；</p></li><li><p>随机抽取____%的样品进行功能性测试，合格率需达到100%。</p></li></ol><p>验收程序如下：</p><table style=\"table-layout: fixed; min-width: 300px\" data-table-id=\"table-1751253726635-ai4d13uyr\"><colgroup><col style=\"min-width: 100px\"><col style=\"min-width: 100px\"><col style=\"min-width: 100px\"></colgroup><tbody><tr><th colspan=\"1\" rowspan=\"1\"><p>步骤</p></th><th colspan=\"1\" rowspan=\"1\"><p>责任方</p></th><th colspan=\"1\" rowspan=\"1\"><p>操作要求</p></th></tr><tr><td colspan=\"1\" rowspan=\"1\"><p>初检</p></td><td colspan=\"1\" rowspan=\"1\"><p>买方</p></td><td colspan=\"1\" rowspan=\"1\"><p>核对货物数量及外包装完整性</p></td></tr><tr><td colspan=\"1\" rowspan=\"1\"><p>复检</p></td><td colspan=\"1\" rowspan=\"1\"><p>双方</p></td><td colspan=\"1\" rowspan=\"1\"><p>联合签署《验收确认书》或提出书面异议</p></td></tr><tr><td colspan=\"1\" rowspan=\"1\"><p>终验</p></td><td colspan=\"1\" rowspan=\"1\"><p>卖方</p></td><td colspan=\"1\" rowspan=\"1\"><p>对异议货物在____日内完成退换或修复</p></td></tr></tbody></table><p>验收异议需以《货物异常报告》形式列明缺陷细节，并附第三方检测机构出具的证明文件（如需）。未按期提出异议视为验收合格。</p>";
        MarkdownParsingUtils markdownParsingUtils = new MarkdownParsingUtils();
        String s = markdownParsingUtils.convertHtmlToMarkdown(html);
        System.err.println(s);
    }

    public List<HeadingContent> mdParsing(String markdownStr) {
        MutableDataSet options = new MutableDataSet();
        Parser parser = Parser.builder(options).build();
        Node document = parser.parse(markdownStr);
        List<HeadingContent> headingContents = extractHeadingsAndContent(document);
        return headingContents;
    }

    public String convertHtmlToMarkdown(String html) {
        Document document = Jsoup.parse(html);
        FlexmarkHtmlConverter converter = FlexmarkHtmlConverter.builder().build();
        String markdown = converter.convert(document.html());
        // 修正标题格式
        markdown = markdown.replaceAll(
                "(?m)^(.+)\\n=+\\s*$",
                "# $1"
        );
        markdown = markdown.replaceAll(
                "(?m)^(.+)\\n-+\\s*$",
                "## $1"
        );

        return markdown;
    }

    public CreateOutlineVO parseOutline(String input) {
        String[] lines = input.split("\n");
        List<CreateOutlineVO> stack = new ArrayList<>();
        CreateOutlineVO root = null;

        for (String line : lines) {
            line = line.trim();
            if (line.isEmpty()) {
                continue;
            }

            Matcher titleMatcher = TITLE_PATTERN.matcher(line);
            Matcher summaryMatcher = SUMMARY_PATTERN.matcher(line);

            if (titleMatcher.matches()) {
                int level = titleMatcher.group(1).length();
                String title = titleMatcher.group(2).trim();

                CreateOutlineVO node = new CreateOutlineVO();
                node.setTitle(title);
                node.setLevel(level);

                if (root == null) {
                    root = node;
                }

                while (!stack.isEmpty() && stack.get(stack.size() - 1).getLevel() >= level) {
                    stack.remove(stack.size() - 1);
                }

                if (!stack.isEmpty()) {
                    stack.get(stack.size() - 1).getCreateOutlineList().add(node);
                }

                stack.add(node);

            } else if (summaryMatcher.matches()) {
                String summary = summaryMatcher.group(1).trim();
                if (!stack.isEmpty()) {
                    stack.get(stack.size() - 1).setSummary(summary);
                }
            }
        }

        return root;
    }


    private List<HeadingContent> extractHeadingsAndContent(Node document) {
        List<HeadingContent> headingContents = new ArrayList<>();
        Heading currentHeading = null;
        StringBuilder currentContent = new StringBuilder();

        for (Node node = document.getFirstChild(); node != null; node = node.getNext()) {
            if (node instanceof Heading) {
                if (currentHeading != null) {
                    headingContents.add(new HeadingContent(currentHeading.getLevel(), currentHeading.getChars().toString(), currentContent.toString().trim()));
                }
                currentHeading = (Heading) node;
                currentContent.setLength(0);
            } else if (currentHeading != null) {
                BasedSequence text = node.getChars();
                currentContent.append(text).append("\n");
            }
        }

        if (currentHeading != null) {
            headingContents.add(new HeadingContent(currentHeading.getLevel(), currentHeading.getChars().toString(), currentContent.toString().trim()));
        }

        return headingContents;
    }

    @Data
    public class HeadingContent {
        private int headingLevel;
        private String headingText;
        private String content;
        private String headingTextAndContent;

        HeadingContent(int headingLevel, String headingText, String content) {
            this.headingLevel = headingLevel;
            this.headingText = headingText;
            this.content = content;
        }

        public String getHeadingTextAndContent() {
            return this.headingTextAndContent = headingText + "\n" + content;
        }

    }
}