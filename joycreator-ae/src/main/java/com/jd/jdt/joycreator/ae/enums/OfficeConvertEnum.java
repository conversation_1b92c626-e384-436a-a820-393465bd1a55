package com.jd.jdt.joycreator.ae.enums;

/**
 * <p>
 * Office 文件转换类型枚举
 * </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @since 2025-06-18
 */
public enum OfficeConvertEnum {

    DOCX_TO_HTML(".docx", ".html", 5, "docx转html"),
    HTML_TO_DOCX(".html", ".docx", 16, "html转word"),
    DOCX_TO_PDF(".docx", ".pdf", 17, "docx转pdf"),
    ;

    /**
     * 源文件后缀
     */
    private String sourceSuffix;

    /**
     * 目标文件后缀
     */
    private String targetSuffix;

    /**
     * 目标文件类型
     */
    private int targetType;

    /**
     * 描述
     */
    private String subDesc;

    OfficeConvertEnum(String sourceSuffix, String targetSuffix, int targetType, String subDesc) {
        this.sourceSuffix = sourceSuffix;
        this.targetSuffix = targetSuffix;
        this.targetType = targetType;
        this.subDesc = subDesc;
    }

    public String getSourceSuffix() {
        return sourceSuffix;
    }

    public String getTargetSuffix() {
        return targetSuffix;
    }

    public int getTargetType() {
        return targetType;
    }

    public String getSubDesc() {
        return subDesc;
    }
}
