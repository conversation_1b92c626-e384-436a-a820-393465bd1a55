package com.jd.jdt.joycreator.ae.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONArray;
import com.google.common.collect.Lists;
import com.jd.jdt.app4s.component.common.api.exception.BussinessBizException;
import com.jd.jdt.joybuilder.permission.entity.ResponseResult;
import com.jd.jdt.joycreator.ae.dao.plus.ITemplateLibraryMapper;
import com.jd.jdt.joycreator.ae.entity.ChatHistoryDetailTemplate;
import com.jd.jdt.joycreator.ae.entity.PromptWordsConfig;
import com.jd.jdt.joycreator.ae.entity.TemplateLibrary;
import com.jd.jdt.joycreator.ae.enums.*;
import com.jd.jdt.joycreator.ae.pojo.dto.*;
import com.jd.jdt.joycreator.ae.pojo.vo.*;
import com.jd.jdt.joycreator.ae.rpc.feign.OfficeConvertService;
import com.jd.jdt.joycreator.ae.service.EditDocumentService;
import com.jd.jdt.joycreator.ae.service.EditService;
import com.jd.jdt.joycreator.ae.service.PromptWordsConfigService;
import com.jd.jdt.joycreator.ae.service.TemplateLibraryService;
import com.jd.jdt.joycreator.ae.utils.JoyCreatorUtil;
import com.jd.jdt.joycreator.ae.utils.JsonIntentListParserUtils;
import com.jd.jdt.joycreator.ae.utils.WordToHtmlConverterUtil2;
import com.jd.jdt.joycreator.ae.utils.XmlParserUtil;
import com.jd.jsf.gd.util.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.C;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 模板库 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Log4j2
@Service
public class TemplateLibraryServiceImpl implements TemplateLibraryService {

    @Autowired
    private ITemplateLibraryMapper iTemplateLibraryMapper;
    @Autowired
    @Lazy
    private EditService editService;
    @Autowired
    private JoyCreatorUtil joyCreatorUtil;
    @Autowired
    private PromptWordsConfigService promptWordsConfigService;
    @Autowired
    private EditDocumentService editDocumentService;
    @Autowired
    private OfficeConvertService officeConvertService;


    public List<TemplateLibrary> listTemplateLibraryByTag(String tag, String name) {
        return iTemplateLibraryMapper.listTemplateLibraryByTag(tag, name);
    }

    @Override
    public TemplateLibrary getById(Long id) {
        return iTemplateLibraryMapper.getById(id);
    }


    @Override
    public Boolean buildTemplateLibraryBuffer(Queue<Object> contentBuffer, Queue<ChatHistoryDetailDTO> chatHistoryDetailDTOQueue, ToolDtlDTO toolDtlDTO, String messagesStr) {
        log.info("开始构建模板库缓存数据，执行中，toolDtlDTO：{}", JsonUtils.toJSONString(toolDtlDTO));
        if (toolDtlDTO.getToolLabels() != ToolLabelsEnum.TEMPLATE_LIBRARY) {
            return Boolean.FALSE;
        }
        //模版tools
        TemplateSimilarityQueriesDTO templateSimilarityQueriesDTO = this.searchTemplateLibrary(toolDtlDTO.getToolParams());
        log.info("开始构建模板库缓存数据，执行完成，templateSimilarityQueriesDTO：{}", JsonUtils.toJSONString(templateSimilarityQueriesDTO));
        if (Objects.isNull(templateSimilarityQueriesDTO)) {
            return Boolean.FALSE;
        }

        String businessNo = joyCreatorUtil.getBusinessNo(JoyCreateBusinessTypeEnum.LSMX);
        ChatCompletionChunkVO chatCompletionChunkVO = new ChatCompletionChunkVO();
        chatCompletionChunkVO.setBusinessNo(businessNo);
        chatCompletionChunkVO.setChatRole(ChatRoleEnum.TEMPLATE_LIBRARY);
        chatCompletionChunkVO.setNewContent(templateSimilarityQueriesDTO.getTemplateLibraryDtlVO());
        contentBuffer.add(chatCompletionChunkVO);
        //chat记录留痕
        ChatHistoryDetailDTO chatHistoryDetailDTO = new ChatHistoryDetailDTO();
        chatHistoryDetailDTO.setChatRole(ChatRoleEnum.TEMPLATE_LIBRARY);
        chatHistoryDetailDTO.setChatContent(JsonUtils.toJSONString(templateSimilarityQueriesDTO.getTemplateLibraryDtlVO()));
        chatHistoryDetailDTO.setBusinessNo(businessNo);
        chatHistoryDetailDTOQueue.add(chatHistoryDetailDTO);

        if (Objects.isNull(templateSimilarityQueriesDTO.getTemplateLibraryDtlVO())) {
            return Boolean.FALSE;
        }

        //call多意图
        AutofillVO autofill = this.autofill(messagesStr, contentBuffer, chatHistoryDetailDTOQueue);
        if (Objects.isNull(autofill) || CollectionUtils.isEmpty(autofill.getTaskList())) {
            String businessNo2 = joyCreatorUtil.getBusinessNo(JoyCreateBusinessTypeEnum.LSMX);
            ChatCompletionChunkVO chatCompletionChunkVO2 = new ChatCompletionChunkVO();
            chatCompletionChunkVO2.setBusinessNo(businessNo2);
            chatCompletionChunkVO2.setChatRole(ChatRoleEnum.TASK_FEEDBACK);
            chatCompletionChunkVO2.setNewContent(templateSimilarityQueriesDTO.getTaskFeedback());
            contentBuffer.add(chatCompletionChunkVO2);
            //chat记录留痕
            ChatHistoryDetailDTO chatHistoryDetailDTO2 = new ChatHistoryDetailDTO();
            chatHistoryDetailDTO2.setChatRole(ChatRoleEnum.TASK_FEEDBACK);
            chatHistoryDetailDTO2.setChatContent(templateSimilarityQueriesDTO.getTaskFeedback());
            chatHistoryDetailDTO2.setBusinessNo(businessNo2);
            chatHistoryDetailDTOQueue.add(chatHistoryDetailDTO2);
        }
        return Boolean.TRUE;
    }

    @Override
    public Long updateTemplateLibrary(MultipartFile file, String tag, String description) throws Exception {
        List<String> fileTypes = Lists.newArrayList("docx", "doc");
        String originalFilename = file.getOriginalFilename();
        if (StringUtils.isEmpty(originalFilename)) {
            throw new BussinessBizException("文件不能为空");
        }
        String fileSuffix = originalFilename.substring(originalFilename.lastIndexOf(".") + 1).toLowerCase();
        if (!fileTypes.contains(fileSuffix)) {
            throw new BussinessBizException("上传文件docx,doc文件类型");
        }

        String fileNameWithoutSuffix = originalFilename.substring(0, originalFilename.lastIndexOf("."));
        String docHtmlStr = WordToHtmlConverterUtil2.convertDocxToHtml(file.getInputStream());
        TemplateLibrary templateLibrary = new TemplateLibrary();
        templateLibrary.setName(fileNameWithoutSuffix);
        templateLibrary.setTag(tag);
        templateLibrary.setDescription(description);
        templateLibrary.setContent(docHtmlStr);
        iTemplateLibraryMapper.save(templateLibrary);
        return templateLibrary.getId();
    }

    @Override
    public Long uploadTemplateLibrary(MultipartFile file, OfficeConvertEnum officeConvert, String tag, String description) {
        try {
            String originalFilename = file.getOriginalFilename();
            if (StringUtils.isEmpty(originalFilename)) {
                throw new BussinessBizException("文件不能为空");
            }
            // 构造转换请求
            OfficeConvertDTO officeConvertDTO = new OfficeConvertDTO();
            officeConvertDTO.setFileName(originalFilename);
            officeConvertDTO.setOfficeConvert(officeConvert);
            officeConvertDTO.setByteArray(file.getBytes());
            ResponseResult<OfficeConvertVO> responseResult = officeConvertService.officeConvert(officeConvertDTO);
            if (responseResult == null || !responseResult.isSuccess() || responseResult.getData() == null) {
                throw new BussinessBizException("文件转换失败");
            }
            OfficeConvertVO officeConvertVO = responseResult.getData();
            byte[] convertedBytes = officeConvertVO.getByteArray();
            String contentStr = new String(convertedBytes, StandardCharsets.UTF_8);
            String fileNameWithoutSuffix = originalFilename.contains(".") ? originalFilename.substring(0, originalFilename.lastIndexOf(".")) : originalFilename;

            TemplateLibrary templateLibrary = new TemplateLibrary();
            templateLibrary.setName(fileNameWithoutSuffix);
            templateLibrary.setTag(tag);
            templateLibrary.setDescription(description);
            templateLibrary.setContent(contentStr);
            iTemplateLibraryMapper.save(templateLibrary);
            return templateLibrary.getId();
        } catch (Exception e) {
            throw new BussinessBizException("上传模板失败: " + e.getMessage());
        }
    }

    @Override
    public TemplateLibraryDtlVO documentAnalysis(DocumentAnalysisDTO documentAnalysisDTO) {
        if (Objects.isNull(documentAnalysisDTO) || StringUtils.isEmpty(documentAnalysisDTO.getTextContent())) {
            throw new BussinessBizException("请求必要参数为空!");
        }
        PromptWordsConfig promptWordsConfig = promptWordsConfigService.buildPromptWordsConfig(PromptCodeEnum.DOCUMENT_ANALYSIS, documentAnalysisDTO.getTextContent());
        ChatCompletionResponseDTO chatCompletionResponseDTO = editService.chatCompletions(promptWordsConfig);
        if (Objects.isNull(chatCompletionResponseDTO) || CollectionUtils.isEmpty(chatCompletionResponseDTO.getChoices()) ||
                Objects.isNull(chatCompletionResponseDTO.getChoices().get(0)) ||
                StringUtils.isBlank(chatCompletionResponseDTO.getChoices().get(0).getMessage().getContent())) {
            return null;
        }
        String content = chatCompletionResponseDTO.getChoices().get(0).getMessage().getContent();
        return JsonIntentListParserUtils.parseObject(content, TemplateLibraryDtlVO.class);
    }

    private void addChatHistoryQueue(Queue<ChatHistoryDetailDTO> chatHistoryDetailDTOQueue, List<TemplateLibrary> templateLibraries, StringBuffer contentBufferAll) {
        ChatHistoryDetailDTO chatHistoryDetailDTO = new ChatHistoryDetailDTO();
        chatHistoryDetailDTO.setChatRole(ChatRoleEnum.ASSISTANT);
        chatHistoryDetailDTO.setChatContent(contentBufferAll.toString());
        chatHistoryDetailDTOQueue.add(chatHistoryDetailDTO);
        log.info("聊天记录入队列，chatHistoryDetailDTO：{}", JsonUtils.toJSONString(chatHistoryDetailDTO));
        if (Objects.isNull(templateLibraries)) {
            return;
        }

        ChatHistoryDetailDTO chatHistoryDetailDTO2 = new ChatHistoryDetailDTO();
        chatHistoryDetailDTO2.setChatRole(ChatRoleEnum.TEMPLATE_LIBRARY_QUERY);
        chatHistoryDetailDTO2.setChatContent(ToolLabelsEnum.TEMPLATE_LIBRARY.getDesc());
        chatHistoryDetailDTOQueue.add(chatHistoryDetailDTO2);
        log.info("聊天记录入队列，chatHistoryDetailDTO2：{}", JsonUtils.toJSONString(chatHistoryDetailDTO2));

        ChatHistoryDetailDTO chatHistoryDetailDTO3 = new ChatHistoryDetailDTO();
        chatHistoryDetailDTO3.setChatRole(ChatRoleEnum.TEMPLATE_LIBRARY);
        chatHistoryDetailDTO3.setChatContent(JsonUtils.toJSONString(templateLibraries));
        chatHistoryDetailDTOQueue.add(chatHistoryDetailDTO3);
        if (CollectionUtils.isEmpty(templateLibraries)) {
            return;
        }
        List<ChatHistoryDetailTemplate> chatHistoryDetailTemplateList = Lists.newArrayList();
        for (TemplateLibrary templateLibrary : templateLibraries) {
            ChatHistoryDetailTemplate chatHistoryDetailTemplate = new ChatHistoryDetailTemplate();
            chatHistoryDetailTemplate.setTemplateId(templateLibrary.getId());
            chatHistoryDetailTemplateList.add(chatHistoryDetailTemplate);
        }
        chatHistoryDetailDTO3.setChatHistoryDetailTemplateList(chatHistoryDetailTemplateList);
        log.info("聊天记录入队列，chatHistoryDetailDTO3：{}", JsonUtils.toJSONString(chatHistoryDetailDTO3));
    }


    private TemplateSimilarityQueriesDTO searchTemplateLibrary(Map<String, Object> toolParams) {
        log.info("开始查询模板库，toolParams：{}", toolParams);
        if (MapUtil.isEmpty(toolParams)) {
            return null;
        }
        String templateName = toolParams.get("template_name").toString();
        List<TemplateLibrary> templateLibraryList = iTemplateLibraryMapper.listTemplateLibrary();
        PromptWordsConfig promptWordsConfig = promptWordsConfigService.buildPromptWordsConfig(PromptCodeEnum.TEMPLATE_SIMILARITY_QUERIES, templateName, JsonUtils.toJSONString(templateLibraryList));
        ChatCompletionResponseDTO chatCompletionResponseDTO = editService.chatCompletions(promptWordsConfig);
        if (Objects.isNull(chatCompletionResponseDTO) || CollectionUtils.isEmpty(chatCompletionResponseDTO.getChoices())) {
            return null;
        }
        List<ChatCompletionResponseDTO.ChoiceDTO> choices = chatCompletionResponseDTO.getChoices();
        if (Objects.isNull(choices.get(0).getMessage())) {
            return null;
        }
        String content = choices.get(0).getMessage().getContent();
        TemplateSimilarityQueriesDTO templateSimilarityQueriesDTO = JsonIntentListParserUtils.parseObject(content, TemplateSimilarityQueriesDTO.class);
        if (Objects.isNull(templateSimilarityQueriesDTO) || Objects.isNull(templateSimilarityQueriesDTO.getId())) {
            return templateSimilarityQueriesDTO;
        }
        TemplateLibrary templateLibrary = iTemplateLibraryMapper.getById(templateSimilarityQueriesDTO.getId());
        log.info("查询模板库完成，templateLibrariesAll：{}", JsonUtils.toJSONString(templateLibrary));
        if (Objects.isNull(templateLibrary)) {
            return templateSimilarityQueriesDTO;
        }
        TemplateLibraryDtlVO templateLibraryDtlVO = new TemplateLibraryDtlVO();
        BeanUtils.copyProperties(templateLibrary, templateLibraryDtlVO);
        templateLibraryDtlVO.setTemplateName(templateLibrary.getName());
        templateSimilarityQueriesDTO.setTemplateLibraryDtlVO(templateLibraryDtlVO);
        return templateSimilarityQueriesDTO;
    }

    private List<Long> parseIds(String idsStr) {
        if (StringUtils.isBlank(idsStr)) {
            return null;
        }
        int startIndex = idsStr.indexOf('[');
        int endIndex = idsStr.lastIndexOf(']') + 1;
        List<Long> ids = Lists.newArrayList();
        if (startIndex != -1 && endIndex != -1) {
            String jsonArrayPart = idsStr.substring(startIndex, endIndex);
            try {
                JSONArray jsonArray = new JSONArray(jsonArrayPart);
                for (int i = 0; i < jsonArray.size(); i++) {
                    ids.add(jsonArray.getLong(i));
                }
            } catch (Exception e) {
                log.error("Error parsing JSON array：{}", e);
            }
        } else {
            log.warn("No valid JSON array found in the string.");
        }

        return ids;
    }


    private AutofillVO autofill(String messagesStr, Queue<Object> contentBuffer, Queue<ChatHistoryDetailDTO> chatHistoryDetailDTOQueue) {
        log.info("模版自动填充关键列表抽,autofillDTO：{}", messagesStr);
        //根据用户chat上下文分析用户语义
        PromptWordsConfig promptWordsConfig = promptWordsConfigService.buildPromptWordsConfig(PromptCodeEnum.AUTOFILL, messagesStr);
        ChatCompletionResponseDTO chatCompletionResponseDTO = editService.chatCompletions(promptWordsConfig);
        String content = chatCompletionResponseDTO.getChoices().get(0).getMessage().getContent();
        AutofillVO autofillVO = JsonIntentListParserUtils.parseObject(content, AutofillVO.class);
        log.info("模版自动填充关键列表抽取,autofillVOList:{}", JsonUtils.toJSONString(autofillVO));
        String businessNo = joyCreatorUtil.getBusinessNo(JoyCreateBusinessTypeEnum.LSMX);
        ChatCompletionChunkVO chatCompletionChunkVO = new ChatCompletionChunkVO();
        chatCompletionChunkVO.setBusinessNo(businessNo);
        chatCompletionChunkVO.setChatRole(ChatRoleEnum.AUTOFILL);
        chatCompletionChunkVO.setNewContent(autofillVO);
        contentBuffer.add(chatCompletionChunkVO);
        //历史记录保存
        ChatHistoryDetailDTO chatHistoryDetailDTO = new ChatHistoryDetailDTO();
        chatHistoryDetailDTO.setBusinessNo(businessNo);
        chatHistoryDetailDTO.setChatRole(ChatRoleEnum.AUTOFILL);
        chatHistoryDetailDTO.setChatContent(JsonUtils.toJSONString(autofillVO));
        chatHistoryDetailDTOQueue.add(chatHistoryDetailDTO);

        if (!CollectionUtils.isEmpty(autofillVO.getTaskList())) {
            String businessNo2 = joyCreatorUtil.getBusinessNo(JoyCreateBusinessTypeEnum.LSMX);
            ChatCompletionChunkVO chatCompletionChunkVO2 = new ChatCompletionChunkVO();
            chatCompletionChunkVO2.setBusinessNo(businessNo2);
            chatCompletionChunkVO2.setChatRole(ChatRoleEnum.TASK_FEEDBACK);
            chatCompletionChunkVO2.setNewContent(autofillVO.getTaskFeedback());
            contentBuffer.add(chatCompletionChunkVO2);
            //历史记录保存
            ChatHistoryDetailDTO chatHistoryDetailDTO2 = new ChatHistoryDetailDTO();
            chatHistoryDetailDTO2.setBusinessNo(businessNo2);
            chatHistoryDetailDTO2.setChatRole(ChatRoleEnum.TASK_FEEDBACK);
            chatHistoryDetailDTO2.setChatContent(autofillVO.getTaskFeedback());
            chatHistoryDetailDTOQueue.add(chatHistoryDetailDTO2);
        }
        return autofillVO;
    }


}
