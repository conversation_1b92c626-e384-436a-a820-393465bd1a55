package com.jd.jdt.joycreator.ae.pojo.vo;

import com.jd.jdt.joycreator.ae.enums.SelectedEnum;
import lombok.Data;

/**
 * <p>
 * 聊天记录明细模版VO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-24
 */
@Data
public class ChatHistoryDetailTemplateVO {


    /**
     * 聊天记录明细
     */
    private Long chatHistoryDetailId;

    /**
     * 模版
     */
    private String templateId;

    /**
     * 是否选中
     */
    private SelectedEnum selected;

    /**
     * 模版名称
     */
    private String templateName;

    /**
     * 模版描述
      */
    private String description;

}
