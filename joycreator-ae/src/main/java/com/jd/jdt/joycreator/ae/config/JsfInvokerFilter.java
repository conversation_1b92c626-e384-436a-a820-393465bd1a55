package com.jd.jdt.joycreator.ae.config;

import com.alibaba.fastjson.JSON;
import com.jd.jdt.app4s.component.common.api.entity.ResponseResult;
import com.jd.jdt.app4s.component.common.api.exception.BussinessBizException;
import com.jd.jsf.gd.filter.AbstractFilter;
import com.jd.jsf.gd.msg.Invocation;
import com.jd.jsf.gd.msg.RequestMessage;
import com.jd.jsf.gd.msg.ResponseMessage;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class JsfInvokerFilter extends AbstractFilter {

    private static final String SPLIT= "=============================================================";
    private static final String pack="com.jd.jdt.joycreator";
    @Override
    public ResponseMessage invoke(RequestMessage request) {
        long start = System.currentTimeMillis();
        Invocation invocation = request.getInvocationBody();
        String method = invocation.getMethodName();
        String alias = invocation.getAlias();

        Object[] args = invocation.getArgs();
        String[] argsType = invocation.getArgsType();
        String argTypes="";
        String clazzName = invocation.getClazzName();
        if(!clazzName.startsWith(pack)) {
            ResponseMessage responseMessage = this.getNext().invoke(request);
            return responseMessage;
        }
        if(argsType.length!=0) {
            StringBuilder params = new StringBuilder();
            for (String argType : argsType) {
                params.append(argType);
                params.append(",");
            }
            argTypes= params.substring(0,params.length()-1);
        }
        printRequest(alias,clazzName,argTypes,method,JSON.toJSONString(args));
        // 执行接口调用逻辑
        ResponseMessage responseMessage = this.getNext().invoke(request);
        Throwable exception = responseMessage.getException();
        if(exception!=null) {
            if (exception instanceof BussinessBizException) {
                log.error("业务异常信息, ex:[{}]", exception.getMessage(), exception);
                ResponseResult<Object> fail = ResponseResult.error(((BussinessBizException) exception).getCode()==0?500:((BussinessBizException) exception).getCode(), (exception.getMessage()));
                responseMessage.setResponse(fail);
                responseMessage.setException(null);
            } else {
                log.error("系统异常信息, ex:[{}]", exception.getMessage(), exception);
                ResponseResult<Object> fail = ResponseResult.error();
                responseMessage.setResponse(fail);
                responseMessage.setException(null);
            }
        }
        long end = System.currentTimeMillis();
        printResponse(clazzName, argTypes,method,JSON.toJSONString (responseMessage.getResponse()),(end - start));
        return responseMessage;
    }


    private void printRequest(String alias,String clazzName, String argsType, String method,String args){
            log.info(SPLIT);
            log.info("||Received  [{}] requestInterface: {}#{}({})", alias,clazzName,method,argsType);
            log.info("||**Request**PARAMS****: {}",args);
            log.info(SPLIT);
    }
    /**
     *
     * @param responseBody 返回结果
     * @param time  接口请求耗时
     * <AUTHOR>
     */
    private void printResponse(String clazzName, String argsType, String method,String responseBody, long time) {
            log.info(SPLIT);
            log.info("||**Response**BODY***: {}", responseBody);
            log.info("||********耗时********: requestInterface: {}#{}({}),耗时:{}毫秒!",clazzName,method,argsType,time);
            log.info(SPLIT);
    }
}
