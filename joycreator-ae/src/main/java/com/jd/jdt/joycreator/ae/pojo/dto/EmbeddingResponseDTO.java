package com.jd.jdt.joycreator.ae.pojo.dto;

import lombok.Data;
import java.util.List;

/**
 * <p>
 * 向量嵌入响应DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-29
 */
@Data
public class EmbeddingResponseDTO {
    private List<EmbeddingDataDTO> data;
    private UsageDTO usage;
    private String model;
    private String object;

    @Data
    public static class EmbeddingDataDTO {
        private Integer index;
        private List<Double> embedding;
        private String object;
    }

    @Data
    public static class UsageDTO {
        private Integer prompt_tokens;
        private Integer total_tokens;
    }
}
