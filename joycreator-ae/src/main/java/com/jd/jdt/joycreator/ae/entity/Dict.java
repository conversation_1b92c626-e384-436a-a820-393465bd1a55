package com.jd.jdt.joycreator.ae.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jd.jdt.joycreator.ae.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import com.jd.jdt.joycreator.ae.enums.DictCodeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 字典-字典配置
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_2943_dict")
public class Dict extends BaseEntity<Dict> {

    private static final long serialVersionUID = 1L;

    /**
     * 字典编码
     */
    private DictCodeEnum dictCode;

    /**
     * 是否启用
     */
    private String enabled;

    /**
     * 字典描述
     */
    private String dictDesc;


    public static final String DICT_CODE = "dict_code";

    public static final String ENABLED = "enabled";

    public static final String DICT_DESC = "dict_desc";

    @Override
    protected Serializable pkVal() {
        return null;
    }

}
