package com.jd.jdt.joycreator.ae.pojo.dto;

import com.jd.jdt.joycreator.ae.enums.PanDocConvertTypeEnum;
import lombok.Data;

/**
 * <p>
 * Word Convert PDF FileOriginDTO
 * </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @since 2024-8-16
 */
@Data
public class FileOriginDTO {

    /**
     * 转换类型
     */
    private PanDocConvertTypeEnum panDocConvertType;

    /**
     * 桶名称
     */
    private String bucket;

    /**
     * 文件访问路径（目前没用到，通过文件名称和桶名称从OSS获取）
     */
    private String originUrl;

    /**
     * 文件名称
     */
    private String originName;

    /**
     * 文件类型
     */
    private String originType;

    /**
     * 文档字节数组
     */
    private byte[] byteArray;


}
