package com.jd.jdt.joycreator.ae.config;

import feign.Logger;
import feign.Request;
import feign.Response;
import lombok.extern.log4j.Log4j2;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * <p>
 * Feign请求响应统一日志
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-09
 */
@Log4j2
public class CustomFeignLogger extends Logger {


    @Override
    protected void log(String configKey, String format, Object... args) {
        log.info(String.format(methodTag(configKey) + format, args));
    }

    @Override
    protected void logRequest(String configKey, Level logLevel, Request request) {
        super.logRequest(configKey, logLevel, request);
        if (logLevel.ordinal() >= Level.BASIC.ordinal()) {
            String requestBody = null;
            byte[] body = request.body();
            if (Objects.nonNull(body)) {
                requestBody = new String(body, StandardCharsets.UTF_8);
            }
            log.info("CustomFeignLogger.logRequest，configKey：{}，请求类型：{}，请求url：{}, headers：{}, 请求入参：{}", configKey, request.httpMethod(), request.url(), request.headers(), requestBody);
        }
    }

    @Override
    protected Response logAndRebufferResponse(String configKey, Level logLevel, Response response, long elapsedTime) throws IOException {
        String bodyData = null;
        Response.Body body = response.body();
        if (Objects.nonNull(body)) {
            InputStream originalInputStream = body.asInputStream();
            InputStream loggingInputStream = new LoggingInputStream(originalInputStream, configKey, elapsedTime);
            // 创建一个新的 Response 对象，使用新的 InputStream
            response = response.toBuilder().body(loggingInputStream, body.length()).build();
        }
//        log.info("CustomFeignLogger.logAndRebufferResponse，configKey：{}，响应状态码：{}, 响应时间：{}ms，响应出参：{}", configKey, response.status(), elapsedTime, bodyData);
        return response;
    }

    private String readInputStream(InputStream inputStream) throws IOException {
        StringBuilder sb = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
        }
        return sb.toString();
    }
}

