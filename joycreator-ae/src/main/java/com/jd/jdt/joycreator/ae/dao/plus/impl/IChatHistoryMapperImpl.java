package com.jd.jdt.joycreator.ae.dao.plus.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.jdt.app4s.component.archetype.common.holder.LcdpLoginContext;
import com.jd.jdt.app4s.component.archetype.common.holder.LcdpUserInfo;
import com.jd.jdt.app4s.component.common.api.exception.BussinessBizException;
import com.jd.jdt.joycreator.ae.cache.RLock;
import com.jd.jdt.joycreator.ae.dao.plus.IEditDocumentMapper;
import com.jd.jdt.joycreator.ae.entity.ChatHistory;
import com.jd.jdt.joycreator.ae.dao.mapper.ChatHistoryMapper;
import com.jd.jdt.joycreator.ae.dao.plus.IChatHistoryMapper;
import com.jd.jdt.joycreator.ae.dao.plus.IBaseMapperImpl;
import com.jd.jdt.joycreator.ae.entity.EditDocument;
import com.jd.jdt.joycreator.ae.pojo.dto.CollectionDTO;
import com.jd.jdt.joycreator.ae.pojo.vo.ChatHistoryListVO;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 聊天记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
@Log4j2
@Service
public class IChatHistoryMapperImpl extends IBaseMapperImpl<ChatHistoryMapper, ChatHistory> implements IChatHistoryMapper {

    @Autowired
    private IEditDocumentMapper iEditDocumentMapper;

    @Override
    public List<ChatHistory> listChatHistoryDetailByErp(String erp) {
        LambdaQueryWrapper<ChatHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChatHistory::getErp, erp);
        queryWrapper.orderByDesc(ChatHistory::getId);
        queryWrapper.select(ChatHistory::getId, ChatHistory::getErp, ChatHistory::getCreateTime);
        return list(queryWrapper);
    }

    @Override
    public ChatHistory chatHistoryBySessionId(String sessionId) {
        LambdaQueryWrapper<ChatHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChatHistory::getSessionId, sessionId);
        return getOne(queryWrapper);
    }

    @Override
    public Page<ChatHistoryListVO> searchChatHistoryListVO(Page page, QueryWrapper queryWrapper) {
        return baseMapper.searchChatHistoryListVO(page, queryWrapper);
    }

    @Override
    public Boolean updateCollection(CollectionDTO collectionDTO) {
        LambdaUpdateWrapper<ChatHistory> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ChatHistory::getSessionId, collectionDTO.getSessionId());
        updateWrapper.set(ChatHistory::getCollection, collectionDTO.getCollection());
        return update(updateWrapper);
    }

    @Override
    public Boolean delBySessionId(String sessionId) {
        LambdaUpdateWrapper<ChatHistory> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ChatHistory::getSessionId, sessionId);
        return remove(updateWrapper);
    }

    @Override
    public void create(String sessionId) {
        if (StringUtils.isEmpty(sessionId)) {
            throw new BussinessBizException("sessionId不能为空");
        }
        RLock lock = RLock.on(sessionId);
        log.info("添加分布式锁key:{}", sessionId);
        try {
            if (lock.tryLock(15000, 20000)) {
                ChatHistory chatHistory = chatHistoryBySessionId(sessionId);
                if (Objects.nonNull(chatHistory)) {
                    EditDocument editDocument = iEditDocumentMapper.dtlByChatHistoryId(chatHistory.getId());
                    if (Objects.isNull(editDocument)) {
                        EditDocument editDocumentNew = new EditDocument();
                        editDocumentNew.setChatHistoryId(chatHistory.getId());
                        iEditDocumentMapper.save(editDocumentNew);
                    }
                    return;
                }
                LcdpUserInfo user = LcdpLoginContext.getUser();
                String userName = user.getUserName();
                ChatHistory chatHistoryNew = new ChatHistory();
                chatHistoryNew.setErp(userName);
                chatHistoryNew.setSessionId(sessionId);
                save(chatHistoryNew);
                EditDocument editDocument = iEditDocumentMapper.dtlByChatHistoryId(chatHistoryNew.getId());
                if (Objects.isNull(editDocument)) {
                    EditDocument editDocumentNew = new EditDocument();
                    editDocumentNew.setChatHistoryId(chatHistoryNew.getId());
                    iEditDocumentMapper.save(editDocumentNew);
                }
            }
        } catch (InterruptedException e) {
            log.error("添加分布式锁key加锁异常：{}", sessionId, e);
        } finally {
            log.info("分布式锁key解锁完成：{}", sessionId);
            lock.unlock();
        }
    }

    @Override
    public List<ChatHistory> chatHistoryByIds(List<Long> ids) {
        LambdaQueryWrapper<ChatHistory> chatHistoryLambdaQueryWrapper = new LambdaQueryWrapper<>();
        chatHistoryLambdaQueryWrapper
                .in(ChatHistory::getId, ids)
                .select(ChatHistory::getId, ChatHistory::getSessionId);
        List<ChatHistory> chatHistoryList = list(chatHistoryLambdaQueryWrapper);
        if (CollectionUtils.isEmpty(chatHistoryList)) {
            return Lists.newArrayList();
        }

        return chatHistoryList;
    }


}
