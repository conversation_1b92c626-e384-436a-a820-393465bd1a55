package com.jd.jdt.joycreator.ae.pojo.dto;

import lombok.Data;
import java.util.Map;

/**
 * 消息意图实体类
 * 表示从用户消息中检测到的意图信息
 */
@Data
public class MessageIntent {
    
    /**
     * 主要意图类型（例如：创建合同、设计文档等）
     */
    private String intentType;
    
    /**
     * 意图检测的置信度分数（0-1）
     */
    private Double confidence;
    
    /**
     * 从消息中提取的实体/参数
     * 例如：{"contractType": "PURCHASE", "partyRole": "SELLER"}
     */
    private Map<String, Object> parameters;
    
    /**
     * 此意图是否需要进一步澄清
     */
    private Boolean needsClarification;
    
    /**
     * 需要澄清的方面
     */
    private String[] clarificationPoints;
    
    /**
     * 此意图需要的工具/插件
     */
    private String[] requiredTools;
} 