package com.jd.jdt.joycreator.ae.config;

import com.jd.jdt.joycreator.ae.enums.PromptSourceEnum;

/**
 * <p>
 * 提示词来源上下文
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
public class PromptSourceContext {

    private static final ThreadLocal<PromptSourceEnum> THREAD_LOCAL = new ThreadLocal<>();

    public static void setPromptSource(PromptSourceEnum t) {
        THREAD_LOCAL.set(t);
    }

    public static <T extends PromptSourceEnum> T getPromptSource(Class<T> tClass) {
        return (T)(tClass.cast(THREAD_LOCAL.get()));
    }

    public static PromptSourceEnum getPromptSource() {
        return (PromptSourceEnum)THREAD_LOCAL.get();
    }

    public static void removePromptSource() {
        THREAD_LOCAL.remove();
    }
}
