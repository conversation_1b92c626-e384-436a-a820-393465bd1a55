package com.jd.jdt.joycreator.ae.enums;

public enum SelectedEnum {
    Y("Y", "是"),
    N("N", "否");

    /**
     * code
     */
    private String value;
    /**
     * 描述
     */
    private String desc;

    SelectedEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static SelectedEnum getEnum(String value) {
        for (SelectedEnum a : SelectedEnum.values()) {
            if (a.value.equals(value)) {
                return a;
            }
        }
        return null;
    }

    public String getDesc() {
        return desc;
    }

    public String getValue() {
        return value;
    }
}
