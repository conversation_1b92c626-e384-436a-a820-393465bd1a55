package com.jd.jdt.joycreator.ae.config;

import com.jd.jdt.app4s.component.appstarter.web.AppInterceptor;
import lombok.Data;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ImportResource;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
/**
 * <AUTHOR>
 * @version 1.0
 * @date
 */
@Data
@ControllerAdvice
@Configuration
@ImportResource(locations = {
        "classpath:rpc/jsf-consumer.xml",
        "classpath:rpc/jsf-provider.xml"}
)
public class WebMvcConfig implements WebMvcConfigurer {
    @Resource
    private AppInterceptor appInterceptor;
    @Resource
    private AppStarterLoginInterceptorAdapterEdit appStarterLoginInterceptorAdapterEdit;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        String[] excludePathPatterns = {"/api/XXX/**"};
        String[] pathPatterns = {"/api/v1/**","/api/v2/**"};
        // 执行顺序 为加入先后顺序
        registry.addInterceptor(appStarterLoginInterceptorAdapterEdit).addPathPatterns(pathPatterns).excludePathPatterns(excludePathPatterns);
        registry.addInterceptor(appInterceptor).order(9999);
    }

    @Bean
    public CorsFilter corsFilter() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", corsConfig());
        return new CorsFilter(source);
    }

    private CorsConfiguration corsConfig() {
        CorsConfiguration corsConfiguration = new CorsConfiguration();
        corsConfiguration.setAllowCredentials(true);
        // 允许访问的客户端域名
        List<String> allowedOriginPatterns = new ArrayList<>();
        allowedOriginPatterns.add("*");
        corsConfiguration.setAllowedOriginPatterns(allowedOriginPatterns);
        // 允许任何头
        corsConfiguration.addAllowedHeader("*");
        // 允许任何方法（post、get等）
        corsConfiguration.addAllowedMethod("*");
        return corsConfiguration;
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 脚手架 构建时将静态资源复制到 resources/templates/ 目录下
        String classpath = "classpath:/templates/";
        // 渲染器的静态资源映射
        registry.addResourceHandler("/**").addResourceLocations(classpath);
    }

}
