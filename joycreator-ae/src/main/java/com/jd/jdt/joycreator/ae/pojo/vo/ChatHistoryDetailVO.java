package com.jd.jdt.joycreator.ae.pojo.vo;

import com.jd.jdt.joycreator.ae.enums.ChatRoleEnum;
import com.jd.jdt.joycreator.ae.enums.SelectedEnum;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 聊天记录明细VO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
@Data
public class ChatHistoryDetailVO {


    /**
     * 聊天记录ID
     */
    private Long chatHistoryId;

    /**
     * 会话明细业务唯一编号
     */
    private String businessNo;

    /**
     * 角色
     */
    private ChatRoleEnum chatRole;

    /**
     * 内容
     */
    private Object chatContent;

    /**
     * 是否选中
     */
    private SelectedEnum selected;


}
