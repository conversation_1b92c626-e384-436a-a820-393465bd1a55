package com.jd.jdt.joycreator.ae.pojo.vo;

import com.jd.jdt.joycreator.ae.enums.DictCodeEnum;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 字典-字典配置VO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@Data
public class DictVO {

    /**
     * 字典名称
     */
    private String name;

    /**
     * 字典编码
     */
    private DictCodeEnum dictCode;

    /**
     * 是否启用
     */
    private String enabled;

    /**
     * 字典描述
     */
    private String dictDesc;

    /**
     * 字典明细list
     */
    private List<DictDtlVO> dictDtlList;

}
