package com.jd.jdt.joycreator.ae.pojo.dto;

import lombok.Data;
import java.util.Map;

@Data
public class IntentRecognitionResult {
    private String intent;  // 主要意图，如 "contract_writing", "product_design", etc.
    private String subIntent; // 子意图，如 "purchase_contract", "service_contract", etc.
    private Double confidence; // 置信度
    private Map<String, Object> parameters; // 提取的参数
    private String clarificationQuestion; // 需要澄清的问题
    private boolean needsClarification; // 是否需要澄清
    private String toolName; // 需要调用的工具名称，如果有的话
    private Map<String, Object> toolParameters; // 工具调用参数
} 