package com.jd.jdt.joycreator.ae.dao.plus;

import com.jd.jdt.joycreator.ae.entity.ChatHistoryDetailTemplate;
import com.jd.jdt.joycreator.ae.dao.IBaseDao;
import com.jd.jdt.joycreator.ae.pojo.vo.ChatHistoryDetailTemplateVO;

import java.util.List;

/**
 * <p>
 * 聊天记录明细模版 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-23
 */
public interface IChatHistoryDetailTemplateMapper extends IBaseDao<ChatHistoryDetailTemplate> {

    List<ChatHistoryDetailTemplateVO> listChatHistoryDetailTemplateByChatHistoryId(Long id);

    Boolean delByChatHistoryId(Long chatHistoryId);

    Boolean confirmTemplateLibraryByTemplateId(String businessNo, String templateId);
}
