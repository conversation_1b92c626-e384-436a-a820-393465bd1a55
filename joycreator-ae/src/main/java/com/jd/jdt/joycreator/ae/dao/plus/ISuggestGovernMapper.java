package com.jd.jdt.joycreator.ae.dao.plus;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jd.jdt.joycreator.ae.entity.SuggestGovern;
import com.jd.jdt.joycreator.ae.dao.IBaseDao;
import java.util.List;

/**
 * <p>
 * 推荐管理-推荐管理配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
public interface ISuggestGovernMapper extends IBaseDao<SuggestGovern> {

    /**
     * 查询未删除的推荐管理配置列表
     * @return List<SuggestGovern>
     */
    List<SuggestGovern> listSuggestGovern();

}
