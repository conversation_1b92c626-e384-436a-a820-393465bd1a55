package com.jd.jdt.joycreator.ae.dao.plus.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jd.jdt.joycreator.ae.config.PromptSourceContext;
import com.jd.jdt.joycreator.ae.entity.PromptWordsConfig;
import com.jd.jdt.joycreator.ae.dao.mapper.PromptWordsConfigMapper;
import com.jd.jdt.joycreator.ae.dao.plus.IPromptWordsConfigMapper;
import com.jd.jdt.joycreator.ae.dao.plus.IBaseMapperImpl;
import com.jd.jdt.joycreator.ae.enums.PromptCodeEnum;
import com.jd.jdt.joycreator.ae.enums.PromptSourceEnum;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <p>
 * 提示词管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Service
public class IPromptWordsConfigMapperImpl extends IBaseMapperImpl<PromptWordsConfigMapper, PromptWordsConfig> implements IPromptWordsConfigMapper {


    @Override
    public PromptWordsConfig getPromptWordsConfigByCode(PromptCodeEnum promptWordCode) {
        LambdaQueryWrapper<PromptWordsConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PromptWordsConfig::getPromptWordCode, promptWordCode);
        PromptSourceEnum promptSource = PromptSourceContext.getPromptSource();
        if (Objects.nonNull(promptSource)) {
            queryWrapper.eq(PromptWordsConfig::getPromptWordCode, promptSource);
        }
        return getOne(queryWrapper);
    }
}
