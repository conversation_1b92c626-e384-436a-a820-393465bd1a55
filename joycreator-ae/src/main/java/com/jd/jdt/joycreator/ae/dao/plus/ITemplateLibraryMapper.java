package com.jd.jdt.joycreator.ae.dao.plus;

import com.jd.jdt.joycreator.ae.entity.TemplateLibrary;
import com.jd.jdt.joycreator.ae.dao.IBaseDao;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 模板库 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
public interface ITemplateLibraryMapper extends IBaseDao<TemplateLibrary> {

    List<TemplateLibrary> listTemplateLibraryByTag(String tag, String name);

    List<TemplateLibrary> listTemplateLibrary();

    List<TemplateLibrary> listTemplateLibraryByIds(List<Long> ids);

    List<TemplateLibrary> listTemplateLibraryByLimit(Long limit);
}
