package com.jd.jdt.joycreator.ae.enums;

/**
 * <p>
 * pandoc 文件转换类型枚举
 * </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @since 2025-04-14
 */
public enum PanDocConvertTypeEnum {

    MD_TO_DOCX("markdown", "docx", "markdown转word"),
    HTML_TO_DOCX("html", "docx", "html转word"),
    MD_TO_PDF("markdown", "pdf", "markdown转pdf"),
    ;

    /**
     * 源文件类型
     */
    private String sourceType;
    /**
     * 目标文件类型
     */
    private String targetType;
    /**
     * 描述
     */
    private String subDesc;


    PanDocConvertTypeEnum(String sourceType, String targetType, String subDesc) {
        this.sourceType = sourceType;
        this.targetType = targetType;
        this.subDesc = subDesc;
    }

    public String getSourceType() {
        return sourceType;
    }

    public String getTargetType() {
        return targetType;
    }

    public String getSubDesc() {
        return subDesc;
    }
}
