package com.jd.jdt.joycreator.ae.dao.plus.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jd.jdt.joycreator.ae.dao.plus.IDictDtlMapper;
import com.jd.jdt.joycreator.ae.entity.Dict;
import com.jd.jdt.joycreator.ae.dao.mapper.DictMapper;
import com.jd.jdt.joycreator.ae.dao.plus.IDictMapper;
import com.jd.jdt.joycreator.ae.dao.plus.IBaseMapperImpl;
import com.jd.jdt.joycreator.ae.entity.DictDtl;
import com.jd.jdt.joycreator.ae.enums.DictCodeEnum;
import com.jd.jdt.joycreator.ae.enums.SelectedEnum;
import com.jd.jdt.joycreator.ae.pojo.dto.DictDTO;
import com.jd.jdt.joycreator.ae.pojo.dto.DictDtlDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 字典-字典配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Service
public class IDictMapperImpl extends IBaseMapperImpl<DictMapper, Dict> implements IDictMapper {

    @Autowired
    private IDictDtlMapper iDictDtlMapper;

    @Override
    public DictDTO getDictByCode(String dictCode) {
        LambdaQueryWrapper<Dict> dictLambdaQueryWrapper = new LambdaQueryWrapper<>();
        dictLambdaQueryWrapper
                .eq(Dict::getDictCode, dictCode)
                .eq(Dict::getEnabled, SelectedEnum.Y.getValue());
        Dict dict = getOne(dictLambdaQueryWrapper);
        if (Objects.isNull(dict)) {
            return null;
        }
        DictDTO dictDTO = new DictDTO();
        BeanUtils.copyProperties(dict, dictDTO);
        List<DictDtl> dictDtlList = iDictDtlMapper.listDictDtlByDictId(dict.getId());
        if (CollectionUtils.isEmpty(dictDtlList)) {
            return dictDTO;
        }
        List<DictDtlDTO> dictDtlDTOList = dictDtlList.stream().map(dt -> {
            DictDtlDTO dictDtlDTO = new DictDtlDTO();
            BeanUtils.copyProperties(dt, dictDtlDTO);
            return dictDtlDTO;
        }).collect(Collectors.toList());
        dictDTO.setDictDtlList(dictDtlDTOList);
        return dictDTO;
    }
}
