package com.jd.jdt.joycreator.ae.pojo.dto;

import lombok.Data;

import java.util.List;

/**
 * <p>
 * 多伦Chat请求DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Data
public class DoronChatDTO {

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 消息列表
     */
    private List<DoronChatMessageDTO> messages;

    /**
     * 是否流式输出
     */
    private Boolean stream = Boolean.TRUE;

}
