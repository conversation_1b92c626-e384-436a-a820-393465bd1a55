package com.jd.jdt.joycreator.ae.utils;

import com.aspose.words.Document;
import com.aspose.words.HtmlSaveOptions;
import com.aspose.words.SaveFormat;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;

/**
 * <p>
 * Word To Html 工具类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
public class WordToHtmlConverterUtil2 {

    /**
     * 将Word文档的InputStream转换为HTML字符串
     * @param inputStream Word文档的输入流
     * @return 转换后的HTML字符串（只包含body内容，样式内嵌）
     * @throws Exception 转换过程中的异常
     */
    public static String convertDocxToHtml(InputStream inputStream) throws Exception {
        // Load the document from InputStream
        Document doc = new Document(inputStream);

        // Configure HTML save options with basic settings
        HtmlSaveOptions saveOptions = new HtmlSaveOptions(SaveFormat.HTML);

        // 完全禁用所有外部资源生成，只生成单个HTML文件
        saveOptions.setExportFontResources(false); // 禁用字体资源导出
        saveOptions.setExportTextInputFormFieldAsText(true); // 将表单字段导出为文本
        saveOptions.setExportImagesAsBase64(true); // 将图片嵌入为base64
        saveOptions.setExportOriginalUrlForLinkedImages(false); // 禁用链接图片
        saveOptions.setPrettyFormat(true); // 格式化HTML输出
        
        // 禁用页面设置导出，减少不必要的样式
        saveOptions.setExportPageSetup(false);
        
        // 设置CSS样式模式为内联，确保所有样式都嵌入到HTML中
        saveOptions.setCssStyleSheetType(com.aspose.words.CssStyleSheetType.INLINE);
        
        // 禁用相对字体大小，使用绝对值
        saveOptions.setExportRelativeFontSize(false);
        
        // 禁用导出字体到文件夹 - 不设置字体文件夹避免NullPointerException
        saveOptions.setExportFontsAsBase64(false);

        // Save the document to ByteArrayOutputStream
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        doc.save(outputStream, saveOptions);
        
        // Convert to string
        String htmlContent = outputStream.toString("UTF-8");
        
        // Post-process the HTML to fix font weight issues and extract body content
        return postProcessHtmlContent(htmlContent);
    }
    
    /**
     * Post-process the generated HTML to fix font weight issues and move head content to body
     * @param htmlContent 原始HTML内容
     * @return 处理后的HTML内容（只包含body内容，样式内嵌）
     */
    private static String postProcessHtmlContent(String htmlContent) {
        try {
            // Extract head content (style and meta tags)
            String headContent = "";
            java.util.regex.Pattern headPattern = java.util.regex.Pattern.compile(
                "<head[^>]*>(.*?)</head>", 
                java.util.regex.Pattern.DOTALL | java.util.regex.Pattern.CASE_INSENSITIVE
            );
            java.util.regex.Matcher headMatcher = headPattern.matcher(htmlContent);
            if (headMatcher.find()) {
                headContent = headMatcher.group(1);
            }
            
            // Fix CSS font-face declarations to remove unwanted bold weights for common Chinese fonts
            String[] chineseFonts = {"仿宋", "黑体", "宋体", "楷体", "微软雅黑", "SimSun", "SimHei", "KaiTi", "FangSong"};
            for (String font : chineseFonts) {
                headContent = headContent.replaceAll(
                    "@font-face\\s*\\{\\s*font-family:\\s*" + font + "\\s*;\\s*font-weight:\\s*bold\\s*;", 
                    "@font-face { font-family:" + font + ";"
                );
            }
            
            // Process the HTML content to fix font-weight issues
            htmlContent = fixFontWeightInContent(htmlContent, chineseFonts);
            
            // Remove the entire HTML structure and keep only body content with embedded styles
            java.util.regex.Pattern bodyPattern = java.util.regex.Pattern.compile(
                "<body[^>]*>(.*?)</body>", 
                java.util.regex.Pattern.DOTALL | java.util.regex.Pattern.CASE_INSENSITIVE
            );
            java.util.regex.Matcher bodyMatcher = bodyPattern.matcher(htmlContent);
            
            if (bodyMatcher.find()) {
                String bodyContent = bodyMatcher.group(1);
                
                // Create new content with head styles moved to body
                StringBuilder newContent = new StringBuilder();
                
                // Add the head content (styles, meta tags) at the beginning of body
                if (!headContent.trim().isEmpty()) {
                    newContent.append(headContent);
                }
                
                // Add the original body content
                newContent.append(bodyContent);
                
                // 压缩HTML换行但保留元素间空格
                return compressHtmlWhitespace(newContent.toString());
            }
            
            // If no body found, return processed content as is
            return compressHtmlWhitespace(htmlContent);
            
        } catch (Exception e) {
            System.err.println("Error post-processing HTML: " + e.getMessage());
            e.printStackTrace();
            return htmlContent; // Return original content if processing fails
        }
    }
    
    /**
     * 压缩HTML中的换行和多余空白字符，但保留元素之间的空格
     * @param htmlContent HTML内容
     * @return 压缩后的HTML内容
     */
    private static String compressHtmlWhitespace(String htmlContent) {
        if (htmlContent == null || htmlContent.trim().isEmpty()) {
            return htmlContent;
        }
        
        try {
            // 1. 移除HTML标签之间的换行符和多余空白，但保留单个空格
            // 匹配 >空白字符< 的模式，替换为 > <
            htmlContent = htmlContent.replaceAll(">\\s+<", "> <");
            
            // 2. 移除标签内部的多余换行和空白（但不影响标签内容）
            // 处理标签开始和结束之间的换行
            htmlContent = htmlContent.replaceAll(">\\s*\\n\\s*([^<])", ">$1");
            htmlContent = htmlContent.replaceAll("([^>])\\s*\\n\\s*<", "$1<");
            
            // 3. 压缩连续的空白字符为单个空格（在文本内容中）
            htmlContent = htmlContent.replaceAll("\\s{2,}", " ");
            
            // 4. 移除行首行尾的空白
            htmlContent = htmlContent.replaceAll("(?m)^\\s+|\\s+$", "");
            
            // 5. 特殊处理：确保某些重要标签之间保留适当的空格
            // 保留 span、a、strong、em 等内联元素之间的空格
            htmlContent = htmlContent.replaceAll("(</(?:span|a|strong|em|b|i|u)>)(<(?:span|a|strong|em|b|i|u)[^>]*>)", "$1 $2");
            
            // 6. 移除多余的连续空格，但保留单个空格
            htmlContent = htmlContent.replaceAll(" {2,}", " ");
            
            return htmlContent.trim();
            
        } catch (Exception e) {
            System.err.println("Error compressing HTML whitespace: " + e.getMessage());
            e.printStackTrace();
            return htmlContent; // 如果压缩失败，返回原内容
        }
    }
    
    /**
     * 修复HTML内容中的字体加粗问题
     * @param htmlContent HTML内容
     * @param chineseFonts 中文字体数组
     * @return 修复后的HTML内容
     */
    private static String fixFontWeightInContent(String htmlContent, String[] chineseFonts) {
        // 对每种中文字体进行处理
        for (String font : chineseFonts) {
            // 1. 移除普通文本中不必要的font-weight:bold
            // 匹配 font-family:字体名; font-weight:bold 但不包含特殊背景色或特殊标记
            htmlContent = htmlContent.replaceAll(
                "font-family:\\s*" + font + "\\s*;\\s*font-weight:\\s*bold(?!\\s*;\\s*background-color:\\s*#[a-fA-F0-9]{6})", 
                "font-family:" + font
            );
            
            // 2. 处理style属性中的字体加粗问题
            htmlContent = htmlContent.replaceAll(
                "style\\s*=\\s*[\"']([^\"']*?)font-family:\\s*" + font + "\\s*;\\s*font-weight:\\s*bold\\s*;?([^\"']*?)[\"']",
                "style=\"$1font-family:" + font + ";$2\""
            );
            
            // 3. 处理反向顺序的样式 font-weight:bold; font-family:字体名
            htmlContent = htmlContent.replaceAll(
                "font-weight:\\s*bold\\s*;\\s*font-family:\\s*" + font + "(?!\\s*;\\s*background-color:\\s*#[a-fA-F0-9]{6})", 
                "font-family:" + font
            );
        }
        
        // 4. 处理通用的font-weight:bold移除（针对中文内容）
        // 保留标题、强调等需要加粗的内容，移除普通段落中的加粗
        htmlContent = htmlContent.replaceAll(
            "<p([^>]*?)style\\s*=\\s*[\"']([^\"']*?)font-weight:\\s*bold\\s*;?([^\"']*?)[\"']([^>]*?)>([^<]*?[\u4e00-\u9fa5]+[^<]*?)</p>",
            "<p$1style=\"$2$3\"$4>$5</p>"
        );
        
        // 5. 处理span标签中的不必要加粗
        htmlContent = htmlContent.replaceAll(
            "<span([^>]*?)style\\s*=\\s*[\"']([^\"']*?)font-weight:\\s*bold\\s*;?([^\"']*?)[\"']([^>]*?)>([^<]*?[\u4e00-\u9fa5]+[^<]*?)</span>",
            "<span$1style=\"$2$3\"$4>$5</span>"
        );
        
        // 6. 清理多余的分号和空格
        htmlContent = htmlContent.replaceAll(";;+", ";");
        htmlContent = htmlContent.replaceAll("style\\s*=\\s*[\"']\\s*;", "style=\"");
        htmlContent = htmlContent.replaceAll("style\\s*=\\s*[\"'];([^\"']*?)[\"']", "style=\"$1\"");
        
        return htmlContent;
    }

    /**
     * 兼容旧版本的文件路径方法
     */
    public static void convertDocxToHtml(String inputFilePath, String outputFilePath) throws Exception {
        try (java.io.FileInputStream inputStream = new java.io.FileInputStream(inputFilePath)) {
            String htmlContent = convertDocxToHtml(inputStream);
            java.nio.file.Files.write(java.nio.file.Paths.get(outputFilePath), htmlContent.getBytes("UTF-8"));
            System.out.println("Document converted successfully from " + inputFilePath + " to " + outputFilePath);
        }
    }

    public static void main(String[] args) {
        // Example usage with file paths:
        String inputDocxPath = "/Users/<USER>/IdeaProjects/JoyCreator/joycreator-ae/src/main/resources/scripts/【样例】技术服务协议模板（jd为销售方通用版）.docx";
        String outputHtmlPath = "/Users/<USER>/IdeaProjects/JoyCreator/joycreator-ae/src/main/resources/scripts/output2.html";

        try {
            convertDocxToHtml(inputDocxPath, outputHtmlPath);
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        // Example usage with InputStream:
        /*
        try (FileInputStream inputStream = new FileInputStream(inputDocxPath)) {
            String htmlContent = convertDocxToHtml(inputStream);
            System.out.println("HTML Content: " + htmlContent);
        } catch (Exception e) {
            e.printStackTrace();
        }
        */
    }
}
