package com.jd.jdt.joycreator.ae.pojo.dto;

import lombok.Data;
import java.util.Map;

/**
 * 消息上下文实体类
 * 保存消息的附加上下文和元数据信息
 */
@Data
public class MessageContext {
    
    /**
     * 此消息所属的会话ID
     */
    private String conversationId;
    
    /**
     * 此消息回复的上一条消息的引用
     */
    private String replyToMessageId;
    
    /**
     * 会话流程中的当前阶段
     * （例如：意图检测、澄清、工具执行、响应生成）
     */
    private String conversationStage;
    
    /**
     * 任何工具/插件执行的结果
     */
    private Map<String, Object> toolResults;
    
    /**
     * 选中的模板或资源
     */
    private Map<String, Object> selectedResources;
    
    /**
     * 与消息相关的任何其他元数据
     */
    private Map<String, Object> metadata;
} 