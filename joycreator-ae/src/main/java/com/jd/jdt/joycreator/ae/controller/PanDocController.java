package com.jd.jdt.joycreator.ae.controller;

import com.jd.jdt.app4s.component.common.api.entity.ResponseResult;
import com.jd.jdt.joycreator.ae.pojo.dto.FileStrDTO;
import com.jd.jdt.joycreator.ae.pojo.dto.UploadFileDTO;
import com.jd.jdt.joycreator.ae.service.FileConvertService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * pandoc
 * </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @since 2025-04-14
 */
@Log4j2
@RestController
@RequestMapping("/api/v1/pandoc")
public class PanDocController {

    @Autowired
    private FileConvertService panDocService;


    @PostMapping("/convert-str")
    public ResponseResult<UploadFileDTO> convert(@RequestBody() FileStrDTO fileStrDTO) {
        return ResponseResult.success(panDocService.convertByStr(fileStrDTO));
    }

}
