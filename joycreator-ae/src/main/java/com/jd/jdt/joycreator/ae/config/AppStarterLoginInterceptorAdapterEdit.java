package com.jd.jdt.joycreator.ae.config;

import com.jd.cvp.common.util.StringUtil;
import com.jd.jdt.app4s.component.appstarter.web.AppStarterLoginInterceptorAdapter;
import com.jd.jdt.app4s.component.archetype.common.holder.LcdpLoginContext;
import com.jd.jdt.app4s.component.archetype.common.holder.LcdpUserInfo;
import com.jd.jdt.joycreator.ae.enums.PromptSourceEnum;
import com.jd.jdt.joycreator.ae.pojo.vo.DictDtlVO;
import com.jd.jdt.joycreator.ae.pojo.vo.DictVO;
import com.jd.jdt.joycreator.ae.service.DictService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 登录拦截器适配器，增加IP白名单功能
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@Slf4j
@Component
public class AppStarterLoginInterceptorAdapterEdit extends AppStarterLoginInterceptorAdapter {

    @Autowired
    private DictService dictService;

    /**
     * 预处理方法，在请求处理之前进行调用
     * 对特定IP放行，其它IP执行父类方法
     *
     * @param request  请求对象
     * @param response 响应对象
     * @param handler  处理器
     * @return 是否继续执行
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String promptSource = request.getHeader("prompt_source");
        PromptSourceEnum promptSourceEnum = PromptSourceEnum.getEnum(promptSource);
        if (Objects.nonNull(promptSourceEnum)) {
            PromptSourceContext.setPromptSource(promptSourceEnum);
        }

        String ip = getIpAddress(request);
        log.info("当前请求IP: {}", ip);
        DictVO dictVO = dictService.dtl("IP_WHITE");
        if (Objects.nonNull(dictVO) && CollectionUtils.isNotEmpty(dictVO.getDictDtlList())) {
            List<DictDtlVO> dictDtlList = dictVO.getDictDtlList();
            Set<String> ipWhiteSet = dictDtlList.stream().map(DictDtlVO::getValue).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(ipWhiteSet) && ipWhiteSet.contains(ip)) {
                log.info("IP {} 在白名单中，直接放行", ip);
                // 获取请求头中的user_name参数值
                String userName = request.getHeader("user_name");
                log.info("前请求user_name: {}", userName);
                if (StringUtil.isNotBlank(userName)) {
                    log.info("设置当前请求user_name: {}，为登陆用户。", userName);
                    LcdpUserInfo lcdpUserInfo = new LcdpUserInfo();
                    lcdpUserInfo.setPersonId(userName);
                    lcdpUserInfo.setUserName(userName);
                    LcdpLoginContext.setUser(lcdpUserInfo);
                }
                return true;
            }
        }


        // 不在白名单中，执行父类方法
        log.info("IP {} 不在白名单中，执行标准登录验证", ip);
        return super.preHandle(request, response, handler);
    }

    @Override
    public void afterCompletion(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, Exception e) {
        super.afterCompletion(httpServletRequest, httpServletResponse, o, e);
        PromptSourceContext.removePromptSource();
    }

    /**
     * 获取请求的真实IP地址
     *
     * @param request 请求对象
     * @return IP地址
     */
    private String getIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        // 如果是多级代理，取第一个IP地址
        if (ip != null && ip.contains(",")) {
            ip = ip.substring(0, ip.indexOf(",")).trim();
        }
        return ip;
    }
}
