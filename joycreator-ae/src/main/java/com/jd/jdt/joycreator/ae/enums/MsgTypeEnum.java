package com.jd.jdt.joycreator.ae.enums;

/**
 * <p>
 * 输入类型枚举
 * </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @since 2025-06-23
 */
public enum MsgTypeEnum {

    TEXT("TEXT", "文本"),
    FILE("FILE", "文件"),
    EXCERPT("EXCERPT", "原文引用"),
    ;

    /**
     * 源文件类型
     */
    private String Type;

    /**
     * 描述
     */
    private String desc;


    MsgTypeEnum(String type, String desc) {
        this.Type = type;
        this.desc = desc;
    }

    public static MsgTypeEnum getEnum(String type) {
        for (MsgTypeEnum a : MsgTypeEnum.values()) {
            if (a.getType().equals(type)) {
                return a;
            }
        }
        return null;
    }

    public String getType() {
        return Type;
    }

    public void setType(String type) {
        Type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
