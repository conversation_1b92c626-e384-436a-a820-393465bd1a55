package com.jd.jdt.joycreator.ae.config;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.transfer.TransferManager;
import com.amazonaws.services.s3.transfer.TransferManagerBuilder;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * AmazonS3Helper
 * </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @since 2025-04-14
 */
@Component
public class AmazonS3Helper {
    @Autowired
    private OssConfig ossConfig;
    private static ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(10, 100, 60, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(100), new ThreadFactoryBuilder().setNameFormat("oss-thread-poot-%d").build());

    @Bean
    public AmazonS3 amazonS3Client() {
        BasicAWSCredentials basicAWSCredentials = new BasicAWSCredentials(ossConfig.getAccessKey(), ossConfig.getSecretKey());
        ClientConfiguration clientConfiguration = new ClientConfiguration();
        clientConfiguration.withSignerOverride("S3SignerType");
        clientConfiguration.setProtocol(Objects.equals(ossConfig.getProtocol(), "http") ? Protocol.HTTP : Protocol.HTTPS);
        return AmazonS3ClientBuilder.standard()
                .withCredentials(new AWSStaticCredentialsProvider(basicAWSCredentials))
                .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(ossConfig.getIntranetEndpoint(), null))
                .withPathStyleAccessEnabled(true)
                .withClientConfiguration(clientConfiguration)
                .disableChunkedEncoding()
                .withPathStyleAccessEnabled(true)
                .build();

    }

    @Bean
    public TransferManager transferManager() {
        return TransferManagerBuilder.standard()
                .withS3Client(amazonS3Client())
                .withMultipartUploadThreshold(20 * 1024 * 1024L)
                .withExecutorFactory(() -> threadPoolExecutor)
                .withMinimumUploadPartSize(10 * 1024 * 1024L)
                .build();

    }
}
