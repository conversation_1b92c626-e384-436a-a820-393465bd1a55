package com.jd.jdt.joycreator.ae.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.regex.Pattern;

/**
 * <p>
 * 模糊匹配(滑动窗口)
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
public class FuzzyTextMatcherUtils2 {
    private static final Pattern SYMBOL_PATTERN = Pattern.compile("[\\p{Punct}\\p{Space}\\n\\r]+", Pattern.UNICODE_CHARACTER_CLASS);

    public static class MatchResult {
        public int startIdx;
        public int endIdx;
        public String matchedText;
        public int totalMismatch;
        public int maxContinuousMismatch;
        public long costMillis;
        public MatchResult(int startIdx, int endIdx, String matchedText, int totalMismatch, int maxContinuousMismatch, long costMillis) {
            this.startIdx = startIdx;
            this.endIdx = endIdx;
            this.matchedText = matchedText;
            this.totalMismatch = totalMismatch;
            this.maxContinuousMismatch = maxContinuousMismatch;
            this.costMillis = costMillis;
        }
    }

    // 归一化文本，返回归一化字符串和原文位置映射
    private static class NormalizedText {
        String normalized;
        int[] origPos; // normalized[i]对应原文的下标
        NormalizedText(String normalized, int[] origPos) {
            this.normalized = normalized;
            this.origPos = origPos;
        }
    }

    private static NormalizedText normalize(String text, boolean ignoreSymbols, boolean caseSensitive) {
        StringBuilder sb = new StringBuilder();
        List<Integer> posList = new ArrayList<>();
        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);
            if (ignoreSymbols && SYMBOL_PATTERN.matcher(String.valueOf(c)).matches()) {
                continue;
            }
            char nc = caseSensitive ? c : Character.toLowerCase(c);
            sb.append(nc);
            posList.add(i);
        }
        int[] posArr = posList.stream().mapToInt(Integer::intValue).toArray();
        return new NormalizedText(sb.toString(), posArr);
    }

    // 配置类合并为静态内部类
    public static class FuzzyMatchConfig {
        private int maxContinuousMismatch = 8;
        private int maxTotalMismatch = 100;
        private boolean ignoreSymbols = true;
        private boolean caseSensitive = false;
        private int threadCount = Runtime.getRuntime().availableProcessors();
        public FuzzyMatchConfig() {}
        public FuzzyMatchConfig(int maxContinuousMismatch, int maxTotalMismatch, boolean ignoreSymbols, boolean caseSensitive, int threadCount) {
            this.maxContinuousMismatch = maxContinuousMismatch;
            this.maxTotalMismatch = maxTotalMismatch;
            this.ignoreSymbols = ignoreSymbols;
            this.caseSensitive = caseSensitive;
            this.threadCount = threadCount;
        }
        public int getMaxContinuousMismatch() { return maxContinuousMismatch; }
        public void setMaxContinuousMismatch(int v) { this.maxContinuousMismatch = v; }
        public int getMaxTotalMismatch() { return maxTotalMismatch; }
        public void setMaxTotalMismatch(int v) { this.maxTotalMismatch = v; }
        public boolean isIgnoreSymbols() { return ignoreSymbols; }
        public void setIgnoreSymbols(boolean v) { this.ignoreSymbols = v; }
        public boolean isCaseSensitive() { return caseSensitive; }
        public void setCaseSensitive(boolean v) { this.caseSensitive = v; }
        public int getThreadCount() { return threadCount; }
        public void setThreadCount(int v) { this.threadCount = v; }
    }

    public static MatchResult findBestMatch(String original, String target, FuzzyMatchConfig config) throws InterruptedException, ExecutionException {
        long start = System.currentTimeMillis();
        NormalizedText normOrig = normalize(original, config.isIgnoreSymbols(), config.isCaseSensitive());
        NormalizedText normTarget = normalize(target, config.isIgnoreSymbols(), config.isCaseSensitive());
        int winLen = normTarget.normalized.length();
        int totalLen = normOrig.normalized.length();
        int threadCount = config.getThreadCount();
        ExecutorService pool = Executors.newFixedThreadPool(threadCount);
        List<Future<MatchResult>> futures = new ArrayList<>();
        int step = Math.max(1, totalLen / threadCount);
        for (int t = 0; t < threadCount; t++) {
            int from = t * step;
            int to = (t == threadCount - 1) ? (totalLen - winLen + 1) : Math.min(totalLen - winLen + 1, (t + 1) * step);
            if (from >= to) continue;
            futures.add(pool.submit(() -> {
                MatchResult best = null;
                for (int i = from; i < to; i++) {
                    int totalMismatch = 0;
                    int maxContMismatch = 0;
                    int contMismatch = 0;
                    for (int j = 0; j < winLen; j++) {
                        if (normOrig.normalized.charAt(i + j) != normTarget.normalized.charAt(j)) {
                            contMismatch++;
                            totalMismatch++;
                            if (contMismatch > maxContMismatch) maxContMismatch = contMismatch;
                            if (contMismatch > config.getMaxContinuousMismatch()) break;
                        } else {
                            contMismatch = 0;
                        }
                        if (totalMismatch > config.getMaxTotalMismatch()) break;
                    }
                    if (maxContMismatch > config.getMaxContinuousMismatch() || totalMismatch > config.getMaxTotalMismatch()) continue;
                    if (best == null || totalMismatch < best.totalMismatch || (totalMismatch == best.totalMismatch && maxContMismatch < best.maxContinuousMismatch)) {
                        int startIdx = normOrig.origPos[i];
                        int endIdx = normOrig.origPos[i + winLen - 1];
                        // 优化两端内容对齐，确保不丢失模型片段前后内容
                        int origStart = startIdx, origEnd = endIdx;
                        // 统计模型片段前连续符号数量
                        int modelLeft = 0;
                        while (modelLeft < target.length() && SYMBOL_PATTERN.matcher(String.valueOf(target.charAt(modelLeft))).matches()) modelLeft++;
                        if (modelLeft > 0) {
                            int count = 0, pos = origStart - 1;
                            while (pos >= 0 && count < modelLeft && SYMBOL_PATTERN.matcher(String.valueOf(original.charAt(pos))).matches()) {
                                count++; pos--;
                            }
                            origStart -= count;
                        } else {
                            // 如果模型片段前没有符号，原文片段前也不能有符号
                            while (origStart > 0 && SYMBOL_PATTERN.matcher(String.valueOf(original.charAt(origStart - 1))).matches()) {
                                origStart--;
                            }
                        }
                        // 统计模型片段后连续符号数量
                        int modelRight = target.length() - 1;
                        while (modelRight >= 0 && SYMBOL_PATTERN.matcher(String.valueOf(target.charAt(modelRight))).matches()) modelRight--;
                        int modelRightCount = target.length() - 1 - modelRight;
                        if (modelRightCount > 0) {
                            int count = 0, pos = origEnd + 1;
                            while (pos < original.length() && count < modelRightCount && SYMBOL_PATTERN.matcher(String.valueOf(original.charAt(pos))).matches()) {
                                count++; pos++;
                            }
                            origEnd += count;
                        }
                        // 向后扩展，确保原文片段末尾能完整包含模型片段末尾所有内容
                        int modelTail = target.length() - 1;
                        int origTail = origEnd;
                        while (modelTail >= 0 && origTail < original.length()) {
                            // 跳过模型片段末尾的符号
                            if (SYMBOL_PATTERN.matcher(String.valueOf(target.charAt(modelTail))).matches()) {
                                modelTail--;
                                continue;
                            }
                            // 跳过原文片段末尾的符号
                            if (SYMBOL_PATTERN.matcher(String.valueOf(original.charAt(origTail))).matches()) {
                                origTail++;
                                continue;
                            }
                            // 如果内容不一致，停止扩展
                            if (Character.toLowerCase(target.charAt(modelTail)) != Character.toLowerCase(original.charAt(origTail))) {
                                break;
                            }
                            modelTail--;
                            origTail++;
                        }
                        // origTail指向最后一个需要包含的字符
                        if (origTail - 1 > origEnd) {
                            origEnd = origTail - 1;
                        }
                        // 向前扩展，确保原文片段前端能完整包含模型片段前端所有内容
                        int modelHead = 0;
                        int origHead = origStart;
                        while (modelHead < target.length() && origHead > 0) {
                            // 跳过模型片段前端的符号
                            if (SYMBOL_PATTERN.matcher(String.valueOf(target.charAt(modelHead))).matches()) {
                                modelHead++;
                                continue;
                            }
                            // 跳过原文片段前端的符号
                            if (SYMBOL_PATTERN.matcher(String.valueOf(original.charAt(origHead - 1))).matches()) {
                                origHead--;
                                continue;
                            }
                            // 如果内容不一致，停止扩展
                            if (Character.toLowerCase(target.charAt(modelHead)) != Character.toLowerCase(original.charAt(origHead - 1))) {
                                break;
                            }
                            modelHead++;
                            origHead--;
                        }
                        if (origHead < origStart) {
                            origStart = origHead;
                        }
                        String matched = original.substring(origStart, origEnd + 1);
                        best = new MatchResult(origStart, origEnd, matched, totalMismatch, maxContMismatch, 0);
                    }
                }
                return best;
            }));
        }
        pool.shutdown();
        MatchResult best = null;
        for (Future<MatchResult> f : futures) {
            MatchResult r = f.get();
            if (r == null) continue;
            if (best == null || r.totalMismatch < best.totalMismatch || (r.totalMismatch == best.totalMismatch && r.maxContinuousMismatch < best.maxContinuousMismatch)) {
                best = r;
            }
        }
        long cost = System.currentTimeMillis() - start;
        if (best != null) {
            best.costMillis = cost;
        }
        return best;
    }

    public static MatchResult findBestMatch(String original, String target) throws InterruptedException, ExecutionException {
        return findBestMatch(original, target, new FuzzyMatchConfig());
    }
} 