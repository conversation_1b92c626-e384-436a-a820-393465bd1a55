package com.jd.jdt.joycreator.ae.pojo.dto;

import lombok.Data;

/**
 * 集团人员信息响应结果
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-02-23
 */
@Data
public class HrUserBasicDTO {
    /**
     * 用户编号
     */
    private String userCode;
    /**
     * 用户ERP
     */
    private String userName;
    /**
     * 真实名称
     */
    private String realName;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 座机
     */
    private String telephone;
    /**
     * 性别
     */
    private String sex;
    /**
     * 生日
     */
    private String birthday;
    /**
     * 年龄
     */
    private int age;
    /**
     * 头像图片地址
     */
    private String headImg;
    /**
     * 组织机构编码
     */
    private String organizationCode;
    /**
     * 组织机构名称
     */
    private String organizationName;
    /**
     * 组织机构全名
     */
    private String organizationFullName;
    /**
     * 组织机构层级
     */
    private String organizationLevel;
    /**
     * 组织机构全路径
     */
    private String organizationFullPath;
    /**
     * 岗位编码
     */
    private String positionCode;
    /**
     * 岗位名称
     */
    private String positionName;
    /**
     * 职级编码
     */
    private String levelCode;
    /**
     * 职级名称
     */
    private String levelName;

    /**
     * 入职时间
     */
    private String entryDate;
    /**
     * 关键岗位，1：是，0：否，关键岗位一般指代机构负责人岗
     */
    private String keyPosition;
    /**
     * 岗位名称--新
     */
    private String positionShowName;
    private String userType;
}
