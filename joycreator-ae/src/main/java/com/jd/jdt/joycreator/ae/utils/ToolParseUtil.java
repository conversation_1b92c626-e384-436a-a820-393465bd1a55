package com.jd.jdt.joycreator.ae.utils;

import com.jd.jdt.joycreator.ae.enums.ToolLabelsEnum;
import com.jd.jdt.joycreator.ae.pojo.dto.ToolDtlDTO;
import com.jd.jsf.gd.util.JsonUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <p>
 * 工具解析
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
public class ToolParseUtil {


    public static void main(String[] args) {
        String str = "```mxl\n" +
                "        <doccontent_rewrite>\n" +
                "          <doccontent_rewrite_keyword>文档内容</doccontent_rewrite_keyword>\n" +
                "          <doccontent_rewrite_style>改写风格、方向、要求</doccontent_rewrite_style>\n" +
                "        </doccontent_rewrite>\n" +
                "        ";

        ToolDtlDTO parse = parse(str);
        System.err.println(JsonUtils.toJSONString(parse));
    }


    private static final Pattern TOOL_CODE_PATTERN = Pattern.compile("```xml(.*?)```", Pattern.DOTALL);

    public static ToolDtlDTO parse(String text) {
        if (text == null || text.trim().isEmpty()) {
            return null;
        }

        String contentToParse;

        Matcher markdownMatcher = TOOL_CODE_PATTERN.matcher(text);
        if (markdownMatcher.find()) {
            contentToParse = markdownMatcher.group(1).trim();
        } else {
            // Fallback: try to extract content that looks like XML from the string.
            int firstOpen = text.indexOf('<');
            int lastClose = text.lastIndexOf('>');
            if (firstOpen != -1 && lastClose > firstOpen) {
                contentToParse = text.substring(firstOpen, lastClose + 1).trim();
            } else {
                return null;
            }
        }

        if (contentToParse.isEmpty()) {
            return null;
        }

        try {
            return parseXmlToDto(contentToParse);
        } catch (Exception e) {
            e.printStackTrace(); // In a real application, use a logger
            return null;
        }
    }

    private static ToolDtlDTO parseXmlToDto(String xmlContent) throws Exception {
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        DocumentBuilder builder = factory.newDocumentBuilder();
        Document doc = builder.parse(new InputSource(new StringReader(xmlContent)));
        doc.getDocumentElement().normalize();

        Element rootElement = doc.getDocumentElement();

        ToolDtlDTO dto = new ToolDtlDTO();
        ToolLabelsEnum toolLabel = ToolLabelsEnum.getEnumByCode(rootElement.getNodeName());
        dto.setToolLabels(toolLabel);

        Map<String, Object> params = new HashMap<>();
        NodeList childNodes = rootElement.getChildNodes();
        for (int i = 0; i < childNodes.getLength(); i++) {
            Node node = childNodes.item(i);
            if (node.getNodeType() == Node.ELEMENT_NODE) {
                params.put(node.getNodeName(), getNodeValue(node));
            }
        }
        dto.setToolParams(params);

        return dto;
    }

    private static Object getNodeValue(Node node) {
        if (!node.hasChildNodes()) {
            return node.getTextContent();
        }

        NodeList children = node.getChildNodes();
        if (children.getLength() == 1 && children.item(0).getNodeType() == Node.TEXT_NODE) {
            return children.item(0).getTextContent().trim();
        }

        List<Object> childValues = new ArrayList<>();
        Map<String, Object> childMap = new HashMap<>();
        boolean hasElements = false;

        for (int i = 0; i < children.getLength(); i++) {
            Node child = children.item(i);
            if (child.getNodeType() == Node.ELEMENT_NODE) {
                hasElements = true;
                Object value = getNodeValue(child);
                if (childMap.containsKey(child.getNodeName())) {
                    Object existing = childMap.get(child.getNodeName());
                    if (existing instanceof List) {
                        ((List<Object>) existing).add(value);
                    } else {
                        List<Object> list = new ArrayList<>();
                        list.add(existing);
                        list.add(value);
                        childMap.put(child.getNodeName(), list);
                    }
                } else {
                    childMap.put(child.getNodeName(), value);
                }
            }
        }
        if (hasElements) {
            return childMap;
        }
        return node.getTextContent().trim();
    }
} 