package com.jd.jdt.joycreator.ae.cache;


import com.wangyin.r2m.client.jedis.Jedis;
import com.wangyin.r2m.client.jedis.JedisClusterConnectionHandler;
import com.wangyin.r2m.client.jedis.JedisPool;
import com.wangyin.r2m.client.jedis.ScanParams;
import com.wangyin.rediscluster.client.CacheClusterClient;
import com.wangyin.rediscluster.experimental.NodesHelper;
import com.wangyin.rediscluster.iterator.R2mNodeKeysIterator;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * *********************************<br>
 * <P>类名称：RedisHelper</P>
 * *********************************<br>
 * <P>类描述：Redis缓存帮助类</P>
 *
 * @version 1.0<br>
 */
@Log4j2
@Component
public class RedisHelper {
    /**
     * redis client
     */
    @Autowired
    private CacheClusterClient jimClient;

    private long expire_hour = 1;

    private static final String PREFIX = "joy_edit";

    public long getExpire_hour() {
        return expire_hour;
    }

    public void setExpire_hour(long expire_hour) {
        this.expire_hour = expire_hour;
    }

    /**
     * jimClient get 方法
     *
     * @return the jimClient
     */
    public CacheClusterClient getJimClient() {
        return jimClient;
    }

    /**
     * 设置缓存的过去时间
     */
    public Boolean expire(String key, int expire) {
        return jimClient.expire(key, expire) == 1 ? true : false;
    }

    /**
     * 设置缓存的过去时间
     */
    public Boolean expire(String key) {
        return jimClient.expire(key, (int) (expire_hour * 3600)) == 1 ? true : false;
    }

    /**
     * 设置缓存
     */
    public void setObject(String key, Object obj, boolean isExpire) {
        try{
            key = PREFIX + "_" + key;
            jimClient.setObject(key, obj);
            if (isExpire) {
                expire(key);
            }
        }catch (Exception e){
            log.error("setObject缓存基本的对象失败",e);
        }
    }

    public void setObject(String key, Object obj, boolean isExpire, int expire) {
        try{
            key = PREFIX + "_" + key;
            jimClient.setObject(key, obj);
            if (isExpire) {
                expire(key, expire);
            }
        }catch (Exception e){
            log.error("setObject缓存基本的对象失败",e);
        }
    }

    public Object getObject(String key) {
        try{
            key = PREFIX + "_" + key;
            return jimClient.getObject(key);
        }catch (Exception e){
            log.error("getObject查询缓存基本的对象失败",e);
        }
        return null;
    }

    /**
     * 缓存基本的对象，Integer、String、实体类等
     *
     * @param key   缓存的键值
     * @param value 缓存的值
     */
    public <T> void setCacheObject(final String key, final T value) {
        try{
            this.setObject(key, value, Boolean.FALSE);
        }catch (Exception e){
            log.error("setCacheObject缓存基本的对象失败",e);
        }
    }

    /**
     * 删除缓存
     *
     * @param key
     * @return
     */
    @SuppressWarnings("deprecation")
    public long deleteRedisByKey(String key) {
        key = PREFIX + "_" + key;
        long returnInt = 0;
        if (key.equals("")) {
            key = "*";
        }
        if (((String) key).indexOf("*") >= 0) {
            Map<String, Integer> res = new HashMap<String, Integer>();
            try {
                final String finalKey = key;
                res = new NodesHelper<Integer>(jimClient.getConnectionHandler()) {
                    @Override
                    public Map<String, JedisPool> setNodes(JedisClusterConnectionHandler connectionHandler) {
                        return connectionHandler.getMasterNodes();
                    }

                    @Override
                    public Integer handle(final Jedis jedis) {
                        int count = 0;
                        //这里使用R2mNodeKeysIterator, count 表示每次迭代最多查询个数, 和总数无关!
                        for (String key : new R2mNodeKeysIterator(jedis, new ScanParams().match(finalKey))) {
                            count += jimClient.del(key);
                        }
                        return count;
                    }

                    @Override
                    public void handleException(String Hnp, Exception e) {
                        log.error(Hnp + " got Exception : " + e.getMessage());
                    }
                }.run();
            } catch (Exception e) {
                e.printStackTrace();
            }

            for (Integer count : res.values()) {
                returnInt += count;
            }
        } else {
            returnInt = this.getJimClient().del(key);
        }
        return returnInt;
    }

    /**
     * 获取指定key是否存在
     *
     * @param key
     * @return
     */
    public Boolean exists(String key) {
        key = PREFIX + "_" + key;
        return jimClient.exists(key);
    }

    /**
     * @param key key
     * @return
     * @Description: 获取自增长值
     */
    public Long getIncr(String key) {
        key = PREFIX + "_" + key;
        Long incr = jimClient.incr(key);
        if (incr != null) {
            //如果值为1说明是第一次设置，那么设置key的存活时间为24小时
            if (incr == 1) {
                jimClient.expire(key, (24 * 60 * 60 + 1000));
            }
        }
        return incr;
    }



    /**
     * @param key key
     * @return
     * @Description: 获取自增长值
     */
    public Long getIncrNoExpire(String key) {
        key = PREFIX + "_" + key;
        Long incr = jimClient.incr(key);
        return incr;
    }

    /**
     * 设置缓存过期时间
     */
    public Boolean expireAdditPrefix(String key, int expire) {
        key = PREFIX + "_" + key;
        return jimClient.expire(key, expire) == 1 ? true : false;
    }

    /**
     * 添加指定key下的set元素数据
     *
     * @param key
     * @param members
     * @return
     */
    public Long sadd(String key, String... members) {
        key = PREFIX + "_" + key;
        return jimClient.sadd(key, members);
    }

    /**
     * 获取指定key下的set元素数量
     *
     * @param key
     * @return
     */
    public Long scard(String key) {
        key = PREFIX + "_" + key;
        return jimClient.scard(key);
    }

    /**
     * 随机获取指定key下的一个set元素并删除此元素
     *
     * @param key
     * @return
     */
    public String spop(String key) {
        key = PREFIX + "_" + key;
        return jimClient.spop(key);
    }

    /**
     * 获取指定key下的所有set元素
     *
     * @param key
     * @return
     */
    public Set<String> smembers(String key) {
        key = PREFIX + "_" + key;
        return jimClient.smembers(key);
    }

    /**
     * 删除指定key下的指定元素集
     *
     * @param key
     * @param members
     * @return
     */
    public Long srem(String key, String... members) {
        key = PREFIX + "_" + key;
        return jimClient.srem(key, members);
    }


    /**
     * 设置缓存 带过期时间
     */
    public long setnx(String key, String val, int expire) {
        key = PREFIX + "_" + key;
        Long setnx = jimClient.setnx(key, val);
        if (setnx == 1) {
            expire(key, expire);
        }
        return setnx;
    }

    /**
     * 查询key的到期时间
     */
    public long ttl(String key) {
        key = PREFIX + "_" + key;
        Long ttl = jimClient.ttl(key);
        return ttl;
    }
}
