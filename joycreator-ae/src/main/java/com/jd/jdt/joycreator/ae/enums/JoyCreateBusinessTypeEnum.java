package com.jd.jdt.joycreator.ae.enums;

/**
 * <p>
 * 业务类型前缀枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
public enum JoyCreateBusinessTypeEnum {

    LS("LS", "历史会话记录"),
    LSMX("LSMX", "历史会话记录明细");

    /**
     * code
     */
    private String value;
    /**
     * 描述
     */
    private String desc;

    JoyCreateBusinessTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static JoyCreateBusinessTypeEnum getEnum(String value) {
        for (JoyCreateBusinessTypeEnum a : JoyCreateBusinessTypeEnum.values()) {
            if (a.value.equals(value)) {
                return a;
            }
        }
        return null;
    }

    public String getDesc() {
        return desc;
    }

    public String getValue() {
        return value;
    }
}
