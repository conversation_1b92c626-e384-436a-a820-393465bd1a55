package com.jd.jdt.joycreator.ae.pojo.dto;

import lombok.Data;
import java.util.List;

/**
 * <p>
 * 模型网关（Meta非流式DTO）
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-08
 */
@Data
public class ChatCompletionResponseDTO {
    private String created;
    private UsageDTO usage;
    private String model;
    private String id;
    private List<ChoiceDTO> choices;
    private String system_fingerprint;
    private String object;

    @Data
    public static class UsageDTO {
        private Integer completion_tokens;
        private Integer prompt_tokens;
        private CompletionTokensDetailsDTO completion_tokens_details;
        private PromptTokensDetailsDTO prompt_tokens_details;
        private Integer total_tokens;
    }

    @Data
    public static class CompletionTokensDetailsDTO {
        private Integer accepted_prediction_tokens;
        private Integer audio_tokens;
        private Integer reasoning_tokens;
        private Integer rejected_prediction_tokens;
    }

    @Data
    public static class PromptTokensDetailsDTO {
        private Integer audio_tokens;
        private Integer cached_tokens;
    }

    @Data
    public static class ChoiceDTO {
        private String finish_reason;
        private Integer index;
        private MessageDTO message;
    }

    @Data
    public static class MessageDTO {
        private String role;
        private List<Object> annotations;
        private String content;
    }
}
