package com.jd.jdt.joycreator.ae.service;

import com.jd.jdt.joycreator.ae.entity.ChatHistoryDetailTemplate;
import com.jd.jdt.joycreator.ae.pojo.dto.ChatHistoryOperationExpandDTO;

import java.util.List;

/**
 * <p>
 * 聊天记录明细 服务
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-24
 */
public interface ChatHistoryDetailTemplateService {

    /**
     * 根据聊天记录明细ID查询模板列表
     *
     * @param chatHistoryDetailId 聊天记录明细ID
     * @return 模板列表
     */
    List<ChatHistoryDetailTemplate> listChatHistoryDetailTemplateByChatHistoryDetailId(Long chatHistoryDetailId);

    /**
     * ，选中模版
     *
     * @param chatHistoryOperationExpandDTO
     * @return
     */
    Boolean confirmTemplateLibrary(ChatHistoryOperationExpandDTO chatHistoryOperationExpandDTO);
}
