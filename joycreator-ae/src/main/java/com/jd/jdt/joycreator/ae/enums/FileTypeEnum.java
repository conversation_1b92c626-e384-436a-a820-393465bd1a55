package com.jd.jdt.joycreator.ae.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * <p>
 * 文件业务类型枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
@Getter
public enum FileTypeEnum {
    USER_CHAT("USER_CHAT", "用户会话"),
    SUGGEST_GOVERN("SUGGEST_GOVERN", "推荐管理"),
    TEMPLATE("TEMPLATE", "模版"),
    GENERAL("GENERAL", "通用类型"),
    ;

    /**
     * code
     */
    private String code;
    /**
     * 描述
     */
    private String desc;

    FileTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static FileTypeEnum getByCode(String code) {
        for (FileTypeEnum handlerEnum : FileTypeEnum.values()) {
            if (Objects.equals(handlerEnum.getCode(), code)) {
                return handlerEnum;
            }
        }
        return null;
    }
}
