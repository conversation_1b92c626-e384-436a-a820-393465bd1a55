package com.jd.jdt.joycreator.ae.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.ArrayList;
import java.util.List;

public class ContentAnchorAutoCompleteUtil {
    /**
     * 在原文中查找与anchorPattern最相似、分隔符和空格等最接近的片段，返回原文中完全一致的片段。
     * 分隔符内内容完全容错，只要前缀和后缀能对上就能补全；分隔符类型混乱时自动尝试多种分隔符组合。
     * @param original 原文全文
     * @param anchorPattern 需要查找的锚点模式（如"保险备注：【   】"或"合同签署后【   】个工作日内"）
     * @return 原文中最接近且分隔符、空格、标点等完全一致的片段，找不到返回null
     */
    public static String findBestAnchorContent(String original, String anchorPattern) {
        if (original == null || anchorPattern == null || anchorPattern.trim().isEmpty()) {
            return null;
        }
        // 1. 直接全等查找
        int idx = original.indexOf(anchorPattern);
        if (idx != -1) {
            return anchorPattern;
        }
        // 2. 分隔符容错查找
        // 支持的分隔符对
        char[] lefts = {'【', '[', '(', '{', '<', '“', '"', '‘', '\''};
        char[] rights = {'】', ']', ')', '}', '>', '”', '"', '’', '\''};
        List<int[]> sepPairs = new ArrayList<>();
        // 找到所有可能的分隔符对
        for (int i = 0; i < lefts.length; i++) {
            int l = anchorPattern.indexOf(lefts[i]);
            int r = anchorPattern.lastIndexOf(rights[i]);
            if (l >= 0 && r > l) {
                sepPairs.add(new int[]{l, r, i});
            }
        }
        // 如果没找到成对分隔符，尝试冒号、下划线等特殊符号
        if (sepPairs.isEmpty()) {
            int l = anchorPattern.indexOf(':');
            int r = anchorPattern.lastIndexOf(':');
            if (l >= 0 && r > l) sepPairs.add(new int[]{l, r, -1});
            l = anchorPattern.indexOf('：');
            r = anchorPattern.lastIndexOf('：');
            if (l >= 0 && r > l) sepPairs.add(new int[]{l, r, -2});
            l = anchorPattern.indexOf('_');
            r = anchorPattern.lastIndexOf('_');
            if (l >= 0 && r > l) sepPairs.add(new int[]{l, r, -3});
        }
        // 尝试所有分隔符对，优先返回最优匹配
        String bestMatch = null;
        int bestScore = -1;
        for (int[] pair : sepPairs) {
            int leftIdx = pair[0];
            int rightIdx = pair[1];
            String prefix = anchorPattern.substring(0, leftIdx + 1); // 包含左分隔符
            String suffix = anchorPattern.substring(rightIdx); // 包含右分隔符及后缀
            String regex = Pattern.quote(prefix) + ".*?" + Pattern.quote(suffix);
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(original);
            while (matcher.find()) {
                String candidate = matcher.group();
                int score = calcSimilarityScore(anchorPattern, candidate);
                if (score > bestScore) {
                    bestScore = score;
                    bestMatch = candidate;
                }
            }
        }
        // 3. 如果分隔符混乱或没找到，宽泛查找：只用前缀和后缀
        if (bestMatch == null) {
            // 尝试找第一个空格或分隔符作为前缀分界
            int leftIdx = -1, rightIdx = -1;
            for (int i = 0; i < anchorPattern.length(); i++) {
                if (!Character.isLetterOrDigit(anchorPattern.charAt(i))) {
                    leftIdx = i;
                    break;
                }
            }
            for (int i = anchorPattern.length() - 1; i >= 0; i--) {
                if (!Character.isLetterOrDigit(anchorPattern.charAt(i))) {
                    rightIdx = i;
                    break;
                }
            }
            if (leftIdx >= 0 && rightIdx > leftIdx) {
                String prefix = anchorPattern.substring(0, leftIdx + 1);
                String suffix = anchorPattern.substring(rightIdx);
                String regex = Pattern.quote(prefix) + ".*?" + Pattern.quote(suffix);
                Pattern pattern = Pattern.compile(regex);
                Matcher matcher = pattern.matcher(original);
                while (matcher.find()) {
                    String candidate = matcher.group();
                    int score = calcSimilarityScore(anchorPattern, candidate);
                    if (score > bestScore) {
                        bestScore = score;
                        bestMatch = candidate;
                    }
                }
            }
        }
        // 4. 还找不到，尝试只用前缀宽泛查找
        if (bestMatch == null) {
            String prefix = anchorPattern.length() > 6 ? anchorPattern.substring(0, 6) : anchorPattern;
            int idx2 = original.indexOf(prefix);
            if (idx2 != -1) {
                int end = Math.min(idx2 + anchorPattern.length() + 10, original.length());
                bestMatch = original.substring(idx2, end);
            }
        }
        return bestMatch;
    }

    // 计算分隔符、空格、数字、符号分布的相似度，越高越好
    private static int calcSimilarityScore(String pattern, String candidate) {
        int score = 0;
        int minLen = Math.min(pattern.length(), candidate.length());
        for (int i = 0; i < minLen; i++) {
            char pc = pattern.charAt(i);
            char cc = candidate.charAt(i);
            if (pc == cc) {
                score += 3;
            } else if (Character.isWhitespace(pc) && Character.isWhitespace(cc)) {
                score += 2;
            } else if (isSymbol(pc) && isSymbol(cc)) {
                score += 2;
            } else if (Character.isDigit(pc) && Character.isDigit(cc)) {
                score += 2;
            }
        }
        // 长度越接近分数越高
        score -= Math.abs(pattern.length() - candidate.length());
        return score;
    }

    private static boolean isSymbol(char c) {
        return !Character.isLetterOrDigit(c) && !Character.isWhitespace(c);
    }
} 