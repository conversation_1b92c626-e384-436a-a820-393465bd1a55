package com.jd.jdt.joycreator.ae.dao.plus.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jd.jdt.joycreator.ae.entity.ChatHistoryDetailExcerpt;
import com.jd.jdt.joycreator.ae.dao.mapper.ChatHistoryDetailExcerptMapper;
import com.jd.jdt.joycreator.ae.dao.plus.IChatHistoryDetailExcerptMapper;
import com.jd.jdt.joycreator.ae.dao.plus.IBaseMapperImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 聊天记录明细引用 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Service
public class IChatHistoryDetailExcerptMapperImpl extends IBaseMapperImpl<ChatHistoryDetailExcerptMapper, ChatHistoryDetailExcerpt> implements IChatHistoryDetailExcerptMapper {

    @Override
    public List<ChatHistoryDetailExcerpt> listByChatHistoryDetailId(Long chatHistoryDetailId) {
        LambdaQueryWrapper<ChatHistoryDetailExcerpt> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChatHistoryDetailExcerpt::getChatHistoryDetailId, chatHistoryDetailId);
        return list(queryWrapper);
    }
}
