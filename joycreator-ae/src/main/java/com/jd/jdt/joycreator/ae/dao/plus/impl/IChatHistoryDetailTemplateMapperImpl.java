package com.jd.jdt.joycreator.ae.dao.plus.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.jd.jdt.joycreator.ae.dao.plus.IChatHistoryDetailMapper;
import com.jd.jdt.joycreator.ae.dao.plus.ITemplateLibraryMapper;
import com.jd.jdt.joycreator.ae.entity.ChatHistoryDetail;
import com.jd.jdt.joycreator.ae.entity.ChatHistoryDetailTemplate;
import com.jd.jdt.joycreator.ae.dao.mapper.ChatHistoryDetailTemplateMapper;
import com.jd.jdt.joycreator.ae.dao.plus.IChatHistoryDetailTemplateMapper;
import com.jd.jdt.joycreator.ae.dao.plus.IBaseMapperImpl;
import com.jd.jdt.joycreator.ae.entity.TemplateLibrary;
import com.jd.jdt.joycreator.ae.enums.SelectedEnum;
import com.jd.jdt.joycreator.ae.pojo.vo.ChatHistoryDetailTemplateVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 聊天记录明细模版 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-23
 */
@Service
public class IChatHistoryDetailTemplateMapperImpl extends IBaseMapperImpl<ChatHistoryDetailTemplateMapper, ChatHistoryDetailTemplate> implements IChatHistoryDetailTemplateMapper {

    @Autowired
    private IChatHistoryDetailMapper iChatHistoryDetailMapper;
    @Autowired
    private ITemplateLibraryMapper iTemplateLibraryMapper;

    @Override
    public List<ChatHistoryDetailTemplateVO> listChatHistoryDetailTemplateByChatHistoryId(Long id) {
        List<ChatHistoryDetail> chatHistoryDetailList = iChatHistoryDetailMapper.listChatHistoryDetailByChatHistoryId(id);
        if (CollectionUtils.isEmpty(chatHistoryDetailList)) {
            return null;
        }
        List<Long> chatHistoryDetailIdList = chatHistoryDetailList.stream().map(ChatHistoryDetail::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(chatHistoryDetailIdList)) {
            return null;
        }
        LambdaQueryWrapper<ChatHistoryDetailTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ChatHistoryDetailTemplate::getChatHistoryDetailId, chatHistoryDetailIdList);
        queryWrapper.groupBy(ChatHistoryDetailTemplate::getId);
        List<ChatHistoryDetailTemplate> chatHistoryDetailTemplateList = list(queryWrapper);
        if (CollectionUtils.isEmpty(chatHistoryDetailTemplateList)) {
            return null;
        }

        List<ChatHistoryDetailTemplateVO> chatHistoryDetailTemplateVOList = chatHistoryDetailTemplateList.stream().map(c -> {
            ChatHistoryDetailTemplateVO chatHistoryDetailTemplateVO = new ChatHistoryDetailTemplateVO();
            BeanUtils.copyProperties(c, chatHistoryDetailTemplateVO);
            TemplateLibrary templateLibrary = iTemplateLibraryMapper.getById(c.getTemplateId());
            if (Objects.nonNull(templateLibrary)) {
                chatHistoryDetailTemplateVO.setTemplateName(templateLibrary.getName());
                chatHistoryDetailTemplateVO.setTemplateId(templateLibrary.getId().toString());
            }
            return chatHistoryDetailTemplateVO;
        }).collect(Collectors.toList());
        return chatHistoryDetailTemplateVOList;
    }

    @Override
    public Boolean delByChatHistoryId(Long chatHistoryId) {
        List<ChatHistoryDetail> chatHistoryDetailList = iChatHistoryDetailMapper.listChatHistoryDetailByChatHistoryId(chatHistoryId);
        if (CollectionUtils.isEmpty(chatHistoryDetailList)) {
            return null;
        }
        List<Long> chatHistoryDetailIdList = chatHistoryDetailList.stream().map(ChatHistoryDetail::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(chatHistoryDetailIdList)) {
            return null;
        }
        LambdaQueryWrapper<ChatHistoryDetailTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ChatHistoryDetailTemplate::getChatHistoryDetailId, chatHistoryDetailIdList);
        return remove(queryWrapper);
    }

    @Override
    public Boolean confirmTemplateLibraryByTemplateId(String businessNo, String templateId) {
        ChatHistoryDetail historyDetail = iChatHistoryDetailMapper.getChatHistoryDetailByBusinessNo(businessNo);
        if (Objects.isNull(historyDetail)) {
            return Boolean.FALSE;
        }
        LambdaUpdateWrapper<ChatHistoryDetailTemplate> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .eq(ChatHistoryDetailTemplate::getChatHistoryDetailId, historyDetail.getId())
                .eq(ChatHistoryDetailTemplate::getTemplateId, templateId)
                .set(ChatHistoryDetailTemplate::getSelected, SelectedEnum.Y);
        return update(updateWrapper);
    }
}
