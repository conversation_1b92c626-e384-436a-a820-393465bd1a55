package com.jd.jdt.joycreator.ae.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.jd.jdt.joycreator.ae.pojo.dto.EmbeddingResponseDTO;
import com.jd.jdt.joycreator.ae.rpc.feign.LLMGatewayService;
import com.jd.jsf.gd.util.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class TextReplaceUtil {

    @Autowired
    private LLMGatewayService llmGatewayService;

    private static final String EMBEDDING_API = "http://localhost:5000/embedding";
    private static final ObjectMapper mapper = new ObjectMapper();

    /**
     * 替换原文中与目标段落最相似的片段为新段落。
     *
     * @param originalText         原文字符串
     * @param targetParagraph      原文待修改的段落（可跨多个段落）
     * @param replacementParagraph 替换后的新段落
     * @return 替换后的全文字符串
     */
    public String replaceMostSimilarParagraph(String originalText, String targetParagraph, String replacementParagraph) throws Exception {
        List<String> paragraphs = Arrays.stream(originalText.split("\n\n"))
                .map(String::trim)
                .collect(Collectors.toList());

        float[] targetEmbedding = getEmbedding(targetParagraph);

        int bestStart = -1, bestEnd = -1;
        double maxSimilarity = -1;

        // 滑动窗口匹配
        for (int windowSize = 1; windowSize <= paragraphs.size(); windowSize++) {
            for (int start = 0; start <= paragraphs.size() - windowSize; start++) {
                int end = start + windowSize;
                String combinedText = String.join("\n\n", paragraphs.subList(start, end));
                float[] combinedEmbedding = getEmbedding(combinedText);
                double sim = cosineSimilarity(targetEmbedding, combinedEmbedding);
                if (sim > maxSimilarity) {
                    maxSimilarity = sim;
                    bestStart = start;
                    bestEnd = end;
                }
            }
        }

        if (bestStart >= 0 && bestEnd > bestStart) {
            List<String> replacedParagraphs = new ArrayList<>();
            replacedParagraphs.addAll(paragraphs.subList(0, bestStart));
            replacedParagraphs.add(replacementParagraph);
            replacedParagraphs.addAll(paragraphs.subList(bestEnd, paragraphs.size()));
            return String.join("\n\n", replacedParagraphs);
        } else {
            throw new RuntimeException("未找到合适的段落组合进行替换。");
        }
    }

    // 获取单个文本的Embedding
    private float[] getEmbedding(String sentence) throws Exception {
        Map<String, Object> params = Maps.newHashMap();
//        params.put("model", "Chatrhino-81B-Pro");
        params.put("model", "text-embedding-ada-002-2");
        params.put("erp", "zhaohan25");
        params.put("input", sentence);
        String requestId = UUID.randomUUID().toString();
        EmbeddingResponseDTO embeddingResponseDTO = llmGatewayService.embeddings("aa051d32-db0b-4294-98a0-b02040240d48", requestId, params);
        List<Double> doubleList = embeddingResponseDTO.getData().get(0).getEmbedding();
        float[] floatArray = new float[doubleList.size()];
        for (int i = 0; i < doubleList.size(); i++) {
            floatArray[i] = doubleList.get(i).floatValue();
        }
        return floatArray;

        /*try (CloseableHttpClient client = HttpClients.createDefault()) {
            HttpPost post = new HttpPost(EMBEDDING_API);
            Map<String, Object> payload = new HashMap<>();
            payload.put("sentences", Collections.singletonList(sentence));
            StringEntity entity = new StringEntity(mapper.writeValueAsString(payload), StandardCharsets.UTF_8);
            entity.setContentType("application/json");
            post.setEntity(entity);

            String response = client.execute(post, httpResponse ->
                    new String(httpResponse.getEntity().getContent().readAllBytes(), StandardCharsets.UTF_8));

            JsonNode jsonNode = mapper.readTree(response).get("embeddings").get(0);
            float[] embedding = new float[jsonNode.size()];
            for (int i = 0; i < jsonNode.size(); i++) {
                embedding[i] = jsonNode.get(i).floatValue();
            }
            return embedding;
        }*/
    }

    // 余弦相似度计算
    private static double cosineSimilarity(float[] a, float[] b) {
        double dot = 0, normA = 0, normB = 0;
        for (int i = 0; i < a.length; i++) {
            dot += a[i] * b[i];
            normA += a[i] * a[i];
            normB += b[i] * b[i];
        }
        return dot / (Math.sqrt(normA) * Math.sqrt(normB));
    }
}
