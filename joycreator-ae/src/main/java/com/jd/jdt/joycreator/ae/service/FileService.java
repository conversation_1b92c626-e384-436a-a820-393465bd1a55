package com.jd.jdt.joycreator.ae.service;

import com.amazonaws.services.s3.model.ObjectMetadata;
import com.jd.jdt.joycreator.ae.pojo.dto.UploadFileDTO;

import java.io.InputStream;

/**
 * <p>
 * 附件相关接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
public interface FileService {

    /**
     * 上传
     *
     * @param fileName
     * @param inputStream
     * @param contentType
     * @return
     */
    UploadFileDTO uploadFile(String fileName, InputStream inputStream, String contentType);

    /**
     * 将文件上传到存储服务
     *
     * @param fileName       上传文件的名称，包括路径信息
     * @param inputStream    文件内容的输入流
     * @param objectMetadata 文件的元数据信息，如大小、类型等
     */
    void upload(String fileName, InputStream inputStream, ObjectMetadata objectMetadata);

    /**
     * 根据文件名下载文件
     *
     * @param fileName 需要下载的文件名
     * @return 返回一个InputStream，通过它可以读取下载的文件内容
     */
    InputStream downLoad(String fileName);
}
