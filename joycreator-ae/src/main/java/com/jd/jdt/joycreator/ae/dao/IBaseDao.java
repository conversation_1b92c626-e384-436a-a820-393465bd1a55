package com.jd.jdt.joycreator.ae.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jd.jdt.joycreator.ae.entity.BaseEntity;

import java.util.Collection;

public interface IBaseDao<T> extends IService<T> {
    /**
     * 插入新数据
     *
     * @param entity
     * @return
     */
    boolean insert(T entity);

    /**
     * 插入新数据
     *
     * @param entity
     * @return
     */
    @Override
    boolean save(T entity);

    @Override
    boolean saveBatch(Collection<T> entityList);

    boolean saveBatch1(Collection<T> entityList);

    @Override
    boolean updateById(T entity);


    boolean saveOrUpdate(T entity);

    boolean saveBatchTime(Collection<T> entityList);

     <T extends BaseEntity> String id(T t);
}
