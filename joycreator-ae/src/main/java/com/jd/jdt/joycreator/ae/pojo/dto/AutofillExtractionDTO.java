package com.jd.jdt.joycreator.ae.pojo.dto;

import com.jd.jdt.joycreator.ae.enums.ChatRoleEnum;
import lombok.Data;

/**
 * <p>
 * AutofillExtractionVO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Data
public class AutofillExtractionDTO {

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 重写类型
     */
    private ChatRoleEnum rewriteType;

    /**
     * 改写/替换关键词
     */
    private String keyword;

    /**
     * 是否最后一个
     */
    private boolean last;

}
