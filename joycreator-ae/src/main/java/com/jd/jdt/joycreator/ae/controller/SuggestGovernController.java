package com.jd.jdt.joycreator.ae.controller;

import com.jd.jdt.app4s.component.common.api.entity.ResponseResult;
import com.jd.jdt.joycreator.ae.pojo.vo.SuggestGovernVO;
import com.jd.jdt.joycreator.ae.service.SuggestGovernServe;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 推荐配置 服务接口
 * </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @since 2025-06-19
 */
@Log4j2
@RestController
@RequestMapping("/api/v1/suggest-govern")
public class SuggestGovernController {

    @Autowired
    private SuggestGovernServe suggestGovernServe;


    /**
     * 推荐管理列表
     *
     * @return
     */
    @GetMapping("/list")
    public ResponseResult<List<SuggestGovernVO>> listSuggestGovern() {
        return ResponseResult.success(suggestGovernServe.listSuggestGovern());
    }

}
