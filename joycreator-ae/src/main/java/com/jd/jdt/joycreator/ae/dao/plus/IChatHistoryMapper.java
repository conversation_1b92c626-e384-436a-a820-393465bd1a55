package com.jd.jdt.joycreator.ae.dao.plus;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.jdt.joycreator.ae.entity.ChatHistory;
import com.jd.jdt.joycreator.ae.dao.IBaseDao;
import com.jd.jdt.joycreator.ae.pojo.dto.CollectionDTO;
import com.jd.jdt.joycreator.ae.pojo.vo.ChatHistoryListVO;

import java.util.List;

/**
 * <p>
 * 聊天记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
public interface IChatHistoryMapper extends IBaseDao<ChatHistory> {

    List<ChatHistory> listChatHistoryDetailByErp(String erp);

    ChatHistory chatHistoryBySessionId(String sessionId);

    Page<ChatHistoryListVO> searchChatHistoryListVO(Page page, QueryWrapper queryWrapper);

    Boolean updateCollection(CollectionDTO collectionDTO);

    Boolean delBySessionId(String sessionId);

    void create(String sessionId);

    List<ChatHistory> chatHistoryByIds(List<Long> ids);
}
