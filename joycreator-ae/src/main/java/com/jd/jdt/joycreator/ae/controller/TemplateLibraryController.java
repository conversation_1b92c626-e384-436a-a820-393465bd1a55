package com.jd.jdt.joycreator.ae.controller;

import com.jd.jdt.app4s.component.common.api.entity.ResponseResult;
import com.jd.jdt.joycreator.ae.entity.TemplateLibrary;
import com.jd.jdt.joycreator.ae.enums.OfficeConvertEnum;
import com.jd.jdt.joycreator.ae.pojo.dto.AutofillExtractionDTO;
import com.jd.jdt.joycreator.ae.pojo.dto.DocumentAnalysisDTO;
import com.jd.jdt.joycreator.ae.pojo.vo.AutofillExtractionVO;
import com.jd.jdt.joycreator.ae.pojo.vo.TemplateLibraryDtlVO;
import com.jd.jdt.joycreator.ae.service.TemplateLibraryService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * <p>
 * 模版库接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Log4j2
@RestController
@RequestMapping("/api/v1/template-library")
public class TemplateLibraryController {

    @Autowired
    private TemplateLibraryService templateLibraryService;

    @GetMapping("{id}")
    public ResponseResult<TemplateLibrary> getById(@PathVariable("id") Long id) {
        return ResponseResult.success(templateLibraryService.getById(id));
    }

    /**
     * 模版上传
     *
     * @param file          文件
     * @param officeConvert 文件枚举 写DOCX_TO_HTML
     * @param tag           模板标记
     * @param description   模板描述
     * @return
     * @throws Exception
     */
    @PostMapping("/upload")
    public ResponseResult<Long> upload(@RequestParam("file") MultipartFile file,
                                       @RequestParam("officeConvert") OfficeConvertEnum officeConvert,
                                       @RequestParam("tag") String tag,
                                       @RequestParam("description") String description) throws Exception {
        return ResponseResult.success(templateLibraryService.uploadTemplateLibrary(file, officeConvert, tag, description));
    }


    /**
     * 模版上传 word文件
     *
     * @param file
     * @param tag
     * @param description
     * @return
     * @throws Exception
     */
    @PostMapping("/upload2")
    public ResponseResult<Long> update2(@RequestParam("file") MultipartFile file,
                                        @RequestParam("tag") String tag,
                                        @RequestParam("description") String description) throws Exception {
        return ResponseResult.success(templateLibraryService.updateTemplateLibrary(file, tag, description));
    }


    /**
     * 文档分析
     *
     * @param documentAnalysisDTO
     * @return
     */
    @PostMapping(value = "/document-analysis")
    public ResponseResult<TemplateLibraryDtlVO> documentAnalysis(@RequestBody DocumentAnalysisDTO documentAnalysisDTO) {
        return ResponseResult.success(templateLibraryService.documentAnalysis(documentAnalysisDTO));
    }

}
