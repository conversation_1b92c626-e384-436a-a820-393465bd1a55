package com.jd.jdt.joycreator.ae;

import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
///////
/**
 * <p>
 * 命令执行程序
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Log4j2
@Component
public class CommandExecutor {

    // 默认超时时间（秒）
    private static final int DEFAULT_TIMEOUT = 60;

    /*public static void main(String[] args) {
        // 示例用法
        try {
            pandoc 文件转换示例
            CommandResult commandResult = executeCommand(Lists.newArrayList("pandoc /Users/<USER>/Documents/销售合同md模板.md -o 测试112233.docx"), true);
            System.err.println(JsonUtils.toJSONString(commandResult));

            // Windows示例：查看目录
            CommandResult result = executeCommand(Lists.newArrayList("dir"), true);
            printResult("Windows Directory Listing", result);

            // Mac/Linux示例：列出文件
            CommandResult result2 = executeCommand(Lists.newArrayList("ls", "-l"), true);
            printResult("Linux/Mac File List", result2);

            // 带超时的命令
            CommandResult timeoutResult = executeCommand(Lists.newArrayList("ping", "localhost"), true, 5);
            printResult("Timeout Test", timeoutResult);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }*/

    /**
     * 执行命令（自动检测平台）
     */
    public CommandResult executeCommand(List<String> command, boolean redirectErrorStream)
            throws IOException, InterruptedException {
        return executeCommand(command, redirectErrorStream, DEFAULT_TIMEOUT);
    }

    /**
     * 带超时控制的命令执行
     */
    public CommandResult executeCommand(List<String> command, boolean redirectErrorStream, int timeoutSeconds)
            throws IOException, InterruptedException {
        ProcessBuilder processBuilder = new ProcessBuilder();
        processBuilder.command(buildPlatformCommand(command));
        processBuilder.redirectErrorStream(redirectErrorStream);

        Process process = processBuilder.start();
        ExecutorService executor = Executors.newFixedThreadPool(2);

        try {
            // 启动异步读取任务
            Future<String> outputFuture = readStream(process.getInputStream(), executor);
            Future<String> errorFuture = redirectErrorStream ? null : readStream(process.getErrorStream(), executor);

            // 等待进程终止
            boolean finished = process.waitFor(timeoutSeconds, TimeUnit.SECONDS);
            if (!finished) {
                process.destroyForcibly();
                throw new TimeoutException("Command timed out after " + timeoutSeconds + " seconds");
            }

            // 获取结果
            return new CommandResult(
                    process.exitValue(),
                    outputFuture.get(),
                    redirectErrorStream ? "" : errorFuture.get()
            );
        } catch (ExecutionException | TimeoutException e) {
            throw new IOException("Command execution failed", e);
        } finally {
            executor.shutdownNow();
            closeProcess(process);
        }
    }

    /**
     * 构建平台相关命令
     */
    private static List<String> buildPlatformCommand(List<String> command) {
        String os = System.getProperty("os.name").toLowerCase();
        List<String> fullCommand = new ArrayList<>();

        if (os.contains("win")) {
            fullCommand.add("cmd.exe");
            fullCommand.add("/c");
        } else {
            fullCommand.add("/bin/sh");
            fullCommand.add("-c");
        }

        // 将命令参数合并为单个字符串（兼容shell解析）
        fullCommand.add(String.join(" ", command));
        return fullCommand;
    }

    /**
     * 异步读取流内容
     */
    private static Future<String> readStream(InputStream inputStream, ExecutorService executor) {
        return executor.submit(() -> {
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
                StringBuilder output = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append(System.lineSeparator());
                }
                return output.toString().trim();
            }
        });
    }

    /**
     * 安全关闭进程
     */
    private static void closeProcess(Process process) {
        try {
            process.getInputStream().close();
            process.getErrorStream().close();
            process.getOutputStream().close();
        } catch (IOException e) {
            // Ignore close errors
        }
    }

    /**
     * 命令执行结果封装类
     */
    class CommandResult {
        final int exitCode;
        final String output;
        final String error;

        CommandResult(int exitCode, String output, String error) {
            this.exitCode = exitCode;
            this.output = output;
            this.error = error;
        }
    }
}
