package com.jd.jdt.joycreator.ae.dao;

import com.jd.jdt.joycreator.ae.Application;
import com.jd.jdt.joycreator.ae.dao.plus.ITemplateLibraryMapper;
import com.jd.jdt.joycreator.ae.entity.TemplateLibrary;
import lombok.extern.log4j.Log4j2;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@Log4j2
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class ITemplateLibraryMapperTest {

    @Autowired
    private ITemplateLibraryMapper iTemplateLibraryMapper;


    @Test
    public void testInsert() {
        TemplateLibrary template = new TemplateLibrary();
        template.setName("销售合同测试1模版");
        template.setTag("销售合同");
        template.setContent("以下是一份卖方视角的销售合同模板（含格式示例），由于篇幅限制，此处提供框架结构和关键条款示例。实际使用时可通过扩展条款细节、增加附件内容达到5000字要求：\\n\\n---\\n\\n**销售合同**\\n合同编号：_____________\\n签订地点：_____________\\n签订日期：_____________\\n\\n---\\n\\n**甲方（卖方）：**\\n名称：_________________________\\n地址：_________________________\\n法定代表人：___________________\\n联系方式：_____________________\\n\\n**乙方（买方）：**\\n名称：_________________________\\n地址：_________________________\\n法定代表人：___________________\\n联系方式：_____________________\\n\\n---\\n\\n### 第一条 合同标的\\n1.1 卖方同意出售，买方同意购买以下货物：\\n| 序号 | 产品名称   | 规格型号    | 单位 | 数量 | 单价（元） | 总价（元） |\\n|------|------------|-------------|------|------|------------|------------|\\n| 1    | ________   | ____________ | ____ | ____ | __________ | __________ |\\n| 2    | ________   | ____________ | ____ | ____ | __________ | __________ |\\n\\n（注：可通过扩展表格行数增加篇幅）\\n\\n---\\n\\n### 第二条 价格与支付方式\\n2.1 合同总金额：人民币_________元（大写：_________________________）。\\n2.2 付款方式：\\n□ 预付款：合同签订后___日内支付总价款的___%\\n□ 进度款：货物交付后___日内支付总价款的___%\\n□ 质保金：验收合格后___日内支付剩余___%\\n\\n---\\n\\n### 第三条 交货与验收\\n3.1 交货时间：_______年___月___日前\\n3.2 交货地点：_________________________\\n3.3 运输方式：_________________________（运费由___方承担）\\n3.4 验收标准：\\n（以下条款需详细展开，可增加技术参数表格）\\n- 外观质量：无破损、无污染\\n- 性能指标：符合国家________标准（GB/T______）\\n- 文件要求：提供产品合格证、检测报告\\n\\n---\\n\\n### 第四条 质量保证\\n4.1 质保期：自验收合格之日起___个月\\n4.2 卖方责任范围：\\n□ 免费更换瑕疵部件\\n□ 承担维修人工费用\\n□ 不承担间接损失（如停产损失）\\n\\n---\\n\\n### 第五条 违约责任\\n5.1 买方逾期付款：每日按未付金额___%支付违约金\\n5.2 卖方逾期交货：每日按合同总额___%支付违约金\\n5.3 单方解除合同：违约方需赔偿守约方直接损失的___%\\n\\n---\\n\\n### 第六条 不可抗力\\n6.1 定义：战争、地震、政府行为等无法预见的情形\\n6.2 受影响方需在___日内书面通知对方\\n6.3 处理方式：协商延期履行或部分解除合同\\n\\n---\\n\\n### 第七条 争议解决\\n7.1 协商不成时，提交_________仲裁委员会仲裁\\n7.2 或：向卖方所在地人民法院提起诉讼\\n\\n---\\n\\n### 第八条 其他条款\\n8.1 知识产权：产品商标、专利归属卖方所有\\n8.2 保密义务：双方不得泄露商业机密（可附保密协议附件）\\n8.3 合同生效：双方签字盖章后生效，一式___份\\n\\n---\\n\\n**附件清单**\\n1. 《技术规格要求》\\n2. 《验收标准细则》\\n3. 《售后服务承诺书》\\n\\n---\\n\\n**甲方（盖章）：**　　　　　　　**乙方（盖章）：**\\n法定代表人（签字）：_________　　法定代表人（签字）：_________\\n日期：_______年___月___日　　　日期：_______年___月___日\\n\\n---\\n\\n### 扩展内容建议（用于增加篇幅）:\\n1. **技术附件**：用表格列明各项技术参数（可占2-3页）\\n2. **售后服务细则**：详细描述响应时间、服务流程\\n3. **法律声明**：增加数据安全、反商业贿赂等条款\\n4. **履约担保**：增加银行保函、保证金等要求\\n5. **风险转移条款**：明确货物损毁灭失的责任划分\\n\\n*注：实际使用时需根据《中华人民共和国民法典》及相关司法解释调整条款，建议委托专业律师审核。*");
        template.setDescription("销售合同类模版");
        
        boolean result = iTemplateLibraryMapper.save(template);
        Assert.assertTrue(result);
        Assert.assertNotNull(template.getId());
        log.info("插入成功，ID: {}", template.getId());
    }

    @Test
    public void testSelect() {
        // 先插入一条测试数据
        TemplateLibrary template = new TemplateLibrary();
        template.setTag("查询测试标签");
        template.setContent("查询测试内容");
        template.setDescription("查询测试描述");
        iTemplateLibraryMapper.save(template);

        // 测试查询
        TemplateLibrary found = iTemplateLibraryMapper.getById(template.getId());
        Assert.assertNotNull(found);
        Assert.assertEquals(template.getTag(), found.getTag());
        Assert.assertEquals(template.getContent(), found.getContent());
        Assert.assertEquals(template.getDescription(), found.getDescription());
        log.info("查询成功: {}", found);
    }

    @Test
    public void testUpdate() {
        // 先插入一条测试数据
        TemplateLibrary template = new TemplateLibrary();
        template.setTag("更新前标签");
        template.setContent("更新前内容");
        template.setDescription("更新前描述");
        iTemplateLibraryMapper.save(template);

        // 更新数据
        template.setTag("更新后标签");
        template.setContent("更新后内容");
        template.setDescription("更新后描述");
        boolean result = iTemplateLibraryMapper.updateById(template);
        
        Assert.assertTrue(result);
        
        // 验证更新结果
        TemplateLibrary updated = iTemplateLibraryMapper.getById(template.getId());
        Assert.assertEquals("更新后标签", updated.getTag());
        Assert.assertEquals("更新后内容", updated.getContent());
        Assert.assertEquals("更新后描述", updated.getDescription());
        log.info("更新成功: {}", updated);
    }

    @Test
    public void testDelete() {
        // 先插入一条测试数据
        TemplateLibrary template = new TemplateLibrary();
        template.setTag("待删除标签");
        template.setContent("待删除内容");
        template.setDescription("待删除描述");
        iTemplateLibraryMapper.save(template);

        // 删除数据
        boolean result = iTemplateLibraryMapper.removeById(template.getId());
        Assert.assertTrue(result);

        // 验证删除结果
        TemplateLibrary deleted = iTemplateLibraryMapper.getById(template.getId());
        Assert.assertNull(deleted);
        log.info("删除成功");
    }
}
