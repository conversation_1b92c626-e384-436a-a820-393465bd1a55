package com.jd.jdt.joycreator.ae.util;

import com.aspose.words.Document;
import com.aspose.words.SaveFormat;

public class AsposeHtmlApplication {
    public static void main(String[] args) {
        try {
            // 指定Word文档路径
            String wordPath = "joycreator-ae/src/main/resources/scripts/【样例】技术服务协议模板（jd为销售方通用版）.docx";
            // 指定输出HTML路径
            String htmlPath = "joycreator-ae/src/main/resources/scripts/output.html";

            // 加载Word文档
            Document doc = new Document(wordPath);

            // 保存为HTML文件
            doc.save(htmlPath, SaveFormat.HTML);

            System.out.println("HTML文档已成功创建: " + htmlPath);
        } catch (Exception e) {
            System.err.println("转换过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
