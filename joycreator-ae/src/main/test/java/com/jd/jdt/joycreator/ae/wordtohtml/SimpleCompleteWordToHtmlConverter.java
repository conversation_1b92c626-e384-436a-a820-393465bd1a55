package com.jd.jdt.joycreator.ae.wordtohtml;

import org.apache.poi.xwpf.usermodel.*;
import java.io.*;
import java.util.*;

/**
 * 简单完整的Word转HTML转换器
 * 保持原Word文档的基本格式，生成单一HTML文件，无外部依赖，确保无乱码
 */
public class SimpleCompleteWordToHtmlConverter {
    
    private StringBuilder htmlContent;
    private int tableCounter = 0;
    private int paragraphCounter = 0;
    
    public SimpleCompleteWordToHtmlConverter() {
        this.htmlContent = new StringBuilder();
    }
    
    /**
     * 转换Word文档为HTML
     */
    public void convertWordToHtml(String inputPath, String outputPath) throws IOException {
        try (FileInputStream fis = new FileInputStream(inputPath);
             XWPFDocument document = new XWPFDocument(fis)) {
            
            System.out.println("开始转换: " + inputPath);
            
            // 开始HTML文档
            startHtmlDocument();
            
            // 处理文档内容
            processDocument(document);
            
            // 结束HTML文档
            endHtmlDocument();
            
            // 写入文件
            writeToFile(outputPath);
            
            System.out.println("转换完成: " + outputPath);
        } catch (Exception e) {
            System.err.println("转换失败: " + e.getMessage());
            e.printStackTrace();
            throw new IOException("转换失败", e);
        }
    }
    
    /**
     * 开始HTML文档
     */
    private void startHtmlDocument() {
        htmlContent.append("<!DOCTYPE html>\n");
        htmlContent.append("<html lang=\"zh-CN\">\n");
        htmlContent.append("<head>\n");
        htmlContent.append("<meta charset=\"UTF-8\">\n");
        htmlContent.append("<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
        htmlContent.append("<title>Word转HTML文档</title>\n");
        htmlContent.append("<style>\n");
        
        // CSS样式
        htmlContent.append("body {\n");
        htmlContent.append("    font-family: 'Microsoft YaHei', '微软雅黑', 'SimSun', '宋体', Arial, sans-serif;\n");
        htmlContent.append("    line-height: 1.5;\n");
        htmlContent.append("    margin: 40px;\n");
        htmlContent.append("    background-color: #ffffff;\n");
        htmlContent.append("    color: #000000;\n");
        htmlContent.append("    font-size: 12pt;\n");
        htmlContent.append("}\n");
        
        htmlContent.append("p {\n");
        htmlContent.append("    margin: 0 0 6pt 0;\n");
        htmlContent.append("    padding: 0;\n");
        htmlContent.append("}\n");
        
        htmlContent.append("table {\n");
        htmlContent.append("    border-collapse: collapse;\n");
        htmlContent.append("    width: 100%;\n");
        htmlContent.append("    margin-bottom: 12pt;\n");
        htmlContent.append("}\n");
        
        htmlContent.append("td, th {\n");
        htmlContent.append("    border: 1px solid #000000;\n");
        htmlContent.append("    padding: 4pt 6pt;\n");
        htmlContent.append("    vertical-align: top;\n");
        htmlContent.append("    word-wrap: break-word;\n");
        htmlContent.append("}\n");
        
        htmlContent.append("th {\n");
        htmlContent.append("    font-weight: bold;\n");
        htmlContent.append("    background-color: #f0f0f0;\n");
        htmlContent.append("}\n");
        
        htmlContent.append("</style>\n");
        htmlContent.append("</head>\n");
        htmlContent.append("<body>\n");
    }
    
    /**
     * 处理文档内容
     */
    private void processDocument(XWPFDocument document) {
        List<IBodyElement> bodyElements = document.getBodyElements();
        
        for (IBodyElement element : bodyElements) {
            try {
                if (element instanceof XWPFParagraph) {
                    processParagraph((XWPFParagraph) element);
                } else if (element instanceof XWPFTable) {
                    processTable((XWPFTable) element);
                }
            } catch (Exception e) {
                System.err.println("处理元素时出错: " + e.getMessage());
            }
        }
    }
    
    /**
     * 处理段落
     */
    private void processParagraph(XWPFParagraph paragraph) {
        paragraphCounter++;
        
        // 获取段落样式
        String paragraphStyle = getParagraphStyle(paragraph);
        
        htmlContent.append("<p");
        if (!paragraphStyle.isEmpty()) {
            htmlContent.append(" style=\"").append(paragraphStyle).append("\"");
        }
        htmlContent.append(">");
        
        // 处理段落中的运行文本
        List<XWPFRun> runs = paragraph.getRuns();
        if (runs.isEmpty()) {
            htmlContent.append("&nbsp;");
        } else {
            for (XWPFRun run : runs) {
                processRun(run);
            }
        }
        
        htmlContent.append("</p>\n");
    }
    
    /**
     * 获取段落样式
     */
    private String getParagraphStyle(XWPFParagraph paragraph) {
        StringBuilder style = new StringBuilder();
        
        try {
            // 对齐方式
            ParagraphAlignment alignment = paragraph.getAlignment();
            if (alignment != null) {
                switch (alignment) {
                    case CENTER:
                        style.append("text-align: center; ");
                        break;
                    case RIGHT:
                        style.append("text-align: right; ");
                        break;
                    case BOTH:
                        style.append("text-align: justify; ");
                        break;
                    default:
                        style.append("text-align: left; ");
                        break;
                }
            }
            
            // 首行缩进
            int firstLineIndent = paragraph.getFirstLineIndent();
            if (firstLineIndent > 0) {
                style.append("text-indent: ").append(firstLineIndent / 20).append("pt; ");
            }
            
        } catch (Exception e) {
            // 忽略异常
        }
        
        return style.toString();
    }
    
    /**
     * 处理运行文本
     */
    private void processRun(XWPFRun run) {
        String text = run.getText(0);
        if (text == null) {
            return;
        }
        
        // 获取运行样式
        String runStyle = getRunStyle(run);
        
        if (!runStyle.isEmpty()) {
            htmlContent.append("<span style=\"").append(runStyle).append("\">");
        }
        
        // 转义HTML特殊字符
        text = escapeHtml(text);
        htmlContent.append(text);
        
        if (!runStyle.isEmpty()) {
            htmlContent.append("</span>");
        }
    }
    
    /**
     * 获取运行样式
     */
    private String getRunStyle(XWPFRun run) {
        StringBuilder style = new StringBuilder();
        
        try {
            // 字体
            String fontFamily = run.getFontFamily();
            if (fontFamily != null && !fontFamily.isEmpty()) {
                style.append("font-family: '").append(fontFamily).append("'; ");
            }
            
            // 字体大小
            int fontSize = run.getFontSize();
            if (fontSize > 0) {
                style.append("font-size: ").append(fontSize).append("pt; ");
            }
            
            // 粗体
            if (run.isBold()) {
                style.append("font-weight: bold; ");
            }
            
            // 斜体
            if (run.isItalic()) {
                style.append("font-style: italic; ");
            }
            
            // 下划线
            UnderlinePatterns underline = run.getUnderline();
            if (underline != null && underline != UnderlinePatterns.NONE) {
                style.append("text-decoration: underline; ");
            }
            
            // 字体颜色
            String color = run.getColor();
            if (color != null && !color.equals("auto") && !color.isEmpty() && color.length() == 6) {
                style.append("color: #").append(color.toUpperCase()).append("; ");
            }
            
        } catch (Exception e) {
            // 忽略异常
        }
        
        return style.toString();
    }
    
    /**
     * 处理表格
     */
    private void processTable(XWPFTable table) {
        tableCounter++;
        
        htmlContent.append("<table>\n");
        
        // 处理表格行
        List<XWPFTableRow> rows = table.getRows();
        for (int i = 0; i < rows.size(); i++) {
            processTableRow(rows.get(i), i == 0);
        }
        
        htmlContent.append("</table>\n");
    }
    
    /**
     * 处理表格行
     */
    private void processTableRow(XWPFTableRow row, boolean isHeader) {
        htmlContent.append("<tr>\n");
        
        List<XWPFTableCell> cells = row.getTableCells();
        for (XWPFTableCell cell : cells) {
            processTableCell(cell, isHeader);
        }
        
        htmlContent.append("</tr>\n");
    }
    
    /**
     * 处理表格单元格
     */
    private void processTableCell(XWPFTableCell cell, boolean isHeader) {
        String tag = isHeader ? "th" : "td";
        
        htmlContent.append("<").append(tag).append(">");
        
        // 处理单元格内容
        List<XWPFParagraph> paragraphs = cell.getParagraphs();
        for (int i = 0; i < paragraphs.size(); i++) {
            if (i > 0) {
                htmlContent.append("<br>");
            }
            processCellParagraph(paragraphs.get(i));
        }
        
        htmlContent.append("</").append(tag).append(">\n");
    }
    
    /**
     * 处理单元格内的段落
     */
    private void processCellParagraph(XWPFParagraph paragraph) {
        // 处理段落中的运行文本
        List<XWPFRun> runs = paragraph.getRuns();
        if (runs.isEmpty()) {
            htmlContent.append("&nbsp;");
        } else {
            for (XWPFRun run : runs) {
                processRun(run);
            }
        }
    }
    
    /**
     * 结束HTML文档
     */
    private void endHtmlDocument() {
        htmlContent.append("</body>\n</html>");
    }
    
    /**
     * 写入文件
     */
    private void writeToFile(String outputPath) throws IOException {
        try (OutputStreamWriter writer = new OutputStreamWriter(
                new FileOutputStream(outputPath), "UTF-8")) {
            writer.write(htmlContent.toString());
        }
    }
    
    /**
     * 转义HTML特殊字符
     */
    private String escapeHtml(String text) {
        if (text == null) {
            return "";
        }
        
        return text.replace("&", "&amp;")
                  .replace("<", "&lt;")
                  .replace(">", "&gt;")
                  .replace("\"", "&quot;")
                  .replace("'", "&#39;")
                  .replace("\n", "<br>")
                  .replace("\r", "")
                  .replace("\t", "&nbsp;&nbsp;&nbsp;&nbsp;");
    }
    
    /**
     * 主方法 - 用于测试
     */
    public static void main(String[] args) {
        SimpleCompleteWordToHtmlConverter converter = new SimpleCompleteWordToHtmlConverter();
        
        // 测试文件路径
        String scriptsPath = "/Users/<USER>/IdeaProjects/JoyCreator/joycreator-ae/src/main/resources/scripts/";
        
        // 测试所有Word文档
        String[] testFiles = {
            "京东大模型服务协议-客户模版20240927-JD法审 1106.docx",
            "微电云技术服务协议（中酿）-10.28修改 (003) (004)10.31.docx",
            "【样例】技术服务协议模板（jd为销售方通用版）-v1 -清洁版.docx"
        };
        
        for (String fileName : testFiles) {
            try {
                String inputPath = scriptsPath + fileName;
                String outputPath = scriptsPath + "simple_complete_" + fileName.replace(".docx", ".html");
                
                System.out.println("开始转换: " + fileName);
                converter.convertWordToHtml(inputPath, outputPath);
                System.out.println("转换完成: " + outputPath);
                System.out.println("---");
                
                // 重置转换器状态以处理下一个文件
                converter = new SimpleCompleteWordToHtmlConverter();
                
            } catch (Exception e) {
                System.err.println("转换失败: " + fileName);
                e.printStackTrace();
                System.out.println("---");
            }
        }
        
        System.out.println("所有文件转换完成！");
    }
}
