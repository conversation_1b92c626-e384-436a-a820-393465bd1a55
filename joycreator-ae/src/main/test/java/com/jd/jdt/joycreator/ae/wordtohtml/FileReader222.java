package com.jd.jdt.joycreator.ae.wordtohtml;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.Path;

public class FileReader222 {

    public static void main(String[] args) {
        // 源文件路径
        String sourceFilePath = "joycreator-ae/src/main/test/java/com/jd/jdt/joycreator/ae/wordtohtml/aaaa.txt";
        // 目标文件路径（当前目录）
        String targetFilePath = "aaaa.txt";
        
        try {
            // 读取源文件内容
            Path sourcePath = Paths.get(sourceFilePath);
            byte[] content = Files.readAllBytes(sourcePath);
            
            // 将内容写入目标文件
            Path targetPath = Paths.get(targetFilePath);
            Files.write(targetPath, content);
            
            System.out.println("文件读取并写入成功！");
            System.out.println("源文件: " + sourcePath.toAbsolutePath());
            System.out.println("目标文件: " + targetPath.toAbsolutePath());
            
        } catch (IOException e) {
            System.err.println("文件操作失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
