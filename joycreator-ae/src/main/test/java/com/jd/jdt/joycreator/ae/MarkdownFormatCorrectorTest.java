package com.jd.jdt.joycreator.ae;

import com.jd.jdt.joycreator.ae.utils.MarkdownFormatCorrector;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

/**
 * <p>
 * Markdown格式纠正工具类测试
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
public class MarkdownFormatCorrectorTest {

    private MarkdownFormatCorrector markdownFormatCorrector;


    @Before
    public void setUp() {
        markdownFormatCorrector = new MarkdownFormatCorrector();
    }

    @Test
    public void testCorrectMarkdownFormat() {
        // 测试标题格式纠正
        String input1 = "###1.1 标题内容";
        String expected1 = "### 1.1 标题内容";
        Assert.assertEquals(expected1, markdownFormatCorrector.correctMarkdownFormat(input1));

        // 测试列表格式纠正
        String input2 = "***1.1.1 列表内容";
        String expected2 = "*** 1.1.1 列表内容";
        Assert.assertEquals(expected2, markdownFormatCorrector.correctMarkdownFormat(input2));

        // 测试多行内容
        String input3 = "##2.1 标题内容\n***3.1.1 列表内容";
        String expected3 = "## 2.1 标题内容\n*** 3.1.1 列表内容";
        Assert.assertEquals(expected3, markdownFormatCorrector.correctMarkdownFormat(input3));
    }

    @Test
    public void testStreamingCorrection() {
        // 测试流式纠正 - 分块输入
        String chunk1 = "##";
        String chunk2 = "2.1 标题内容";
        
        // 第一块不应该有变化，因为它只是一个不完整的标记
        String result1 = markdownFormatCorrector.correctStreamingMarkdown(chunk1);
        Assert.assertEquals(chunk1, result1);
        
        // 第二块应该被纠正，因为现在上下文缓冲区中有完整的标记
        String result2 = markdownFormatCorrector.correctStreamingMarkdown(chunk2);
        Assert.assertEquals("2.1 标题内容", result2);
        
        // 最终结果应该是正确格式的标题
        String finalResult = markdownFormatCorrector.finalizeBuffer();
        Assert.assertEquals("## 2.1 标题内容", finalResult);
    }

    @Test
    public void testComplexStreamingCorrection() {
        // 测试更复杂的流式场景
        String[] chunks = {
            "# 文档标题\n\n##",
            "1.1 第一部分\n\n这是一些内容\n\n###",
            "1.1.1 子部分\n\n* 列表项1\n* 列表项2\n***",
            "1.1.2 特殊列表项"
        };
        
        StringBuilder expectedFull = new StringBuilder();
        
        for (String chunk : chunks) {
            markdownFormatCorrector.correctStreamingMarkdown(chunk);
        }
        
        String finalResult = markdownFormatCorrector.finalizeBuffer();
        String expected = "# 文档标题\n\n## 1.1 第一部分\n\n这是一些内容\n\n### 1.1.1 子部分\n\n* 列表项1\n* 列表项2\n*** 1.1.2 特殊列表项";
        
        Assert.assertEquals(expected, finalResult);
    }
    
    @Test
    public void testEmptyInput() {
        Assert.assertEquals("", markdownFormatCorrector.correctMarkdownFormat(""));
        Assert.assertEquals(null, markdownFormatCorrector.correctMarkdownFormat(null));
    }
}
