package com.jd.jdt.joycreator.ae;

import com.jd.jdt.joycreator.ae.dao.plus.IPromptWordsConfigMapper;
import com.jd.jdt.joycreator.ae.entity.PromptWordsConfig;
import com.jd.jdt.joycreator.ae.enums.PromptCodeEnum;
import com.jd.jdt.joycreator.ae.pojo.dto.FileOriginDTO;
import com.jd.jdt.joycreator.ae.pojo.dto.UploadFileDTO;
import com.jd.jdt.joycreator.ae.service.FileService;
import com.jd.jsf.gd.util.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.*;

@Log4j2
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class PanDocTest {

    @Autowired
    private FileService fileService;
    @Autowired
    private IPromptWordsConfigMapper iPromptWordsConfigMapper;


    @Test
    public void videosResult2() {
        String filePath = "/Users/<USER>/IdeaProjects/JoyCreator/joycreator-ae/src/main/resources/销售合同md模板.md"; // 文件的路径
        try (InputStream inputStream = new FileInputStream(filePath)) {
            // 读取文件内容的示例代码
            UploadFileDTO uploadFileDTO = fileService.uploadFile("prompt/其它/销售合同md模板.md", inputStream, "application/md");
            log.info("uploadFileDTO:{}", uploadFileDTO.getFileWanUrl());
        } catch (IOException e) {
            e.printStackTrace();
        }
        FileOriginDTO fileOriginDTO = new FileOriginDTO();
        fileOriginDTO.setOriginName("prompt/其它/销售合同md模板.md");

    }

    @Test
    public void videosResult3() {
        String srr = "\n" +
                "\n" +
                "以下是一份卖方视角的销售合同模板（含格式示例），由于篇幅限制，此处提供框架结构和关键条款示例。实际使用时可通过扩展条款细节、增加附件内容达到5000字要求：\n" +
                "\n" +
                "---\n" +
                "\n" +
                "**销售合同**  \n" +
                "合同编号：_____________  \n" +
                "签订地点：_____________  \n" +
                "签订日期：_____________  \n" +
                "\n" +
                "---\n" +
                "\n" +
                "**甲方（卖方）：**  \n" +
                "名称：_________________________  \n" +
                "地址：_________________________  \n" +
                "法定代表人：___________________  \n" +
                "联系方式：_____________________  \n" +
                "\n" +
                "**乙方（买方）：**  \n" +
                "名称：_________________________  \n" +
                "地址：_________________________  \n" +
                "法定代表人：___________________  \n" +
                "联系方式：_____________________  \n" +
                "\n" +
                "---\n" +
                "\n" +
                "### 第一条 合同标的  \n" +
                "1.1 卖方同意出售，买方同意购买以下货物：  \n" +
                "| 序号 | 产品名称   | 规格型号    | 单位 | 数量 | 单价（元） | 总价（元） |  \n" +
                "|------|------------|-------------|------|------|------------|------------|  \n" +
                "| 1    | ________   | ____________ | ____ | ____ | __________ | __________ |  \n" +
                "| 2    | ________   | ____________ | ____ | ____ | __________ | __________ |  \n" +
                "\n" +
                "（注：可通过扩展表格行数增加篇幅）\n" +
                "\n" +
                "---\n" +
                "\n" +
                "### 第二条 价格与支付方式  \n" +
                "2.1 合同总金额：人民币_________元（大写：_________________________）。  \n" +
                "2.2 付款方式：  \n" +
                "□ 预付款：合同签订后___日内支付总价款的___%  \n" +
                "□ 进度款：货物交付后___日内支付总价款的___%  \n" +
                "□ 质保金：验收合格后___日内支付剩余___%  \n" +
                "\n" +
                "---\n" +
                "\n" +
                "### 第三条 交货与验收  \n" +
                "3.1 交货时间：_______年___月___日前  \n" +
                "3.2 交货地点：_________________________  \n" +
                "3.3 运输方式：_________________________（运费由___方承担）  \n" +
                "3.4 验收标准：  \n" +
                "（以下条款需详细展开，可增加技术参数表格）  \n" +
                "- 外观质量：无破损、无污染  \n" +
                "- 性能指标：符合国家________标准（GB/T______）  \n" +
                "- 文件要求：提供产品合格证、检测报告  \n" +
                "\n" +
                "---\n" +
                "\n" +
                "### 第四条 质量保证  \n" +
                "4.1 质保期：自验收合格之日起___个月  \n" +
                "4.2 卖方责任范围：  \n" +
                "□ 免费更换瑕疵部件  \n" +
                "□ 承担维修人工费用  \n" +
                "□ 不承担间接损失（如停产损失）  \n" +
                "\n" +
                "---\n" +
                "\n" +
                "### 第五条 违约责任  \n" +
                "5.1 买方逾期付款：每日按未付金额___%支付违约金  \n" +
                "5.2 卖方逾期交货：每日按合同总额___%支付违约金  \n" +
                "5.3 单方解除合同：违约方需赔偿守约方直接损失的___%  \n" +
                "\n" +
                "---\n" +
                "\n" +
                "### 第六条 不可抗力  \n" +
                "6.1 定义：战争、地震、政府行为等无法预见的情形  \n" +
                "6.2 受影响方需在___日内书面通知对方  \n" +
                "6.3 处理方式：协商延期履行或部分解除合同  \n" +
                "\n" +
                "---\n" +
                "\n" +
                "### 第七条 争议解决  \n" +
                "7.1 协商不成时，提交_________仲裁委员会仲裁  \n" +
                "7.2 或：向卖方所在地人民法院提起诉讼  \n" +
                "\n" +
                "---\n" +
                "\n" +
                "### 第八条 其他条款  \n" +
                "8.1 知识产权：产品商标、专利归属卖方所有  \n" +
                "8.2 保密义务：双方不得泄露商业机密（可附保密协议附件）  \n" +
                "8.3 合同生效：双方签字盖章后生效，一式___份  \n" +
                "\n" +
                "---\n" +
                "\n" +
                "**附件清单**  \n" +
                "1. 《技术规格要求》  \n" +
                "2. 《验收标准细则》  \n" +
                "3. 《售后服务承诺书》  \n" +
                "\n" +
                "---\n" +
                "\n" +
                "**甲方（盖章）：**　　　　　　　**乙方（盖章）：**  \n" +
                "法定代表人（签字）：_________　　法定代表人（签字）：_________  \n" +
                "日期：_______年___月___日　　　日期：_______年___月___日  \n" +
                "\n" +
                "---\n" +
                "\n" +
                "### 扩展内容建议（用于增加篇幅）：\n" +
                "1. **技术附件**：用表格列明各项技术参数（可占2-3页）  \n" +
                "2. **售后服务细则**：详细描述响应时间、服务流程  \n" +
                "3. **法律声明**：增加数据安全、反商业贿赂等条款  \n" +
                "4. **履约担保**：增加银行保函、保证金等要求  \n" +
                "5. **风险转移条款**：明确货物损毁灭失的责任划分  \n" +
                "\n" +
                "*注：实际使用时需根据《中华人民共和国民法典》及相关司法解释调整条款，建议委托专业律师审核。*";
        byte[] bytes = srr.getBytes();
        // 字节数组包装为InputStream流
        InputStream inputStream = new ByteArrayInputStream(bytes);
        // 读取文件内容的示例代码
        UploadFileDTO uploadFileDTO = fileService.uploadFile("销售合同md模板1.md", inputStream, "application/md");
        log.info("uploadFileDTO:{}", uploadFileDTO.getFileWanUrl());
    }

    public static void main(String[] args) {

        // 指定Markdown文件的路径
        String filePath = "/Users/<USER>/IdeaProjects/JoyCreator/joycreator-ae/src/main/resources/销售合同md模板.md";

        try (InputStream inputStream = new FileInputStream(filePath);
             ByteArrayOutputStream wordOutputStream = new ByteArrayOutputStream()) {

            // 创建Pandoc进程
            ProcessBuilder processBuilder = new ProcessBuilder("pandoc", "-f", "markdown", "-t", "docx");
            Process process = processBuilder.start();

            // 将Markdown内容从InputStream写入Pandoc的标准输入
            try (OutputStream processInput = process.getOutputStream()) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    processInput.write(buffer, 0, bytesRead);
                }
            }

            // 从Pandoc的标准输出读取生成的Word文档
            try (InputStream processOutput = process.getInputStream()) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = processOutput.read(buffer)) != -1) {
                    wordOutputStream.write(buffer, 0, bytesRead);
                }
            }

            // 获取Word文档的字节数组
            byte[] wordDocument = wordOutputStream.toByteArray();

            // 可以将字节数组保存到文件或其他地方
            try (FileOutputStream fileOutputStream = new FileOutputStream("output.docx")) {
                fileOutputStream.write(wordDocument);
            }

            // 检查Pandoc进程的退出状态
            int exitCode = process.waitFor();
            if (exitCode != 0) {
                System.err.println("Pandoc conversion failed with exit code " + exitCode);
            }
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }
    }


    @Test
    public void videosResult4() {
        PromptWordsConfig promptWordsConfigByCode = iPromptWordsConfigMapper.getPromptWordsConfigByCode(PromptCodeEnum.CHAT_INTENT_RECOGNITION);
        System.err.println(JsonUtils.toJSONString(promptWordsConfigByCode));
    }
}
