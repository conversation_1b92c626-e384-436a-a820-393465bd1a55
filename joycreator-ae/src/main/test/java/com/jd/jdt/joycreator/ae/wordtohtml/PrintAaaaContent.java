package com.jd.jdt.joycreator.ae.wordtohtml;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.Path;

public class PrintAaaaContent {
    public static void main(String[] args) {
        // 文件路径
        String filePath = "/Users/<USER>/IdeaProjects/JoyCreator2/joycreator-ae/src/main/test/java/com/jd/jdt/joycreator/ae/wordtohtml/aaaa.txt";
        
        try {
            // 读取文件内容
            Path path = Paths.get(filePath);
            byte[] bytes = Files.readAllBytes(path);
            String content = new String(bytes);
            
            // 打印文件内容
//            System.out.println("aaaa.txt 文件内容：");
//            System.out.println("======================");
            System.out.println(content);
//            System.out.println("======================");
            
        } catch (IOException e) {
            System.err.println("文件读取失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
