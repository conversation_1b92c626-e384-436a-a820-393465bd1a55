package com.jd.jdt.joycreator.ae;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jd.jdt.joycreator.ae.entity.PromptWordsConfig;
import com.jd.jdt.joycreator.ae.enums.PromptCodeEnum;
import com.jd.jdt.joycreator.ae.pojo.dto.ChatCompletionChunkDTO;
import com.jd.jdt.joycreator.ae.pojo.dto.ChatCompletionResponseDTO;
import com.jd.jdt.joycreator.ae.pojo.dto.DocumentReplaceDTO;
import com.jd.jdt.joycreator.ae.pojo.dto.EmbeddingResponseDTO;
import com.jd.jdt.joycreator.ae.pojo.vo.EditDocumentVO;
import com.jd.jdt.joycreator.ae.rpc.feign.LLMGatewayService;
import com.jd.jdt.joycreator.ae.service.EditDocumentService;
import com.jd.jdt.joycreator.ae.service.EditService;
import com.jd.jdt.joycreator.ae.service.PromptWordsConfigService;
import com.jd.jdt.joycreator.ae.utils.ContentAnchorAutoCompleteUtil;
import com.jd.jdt.joycreator.ae.utils.JoyCreatorUtil;
import com.jd.jdt.joycreator.ae.utils.SimilarTextExtractorUtil;
import com.jd.jsf.gd.util.JsonUtils;
import feign.Response;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

import static cn.hutool.poi.excel.sax.AttributeName.s;


@Log4j2
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class LLMGatewayServiceTest {

    @Autowired
    private LLMGatewayService llmGatewayService;
    @Autowired
    private PromptWordsConfigService promptWordsConfigService;
    @Autowired
    private EditService editService;
    @Autowired
    private EditDocumentService editDocumentService;
    @Autowired
    private JoyCreatorUtil joyCreatorUtil;

    @Test
    public void videosResult2() {
        Map<String, Object> pa = Maps.newHashMap();
        pa.put("onKeyword", Boolean.FALSE);
        pa.put("prompts", "我是卖方立场，帮我找几份销售合同条款");
        try {
            Response response = llmGatewayService.videosResult2("sso.jd.com=BJ.1C897E5F9D209643FD7D2337CC6CF0D7.0720250408100313", pa);
            BufferedReader reader = new BufferedReader(new InputStreamReader(response.body().asInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                if (StringUtils.isEmpty(line)) {
                    continue;
                }
                System.err.println("Received event: " + line);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testEee2() {
        Map<String, Object> params = Maps.newHashMap();
        params.put("stream", Boolean.TRUE);
        params.put("model", "gpt-4o-0806");
        List messages = Lists.newArrayList();
        Map map = Maps.newHashMap();
//        map.put("role", "user");
        map.put("role", "system");
        map.put("content", "帮我写一份租赁合同");
        messages.add(map);
        params.put("messages", messages);
        try {
            Response response = llmGatewayService.chatCompletionsStream("aa051d32-db0b-4294-98a0-b02040240d48", params);
            BufferedReader reader = new BufferedReader(new InputStreamReader(response.body().asInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                if (StringUtils.isEmpty(line)) {
                    continue;
                } else if (line.contains("data: [DONE]")) {
                    log.info("Received event，任务结束：{}", line);
                    continue;
                } else if (line.startsWith("data: ")) {
                    line = line.substring(6);
                }
                ChatCompletionChunkDTO chatCompletionChunkDTO = JsonUtils.parseObject(line, ChatCompletionChunkDTO.class);
                log.info("Received event，chatCompletionChunkDTO：{}", JsonUtils.toJSONString(chatCompletionChunkDTO));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Test
    public void testEee3() {
        Map<String, Object> params = Maps.newHashMap();
        params.put("model", "gpt-4o-0806");
        List messages = Lists.newArrayList();
        Map map = Maps.newHashMap();
//        map.put("role", "user");
        map.put("role", "user");
        map.put("content", "帮我写一份租赁合同");
        messages.add(map);
        params.put("messages", messages);
        log.info("Received event，params：{}", JsonUtils.toJSONString(params));
        ChatCompletionResponseDTO chatCompletionResponseDTO = llmGatewayService.chatCompletions("aa051d32-db0b-4294-98a0-b02040240d48", params);
        log.info("Received event，chatCompletionResponseDTO：{}", JsonUtils.toJSONString(chatCompletionResponseDTO));
    }


    @Test
    public void test5() {
        Map<String, Object> params = Maps.newHashMap();
//        params.put("model", "Chatrhino-81B-Pro");
        params.put("model", "text-embedding-ada-002-2");
        params.put("erp", "zhaohan25");
        params.put("input", "合同");
        log.info("Received event，params：{}", JsonUtils.toJSONString(params));
        String requestId = UUID.randomUUID().toString();
        EmbeddingResponseDTO embeddingResponseDTO = llmGatewayService.embeddings("aa051d32-db0b-4294-98a0-b02040240d48", requestId, params);
        System.err.println(JsonUtils.toJSONString(embeddingResponseDTO));
    }


    @Test
    public void test6() {
        EditDocumentVO editDocumentVO = editDocumentService.dtl("LS2025070200003");
        String bestAnchorContent = ContentAnchorAutoCompleteUtil.findBestAnchorContent(editDocumentVO.getTextContent(), "合同签署后【个工作日");
        System.err.println(bestAnchorContent);
//        String tuserSemantic = "合同签署后【】个工作日内填充为10个工作日";
//        PromptWordsConfig promptWordsConfig = promptWordsConfigService.buildPromptWordsConfig(PromptCodeEnum.DOCUMENT_REPLACE, tuserSemantic, editDocumentVO.getTextContent());
//        ChatCompletionResponseDTO chatCompletionResponseDTO = editService.chatCompletions(promptWordsConfig.getPromptWord(), promptWordsConfig.getModel());
//        String content = chatCompletionResponseDTO.getChoices().get(0).getMessage().getContent();
//        DocumentReplaceDTO documentReplaceDTO = joyCreatorUtil.parseDocumentReplaceJson(content);
//        if (Objects.isNull(documentReplaceDTO)) {
//            System.err.println("识别异常: " + content);
//        }
//        System.out.println(JsonUtils.toJSONString(documentReplaceDTO));
//        if (!editDocumentVO.getTextContent().contains(documentReplaceDTO.getOldContent())) {
//            String bestAnchorContent = ContentAnchorAutoCompleteUtil.findBestAnchorContent(editDocumentVO.getTextContent(), documentReplaceDTO.getOldContent());
//            System.err.println("识别错误: " + documentReplaceDTO.getOldContent());
//            System.err.println("识别错误: " + s);
//            if (editDocumentVO.getTextContent().contains(bestAnchorContent)) {
//                System.out.println("识别错误,但是纠正过来了: " + bestAnchorContent);
//            }else {
//                System.err.println("识别错误,没有纠正过来: " + bestAnchorContent);
//            }
//        }else {
//            System.out.println("识别正确: " + JsonUtils.toJSONString(documentReplaceDTO));
//        }
    }

}