package com.jd.jdt.joycreator.ae;

import com.jd.jdt.joycreator.ae.utils.FuzzyTextMatcherUtils;

import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;

public class FuzzyTextMatcherTest {
    public static void main(String[] args) throws Exception {
        String basePath = "/Users/<USER>/IdeaProjects/JoyCreator2/joycreator-ae/src/main/resources/prompt/意图识别与工具调用提示词/originalText/";
        String original = new String(Files.readAllBytes(Paths.get(basePath + "原文.txt")), StandardCharsets.UTF_8);
        String target = new String(Files.readAllBytes(Paths.get(basePath + "模型抽取到的原文片段.txt")), StandardCharsets.UTF_8);
        long start = System.currentTimeMillis();
        FuzzyTextMatcherUtils.MatchResult result = FuzzyTextMatcherUtils.findBestMatch(original, target);
        long cost = System.currentTimeMillis() - start;
        // 获取默认配置参数
        FuzzyTextMatcherUtils.FuzzyMatchConfig config = new FuzzyTextMatcherUtils.FuzzyMatchConfig();
        StringBuilder sb = new StringBuilder();
        sb.append("==== FuzzyTextMatcher 自动测试 ====").append(System.lineSeparator());
        sb.append("最大连续不一致: ").append(config.getMaxContinuousMismatch()).append(System.lineSeparator());
        sb.append("最大总不一致: ").append(config.getMaxTotalMismatch()).append(System.lineSeparator());
        sb.append("是否忽略符号: ").append(config.isIgnoreSymbols()).append(System.lineSeparator());
        sb.append("是否区分大小写: ").append(config.isCaseSensitive()).append(System.lineSeparator());
        sb.append("线程数: ").append(config.getThreadCount()).append(System.lineSeparator());
        if (result != null) {
            sb.append("\n==== 匹配结果 ====").append(System.lineSeparator());
            sb.append("原文起始下标: ").append(result.startIdx).append(System.lineSeparator());
            sb.append("原文结束下标: ").append(result.endIdx).append(System.lineSeparator());
            sb.append("总不一致数: ").append(result.totalMismatch).append(System.lineSeparator());
            sb.append("最大连续不一致: ").append(result.maxContinuousMismatch).append(System.lineSeparator());
            sb.append("耗时: ").append(result.costMillis).append(" ms").append(System.lineSeparator());
            sb.append("\n==== 匹配片段 ====").append(System.lineSeparator());
            sb.append(result.matchedText).append(System.lineSeparator());
        } else {
            sb.append("\n未找到满足条件的匹配片段！").append(System.lineSeparator());
        }
        System.err.println(sb);
        Files.write(Paths.get(basePath + "我想要根据原文与模型抽取到的原文片段通过算法得出的结果.txt"), sb.toString().getBytes(StandardCharsets.UTF_8));
        System.out.println("测试完成，结果已写入目标文件。总耗时: " + cost + " ms");

        // ==== 内存测试用例，直接打印到chat区域 ====
        System.out.println("\n==== 内存边界测试 ====");
        String[] originals = {
            "这里有十万字.这里有很多字】，这里有一百万字",
            "abc...这里有很多字】xyz",
            "【【这里有很多字】",
            "。这里有很多字】",
            "这里有很多字】",
            "这里有很多字】\n\n",
            "这里有很多字】。\n\n",
            "这里有很多字】。",
            "这里有很多字】。\n",
            "这里有很多字】。\n\n",
        };
        String[] targets = {
            ".这里有很多字】",
            "...这里有很多字】",
            "【这里有很多字】",
            "。这里有很多字】",
            "这里有很多字】",
            "这里有很多字】。",
            "这里有很多字】。",
            "这里有很多字】。",
            "这里有很多字】。",
            "这里有很多字】。",
        };
        for (int i = 0; i < originals.length; i++) {
            String o = originals[i];
            String t = targets[i];
            FuzzyTextMatcherUtils.MatchResult r = FuzzyTextMatcherUtils.findBestMatch(o, t);
            System.out.println("原文: [" + o + "]");
            System.out.println("模型片段: [" + t + "]");
            System.out.println("算法输出: [" + (r != null ? r.matchedText : "无结果") + "]");
            System.out.println("---");
        }

        // ==== 文件物料自动验证（与人工理想输出对比） ====
        System.out.println("\n==== 文件物料自动验证（与人工理想输出对比） ====");
        String wantOutput = new String(Files.readAllBytes(Paths.get(basePath + "我想要根据原文与模型抽取到的原文片段通过算法得出的结果.txt")), StandardCharsets.UTF_8).replaceAll("\r\n", "\n").trim();
        FuzzyTextMatcherUtils.MatchResult algoResult = FuzzyTextMatcherUtils.findBestMatch(original, target);
        String algoOutput = algoResult.matchedText.replaceAll("\r\n", "\n").trim();
        String[] wantLines = wantOutput.split("\n");
        String[] algoLines = algoOutput.split("\n");
        boolean allMatch = true;
        int max = Math.max(wantLines.length, algoLines.length);
        for (int i = 0; i < max; i++) {
            String wantLine = i < wantLines.length ? wantLines[i].trim() : "<缺失>";
            String algoLine = i < algoLines.length ? algoLines[i].trim() : "<缺失>";
            if (!wantLine.equals(algoLine)) {
                allMatch = false;
                System.out.println("第" + (i+1) + "行不一致：");
                System.out.println("人工理想输出: [" + wantLine + "]");
                System.out.println("算法输出:   [" + algoLine + "]");
            }
        }
        if (allMatch) {
            System.out.println("算法输出与人工理想输出完全一致，验证通过！");
        } else {
            System.out.println("算法输出与人工理想输出存在差异，验证未通过！");
        }
    }
} 