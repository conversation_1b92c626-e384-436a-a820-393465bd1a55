package com.jd.jdt.joycreator.ae;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.Map;

public class PandocExecutor {
    public static void main(String[] args) {
        String sourceType = "markdown";
        String targetType = "pdf";

        ProcessBuilder processBuilder = new ProcessBuilder(
                "pandoc",
                "-f", sourceType,
                "-t", targetType,
                "-o", "-", // 指定输出到标准输出
                "--pdf-engine=xelatex",
                "--variable", "mainfont=PingFang SC"
        );

        // 设置环境变量
//        Map<String, String> env = processBuilder.environment();
//        env.put("PATH", "/usr/local/texlive/2023/bin/x86_64-darwin:" + env.get("PATH")); // 确保路径正确

        try {
            Process process = processBuilder.start();

            // 向 Pandoc 进程的标准输入写入 Markdown 内容
            try (OutputStream stdin = process.getOutputStream()) {
                String markdownContent = "# 这是一个标题\n\n这是一些内容。\n\n- 列表项1\n- 列表项2";
                stdin.write(markdownContent.getBytes(StandardCharsets.UTF_8));
                stdin.flush();
            }

            // 从 Pandoc 进程的标准输出读取 PDF 数据
            ByteArrayOutputStream pdfOutputStream = new ByteArrayOutputStream();
            try (InputStream stdout = process.getInputStream()) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = stdout.read(buffer)) != -1) {
                    pdfOutputStream.write(buffer, 0, bytesRead);
                }
            }

            // 检查 PDF 数据长度
            byte[] pdfData = pdfOutputStream.toByteArray();
            System.out.println("PDF data length: " + pdfData.length);

            // 将 PDF 数据写入文件以检查其完整性
            if (pdfData.length > 0) {
                java.nio.file.Files.write(java.nio.file.Paths.get("output.pdf"), pdfData);
            } else {
                System.err.println("No PDF data was generated.");
            }

            // 读取错误输出
            try (InputStream errorStream = process.getErrorStream()) {
                byte[] errorBuffer = new byte[1024];
                int errorBytesRead;
                while ((errorBytesRead = errorStream.read(errorBuffer)) != -1) {
                    System.err.write(errorBuffer, 0, errorBytesRead);
                }
            }

            int exitCode = process.waitFor();
            System.out.println("Process exited with code: " + exitCode);

        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }
    }
}