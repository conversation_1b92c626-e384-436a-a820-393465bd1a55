#!/bin/bash
JAR_NAME="${scripts_projectName}"
SHDIR=$(cd "$(dirname "$0")"; pwd)
BASEDIR=$(cd $SHDIR/..; pwd)
APP_NAME=${BASEDIR}/${JAR_NAME}
echo "AP_NAME = $APP_NAME"

is_exist() {
  pid=$(ps -ef | grep $APP_NAME | awk '{print $2}')
  if [ -z "${pid}" ]; then
    return 1
  else
    return 0
  fi
}

is_exist
if [ $? -eq "0" ]; then
  kill $pid
else
  echo "${APP_NAME} is not running"
fi
echo "PID: $pid"
echo "OK!"
