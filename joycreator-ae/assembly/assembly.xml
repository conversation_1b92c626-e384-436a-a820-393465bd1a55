<?xml version='1.0' encoding='UTF-8'?>
<assembly>
    <!--打包名称，唯一标识-->
    <id>assembly</id>
    <!--打包格式，可以手动修改-->
    <formats>
        <format>zip</format>
    </formats>
    <includeBaseDirectory>false</includeBaseDirectory>
    <!--文件设置-->
    <fileSets>
        <fileSet>
            <directory>${project.basedir}/bin</directory>
            <outputDirectory>bin</outputDirectory>
            <!-- 文件文件权限为777 -->
            <fileMode>777</fileMode>
            <!-- 目录权限为777  -->
            <directoryMode>777</directoryMode>
            <!--脚本中参数变量为pom中的值 关键-->
            <filtered>true</filtered>
            <!--脚本中换行符使用 linux 换行符-->
            <lineEnding>unix</lineEnding>
        </fileSet>
        <fileSet>
            <directory>${project.basedir}/src/main/resources</directory>
            <outputDirectory>config</outputDirectory>
            <filtered>true</filtered>
            <includes>
                <include>/**</include>
            </includes>
            <!-- 不压缩编译文件类型 关键-->
            <nonFilteredFileExtensions>
                <nonFilteredFileExtension>ttf</nonFilteredFileExtension>
                <nonFilteredFileExtension>woff</nonFilteredFileExtension>
                <nonFilteredFileExtension>TTF</nonFilteredFileExtension>
                <nonFilteredFileExtension>png</nonFilteredFileExtension>
                <nonFilteredFileExtension>jpg</nonFilteredFileExtension>
            </nonFilteredFileExtensions>
        </fileSet>
    </fileSets>
  <files>
        <file>
            <source>${project.build.directory}/${project.build.finalName}.jar</source>
        </file>
    </files>

    <dependencySets>
        <dependencySet>
            <outputDirectory>lib</outputDirectory>
            <excludes>
                <exclude>junit:junit</exclude>
            </excludes>
        </dependencySet>
    </dependencySets>
</assembly>